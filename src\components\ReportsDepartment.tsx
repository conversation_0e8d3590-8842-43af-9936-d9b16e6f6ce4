import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Download, 
  Filter, 
  Calendar,
  BarChart3,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  FileSpreadsheet,
  File
} from "lucide-react";
import DateRangeFilter from "./DateRangeFilter";

const ReportsDepartment = () => {
  const [dateRange, setDateRange] = useState("today");
  const [selectedDepartment, setSelectedDepartment] = useState("all");
  const [selectedRegion, setSelectedRegion] = useState("kolhapur");

  const departments = [
    { id: "all", name: "All Departments" },
    { id: "quality", name: "Quality Control" },
    { id: "sales", name: "Sales & Distribution" },
    { id: "production", name: "Production" },
    { id: "energy", name: "Energy Management" },
    { id: "hr", name: "Human Resources" }
  ];

  const regions = [
    { id: "kolhapur", name: "Kolhapur HQ" },
    { id: "pune", name: "Pune Region" },
    { id: "mumbai", name: "Mumbai Region" },
    { id: "nashik", name: "Nashik Region" },
    { id: "nagpur", name: "Nagpur Region" }
  ];

  const reportCategories = [
    {
      title: "Quality Reports",
      icon: <CheckCircle className="h-6 w-6 text-green-600" />,
      reports: [
        { name: "Daily Quality Summary", lastGenerated: "2 hours ago", status: "ready" },
        { name: "Product-wise Defect Analysis", lastGenerated: "1 day ago", status: "ready" },
        { name: "Lab Test Results", lastGenerated: "30 minutes ago", status: "ready" },
        { name: "Packaging Line Performance", lastGenerated: "4 hours ago", status: "processing" }
      ]
    },
    {
      title: "Sales & Distribution",
      icon: <TrendingUp className="h-6 w-6 text-blue-600" />,
      reports: [
        { name: "Regional Sales Performance", lastGenerated: "1 hour ago", status: "ready" },
        { name: "Route Optimization Analysis", lastGenerated: "6 hours ago", status: "ready" },
        { name: "Product Demand Forecast", lastGenerated: "12 hours ago", status: "ready" },
        { name: "Distribution Efficiency", lastGenerated: "2 hours ago", status: "ready" }
      ]
    },
    {
      title: "Production Analytics",
      icon: <BarChart3 className="h-6 w-6 text-purple-600" />,
      reports: [
        { name: "Daily Production Summary", lastGenerated: "30 minutes ago", status: "ready" },
        { name: "Equipment Utilization", lastGenerated: "2 hours ago", status: "ready" },
        { name: "Shift Performance Analysis", lastGenerated: "8 hours ago", status: "ready" },
        { name: "Raw Material Consumption", lastGenerated: "4 hours ago", status: "ready" }
      ]
    },
    {
      title: "Energy & Sustainability",
      icon: <AlertTriangle className="h-6 w-6 text-orange-600" />,
      reports: [
        { name: "Solar Plant Performance", lastGenerated: "1 hour ago", status: "ready" },
        { name: "Cold Storage Efficiency", lastGenerated: "3 hours ago", status: "ready" },
        { name: "Energy Cost Analysis", lastGenerated: "1 day ago", status: "ready" },
        { name: "Sustainability Metrics", lastGenerated: "12 hours ago", status: "ready" }
      ]
    }
  ];

  const handleExport = (format: string, reportName: string) => {
    console.log(`Exporting ${reportName} as ${format}`);
    // Simulate export functionality
    alert(`Exporting ${reportName} as ${format.toUpperCase()} file...`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Filters Section */}
      <Card className="border-blue-200 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100">
          <CardTitle className="flex items-center space-x-2 text-blue-800">
            <Filter className="h-5 w-5" />
            <span>Report Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">Date Range</label>
              <DateRangeFilter value={dateRange} onChange={setDateRange} />
            </div>

            {/* Department Filter */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">Department</label>
              <select 
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {departments.map(dept => (
                  <option key={dept.id} value={dept.id}>{dept.name}</option>
                ))}
              </select>
            </div>

            {/* Region Filter */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">Region</label>
              <select 
                value={selectedRegion}
                onChange={(e) => setSelectedRegion(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {regions.map(region => (
                  <option key={region.id} value={region.id}>{region.name}</option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Categories */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {reportCategories.map((category, index) => (
          <Card key={index} className="border-gray-200 shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100">
              <CardTitle className="flex items-center space-x-3">
                {category.icon}
                <span className="text-gray-800">{category.title}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {category.reports.map((report, reportIndex) => (
                  <div key={reportIndex} className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{report.name}</h4>
                      <p className="text-sm text-gray-600">Last generated: {report.lastGenerated}</p>
                      <Badge className={`mt-2 ${getStatusColor(report.status)}`}>
                        {report.status}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleExport('pdf', report.name)}
                        className="hover:bg-red-50"
                        disabled={report.status !== 'ready'}
                      >
                        <File className="h-4 w-4 text-red-600 mr-1" />
                        PDF
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleExport('csv', report.name)}
                        className="hover:bg-green-50"
                        disabled={report.status !== 'ready'}
                      >
                        <FileSpreadsheet className="h-4 w-4 text-green-600 mr-1" />
                        CSV
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card className="border-orange-200 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100">
          <CardTitle className="text-orange-800">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button 
              className="bg-blue-600 hover:bg-blue-700 text-white h-16"
              onClick={() => console.log('Generate custom report')}
            >
              <FileText className="h-6 w-6 mb-2" />
              <span>Custom Report</span>
            </Button>
            <Button 
              variant="outline" 
              className="border-green-300 hover:bg-green-50 h-16"
              onClick={() => console.log('Schedule report')}
            >
              <Calendar className="h-6 w-6 mb-2 text-green-600" />
              <span>Schedule Report</span>
            </Button>
            <Button 
              variant="outline" 
              className="border-purple-300 hover:bg-purple-50 h-16"
              onClick={() => console.log('Export dashboard')}
            >
              <Download className="h-6 w-6 mb-2 text-purple-600" />
              <span>Export Dashboard</span>
            </Button>
            <Button 
              variant="outline" 
              className="border-orange-300 hover:bg-orange-50 h-16"
              onClick={() => console.log('Report settings')}
            >
              <BarChart3 className="h-6 w-6 mb-2 text-orange-600" />
              <span>Settings</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsDepartment;

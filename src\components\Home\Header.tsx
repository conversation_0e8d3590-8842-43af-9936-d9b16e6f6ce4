import { <PERSON><PERSON> } from "@/components/ui/button";
import { Phone, Mail, Brain, Sparkles } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Header = () => {
    const router=useNavigate()
  return (
    <header className="bg-slate-900/95 border-b border-white/10 sticky top-0 z-50 backdrop-blur-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand with AI Theme */}
          <div className="flex items-center space-x-3">
            <div className="relative w-12 h-12 rounded-full flex items-center justify-center">
              <img src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png" alt="Gokul Dairy Logo" className="h-12 w-12 object-contain" />
              <div className="absolute -top-1 -right-1">
                <Brain className="h-4 w-4 text-blue-400 animate-pulse" />
              </div>
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">गोकुळ दूध डेयरी</h1>
                <Sparkles className="h-4 w-4 text-yellow-400 animate-pulse" />
              </div>
              <p className="text-xs text-blue-200">AI-Powered Dairy Management</p>
            </div>
          </div>

          {/* AI-Themed Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#home" className="text-white hover:text-blue-300 transition-colors font-medium">Home</a>
            <a href="#about" className="text-white hover:text-blue-300 transition-colors font-medium">AI Technology</a>
            <a href="#products" className="text-white hover:text-blue-300 transition-colors font-medium">Solutions</a>
            <a href="#quality" className="text-white hover:text-blue-300 transition-colors font-medium">Analytics</a>
            <a href="#contact" className="text-white hover:text-blue-300 transition-colors font-medium">Contact</a>
          </nav>

          {/* AI-Powered Login */}
          <div className="flex items-center space-x-4">
            <div className="hidden lg:flex items-center space-x-2 text-sm text-blue-200 backdrop-blur-sm bg-white/5 px-3 py-1 rounded-full border border-white/10">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>AI Systems Online</span>
            </div>
            <Button
              onClick={()=>router("/login")}
              size="sm"
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold px-6 py-2 rounded-full transform transition-all duration-300 hover:scale-105 shadow-lg"
            >
              <Brain className="w-4 h-4 mr-2" />
              Access AI Dashboard
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
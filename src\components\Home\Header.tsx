import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const Header = () => {
    const router=useNavigate()
  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-full flex items-center justify-center">
              <img src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png" alt="Gokul Dairy Logo" className="h-12 w-12 object-contain" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-800">गोकुळ दूध डेयरी</h1>
              <p className="text-xs text-gray-600">Dairy Management System</p>
            </div>
          </div>

          {/* Clean Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#home" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">Home</a>
            <a href="#about" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">About</a>
            <a href="#products" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">Solutions</a>
            <a href="#quality" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">Quality</a>
            <a href="#contact" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">Contact</a>
          </nav>

          {/* Professional Login */}
          <div className="flex items-center space-x-4">
            <div className="hidden lg:flex items-center space-x-2 text-sm text-gray-600">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Professional Service</span>
            </div>
            <Button
              onClick={()=>router("/login")}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2"
            >
              Login
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
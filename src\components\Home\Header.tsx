import { But<PERSON> } from "@/components/ui/button";
import { Phone, Mail } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Header = () => {
    const router=useNavigate()
  return (
    <header className="bg-background border-b border-border sticky top-0 z-50 backdrop-blur-sm bg-background/95">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-full flex items-center justify-center">
                <img src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png" alt="Gokul Dairy Logo" className="h-12 w-12 object-contain" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-primary">गोकुल दूध डेयरी</h1>
              <p className="text-xs text-gray-900"><PERSON><PERSON><PERSON></p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#home" className="text-foreground hover:text-primary transition-colors">Home</a>
            <a href="#about" className="text-foreground hover:text-primary transition-colors">About</a>
            <a href="#products" className="text-foreground hover:text-primary transition-colors">Products</a>
            <a href="#quality" className="text-foreground hover:text-primary transition-colors">Quality</a>
            <a href="#contact" className="text-foreground hover:text-primary transition-colors">Contact</a>
          </nav>

          {/* Contact Info */}
          <div className="flex items-center space-x-4">
            {/* <div className="hidden lg:flex items-center space-x-2 text-sm text-gray-900">
              <Phone className="w-4 h-4" />
              <span>+91 98765 43210</span>
            </div> */}
            <Button onClick={()=>router("/login")} size="sm">
              Login
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Factory,
  Milk,
  Package,
  Truck,
  Zap,
  FileText,
  Activity,
  Settings,
  Brain,
  MapPin,
  Building2,
  Users,
  Target,
  BarChart3,
  Heart,
  Droplets
} from "lucide-react";
import DateRangeFilter from "@/components/DateRangeFilter";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut, Pie } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface MainDashboardProps {
  onDepartmentClick?: (departmentId: string) => void;
}

const MainDashboard = ({ onDepartmentClick }: MainDashboardProps) => {
  const [dateRange, setDateRange] = useState("today");
  const [selectedLevel, setSelectedLevel] = useState<'district' | 'taluka' | 'village' | 'dcs'>('district');
  const [selectedTaluka, setSelectedTaluka] = useState<number | null>(null);
  const [selectedVillage, setSelectedVillage] = useState<string | null>(null);

  const [liveData, setLiveData] = useState({
    production: 16.2,
    quality: 96.8,
    energy: 87.2,
    dispatch: 94.5
  });

  // Hierarchical Data Structure: District → Taluka → Village → DCS
  const hierarchicalData = {
    district: {
      name: "Kolhapur District",
      totalProduction: 240000,
      totalAnimals: 234000,
      healthIndex: 92.3,
      efficiency: 99.0,
      wastage: 2400,
      resourceUtilization: 94.5,
      activeDCS: 847,
      totalTalukas: 12,
      totalVillages: 1204
    },
    talukas: [
      {
        id: 1,
        name: "Kolhapur",
        villages: 156,
        dcsCount: 89,
        production: 28500,
        animals: 28500,
        healthIndex: 94.2,
        efficiency: 99.0,
        wastage: 285,
        resourceUtilization: 96.1,
        performance: "excellent",
        villages_data: [
          {
            name: "Rankala",
            dcs: 3,
            production: 1200,
            animals: 1200,
            farmers: 45,
            healthIndex: 95.5,
            efficiency: 99.0,
            wastage: 12,
            resourceUtilization: 97.2,
            performance: "excellent"
          },
          {
            name: "Mahalaxmi",
            dcs: 2,
            production: 890,
            animals: 890,
            farmers: 32,
            healthIndex: 91.2,
            efficiency: 98.0,
            wastage: 18,
            resourceUtilization: 94.8,
            performance: "good"
          },
          {
            name: "Shivaji Nagar",
            dcs: 4,
            production: 1560,
            animals: 1560,
            farmers: 67,
            healthIndex: 93.8,
            efficiency: 98.0,
            wastage: 31,
            resourceUtilization: 95.5,
            performance: "good"
          }
        ]
      },
      {
        id: 2,
        name: "Panhala",
        villages: 98,
        dcsCount: 67,
        production: 22300,
        animals: 22300,
        healthIndex: 89.7,
        efficiency: 98.5,
        wastage: 334,
        resourceUtilization: 92.3,
        performance: "good",
        villages_data: [
          {
            name: "Panhala Fort",
            dcs: 2,
            production: 980,
            animals: 980,
            farmers: 28,
            healthIndex: 87.5,
            efficiency: 97.0,
            wastage: 29,
            resourceUtilization: 89.2,
            performance: "needs_attention"
          }
        ]
      },
      {
        id: 3,
        name: "Shirol",
        villages: 87,
        dcsCount: 72,
        production: 19800,
        animals: 19800,
        healthIndex: 85.2,
        efficiency: 97.5,
        wastage: 495,
        resourceUtilization: 88.7,
        performance: "needs_attention",
        villages_data: [
          {
            name: "Shirol",
            dcs: 4,
            production: 1680,
            animals: 1680,
            farmers: 58,
            healthIndex: 82.1,
            efficiency: 96.0,
            wastage: 67,
            resourceUtilization: 85.3,
            performance: "critical"
          }
        ]
      }
    ]
  };

  // Comprehensive KPI Metrics with Hierarchical Data
  const kpiMetrics = [
    {
      title: "District Production",
      value: `${(hierarchicalData.district.totalProduction / 1000).toFixed(0)}K Litres`,
      target: "245K",
      progress: Math.round((hierarchicalData.district.totalProduction / 245000) * 100),
      trend: "+2.3%",
      status: "good",
      icon: <Milk className="h-6 w-6 text-blue-600" />,
      insight: "Production across all talukas showing consistent growth",
      uplift: 2.3,
      tooltip: "District-wide production from 847 DCS centers across 12 talukas",
      hierarchical: {
        level: "District",
        breakdown: `${hierarchicalData.talukas.length} Talukas, ${hierarchicalData.district.totalVillages} Villages`
      }
    },
    {
      title: "Animal Health Index",
      value: `${hierarchicalData.district.healthIndex}%`,
      target: "95%",
      progress: Math.round((hierarchicalData.district.healthIndex / 95) * 100),
      trend: "+0.5%",
      status: "good",
      icon: <Heart className="h-6 w-6 text-green-600" />,
      insight: "District-wide animal health monitoring across all DCS centers",
      uplift: 0.5,
      tooltip: "Health index aggregated from all villages and DCS centers",
      hierarchical: {
        level: "District",
        breakdown: `${hierarchicalData.district.totalAnimals.toLocaleString()} Animals Monitored`
      }
    },
    {
      title: "Operational Efficiency",
      value: `${hierarchicalData.district.efficiency}%`,
      target: "99.5%",
      progress: Math.round((hierarchicalData.district.efficiency / 99.5) * 100),
      trend: "+0.2%",
      status: "excellent",
      icon: <Target className="h-6 w-6 text-purple-600" />,
      insight: "Optimized operations across all hierarchical levels",
      uplift: 0.2,
      tooltip: "Efficiency measured across District → Taluka → Village → DCS levels",
      hierarchical: {
        level: "Multi-Level",
        breakdown: `${hierarchicalData.district.activeDCS} DCS Centers Active`
      }
    },
    {
      title: "Resource Utilization",
      value: `${hierarchicalData.district.resourceUtilization}%`,
      target: "96%",
      progress: Math.round((hierarchicalData.district.resourceUtilization / 96) * 100),
      trend: "****%",
      status: "good",
      icon: <BarChart3 className="h-6 w-6 text-blue-600" />,
      insight: "Optimal resource allocation across all operational levels",
      uplift: 1.3,
      tooltip: "Resource utilization tracking from village to district level",
      hierarchical: {
        level: "Hierarchical",
        breakdown: "Village → Taluka → District Optimization"
      }
    }
  ];

  const criticalAlerts = [
    {
      id: 1,
      type: "critical",
      category: "major",
      title: "Cold Storage B Temperature Alert",
      description: "Temperature reading: 6.2°C (Target: 4-5°C)",
      time: "2 minutes ago",
      department: "Energy Management",
      location: "Cold Storage Unit B, Building A",
      aiAnalysis: "Compressor efficiency down 15%, potential refrigerant leak detected",
      recommendedAction: "Immediate technician dispatch required, check refrigerant levels",
      impact: "High - Product quality at risk"
    },
    {
      id: 2,
      type: "warning",
      category: "medium", 
      title: "Packaging Line 3 Seal Defects",
      description: "Seal defect rate: 2.1% (Target: <1%)",
      time: "8 minutes ago",
      department: "Quality Control",
      location: "Production Floor B, Line 3",
      aiAnalysis: "Sealing temperature variance detected, pressure inconsistency noted",
      recommendedAction: "Adjust sealing parameters, calibrate pressure settings",
      impact: "Medium - Increased waste, quality concerns"
    },
    {
      id: 3,
      type: "info",
      category: "minor",
      title: "Festival Season Demand Forecast",
      description: "Expected 15% increase in ghee demand for Diwali",
      time: "15 minutes ago",
      department: "Sales",
      location: "Planning Office",
      aiAnalysis: "Historical data shows consistent 12-18% spike during festival periods",
      recommendedAction: "Increase ghee production by 20%, prepare additional packaging",
      impact: "Opportunity - Revenue increase potential"
    }
  ];

  const departmentCards = [
    { 
      id: "quality", 
      name: "Quality Control", 
      icon: Package, 
      alerts: 2, 
      status: "warning", 
      metrics: "96.8%",
      description: "AI quality monitoring"
    },
    { 
      id: "production", 
      name: "Production", 
      icon: Factory, 
      alerts: 0, 
      status: "good", 
      metrics: "16.2K L",
      description: "Real-time production tracking"
    },
    { 
      id: "processing", 
      name: "Processing", 
      icon: Settings, 
      alerts: 1, 
      status: "info", 
      metrics: "Active",
      description: "Predictive maintenance"
    },
    { 
      id: "dispatch", 
      name: "Dispatch & Cold Chain", 
      icon: Truck, 
      alerts: 1, 
      status: "info", 
      metrics: "94.5%",
      description: "Route optimization"
    },
    { 
      id: "energy", 
      name: "Energy Management", 
      icon: Zap, 
      alerts: 1, 
      status: "warning", 
      metrics: "87.2%",
      description: "Solar & grid optimization"
    },
    { 
      id: "reports", 
      name: "Reports", 
      icon: FileText, 
      alerts: 0, 
      status: "good", 
      metrics: "24 Reports",
      description: "Analytics & insights"
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical': return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'info': return <Clock className="h-5 w-5 text-blue-600" />;
      default: return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'major': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-white';
      case 'minor': return 'bg-blue-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const handleDepartmentClick = (departmentId: string) => {
    if (onDepartmentClick) {
      onDepartmentClick(departmentId);
    }
  };

  const handleViewAllAlerts = () => {
    if (onDepartmentClick) {
      onDepartmentClick('all-alerts');
    }
  };

  // Chart Data
  const productionTrendData = {
    labels: ['6 AM', '8 AM', '10 AM', '12 PM', '2 PM', '4 PM', '6 PM', '8 PM'],
    datasets: [
      {
        label: 'Milk Production (Litres)',
        data: [1200, 1800, 2100, 2400, 2200, 1900, 1600, 1300],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Target Production',
        data: [1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderDash: [5, 5],
        tension: 0.4,
      }
    ],
  };

  const qualityMetricsData = {
    labels: ['Fat Content', 'Protein', 'Lactose', 'Solids', 'pH Level'],
    datasets: [
      {
        label: 'Current (%)',
        data: [4.2, 3.8, 4.9, 12.5, 6.7],
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(168, 85, 247, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
        borderColor: [
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
          'rgb(168, 85, 247)',
          'rgb(245, 158, 11)',
          'rgb(239, 68, 68)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const departmentPerformanceData = {
    labels: ['Production', 'Quality', 'Processing', 'Dispatch', 'Energy', 'Maintenance'],
    datasets: [
      {
        label: 'Efficiency (%)',
        data: [94, 97, 89, 92, 87, 85],
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
      },
      {
        label: 'Target (%)',
        data: [95, 95, 90, 95, 90, 88],
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 2,
      },
    ],
  };

  const energyConsumptionData = {
    labels: ['Cooling', 'Processing', 'Packaging', 'Lighting', 'Others'],
    datasets: [
      {
        data: [35, 25, 20, 12, 8],
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(168, 85, 247, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(34, 197, 94)',
          'rgb(245, 158, 11)',
          'rgb(168, 85, 247)',
          'rgb(239, 68, 68)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: false,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header with Hierarchical Structure and Real-time Status */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-orange-500 bg-clip-text text-transparent">
            गोकुळ दूध डेयरी - Hierarchical Operations Center
          </h2>
          <p className="text-gray-600 mt-1">District → Taluka → Village → DCS • Comprehensive KPI Tracking</p>

          {/* Hierarchical Breadcrumb */}
          <div className="flex items-center space-x-2 mt-2 text-sm">
            <Building2 className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-600">Kolhapur District</span>
            {selectedTaluka && (
              <>
                <span className="text-gray-400">→</span>
                <MapPin className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-600">
                  {hierarchicalData.talukas.find(t => t.id === selectedTaluka)?.name} Taluka
                </span>
              </>
            )}
            {selectedVillage && (
              <>
                <span className="text-gray-400">→</span>
                <Users className="h-4 w-4 text-purple-600" />
                <span className="font-medium text-purple-600">{selectedVillage} Village</span>
              </>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 bg-green-50 px-3 py-2 rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-green-700">Live Hierarchical Data</span>
          </div>
          <div className="flex items-center space-x-2 bg-blue-50 px-3 py-2 rounded-lg">
            <BarChart3 className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">
              {hierarchicalData.district.activeDCS} DCS Active
            </span>
          </div>
          <DateRangeFilter value={dateRange} onChange={setDateRange} />
        </div>
      </div>

      {/* Hierarchical Data Flow Visualization */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-green-50 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <MapPin className="h-6 w-6 text-blue-600" />
            Hierarchical Data Structure: District → Taluka → Village → DCS
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* District Level */}
            <div className="text-center p-4 bg-white rounded-lg border-2 border-blue-200">
              <div className="flex items-center justify-center mb-3">
                <Building2 className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-blue-600">1</div>
              <div className="text-sm font-medium text-gray-700">District</div>
              <div className="text-xs text-gray-500 mt-1">Kolhapur</div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Production:</span>
                  <span className="font-medium">{(hierarchicalData.district.totalProduction / 1000).toFixed(0)}K L</span>
                </div>
                <div className="flex justify-between">
                  <span>Animals:</span>
                  <span className="font-medium">{(hierarchicalData.district.totalAnimals / 1000).toFixed(0)}K</span>
                </div>
              </div>
            </div>

            {/* Taluka Level */}
            <div className="text-center p-4 bg-white rounded-lg border-2 border-green-200">
              <div className="flex items-center justify-center mb-3">
                <MapPin className="w-8 h-8 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-600">{hierarchicalData.district.totalTalukas}</div>
              <div className="text-sm font-medium text-gray-700">Talukas</div>
              <div className="text-xs text-gray-500 mt-1">Regional Hubs</div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Avg Efficiency:</span>
                  <span className="font-medium">98.3%</span>
                </div>
                <div className="flex justify-between">
                  <span>Health Index:</span>
                  <span className="font-medium">89.7%</span>
                </div>
              </div>
            </div>

            {/* Village Level */}
            <div className="text-center p-4 bg-white rounded-lg border-2 border-purple-200">
              <div className="flex items-center justify-center mb-3">
                <Users className="w-8 h-8 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-purple-600">{hierarchicalData.district.totalVillages}</div>
              <div className="text-sm font-medium text-gray-700">Villages</div>
              <div className="text-xs text-gray-500 mt-1">Base Operations</div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Avg Production:</span>
                  <span className="font-medium">199L</span>
                </div>
                <div className="flex justify-between">
                  <span>Farmers:</span>
                  <span className="font-medium">~45</span>
                </div>
              </div>
            </div>

            {/* DCS Level */}
            <div className="text-center p-4 bg-white rounded-lg border-2 border-orange-200">
              <div className="flex items-center justify-center mb-3">
                <Droplets className="w-8 h-8 text-orange-600" />
              </div>
              <div className="text-2xl font-bold text-orange-600">{hierarchicalData.district.activeDCS}</div>
              <div className="text-sm font-medium text-gray-700">DCS Centers</div>
              <div className="text-xs text-gray-500 mt-1">Collection Points</div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Avg Collection:</span>
                  <span className="font-medium">283L</span>
                </div>
                <div className="flex justify-between">
                  <span>Utilization:</span>
                  <span className="font-medium">94.5%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Flow Visualization */}
          <div className="mt-6 flex items-center justify-center">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-gray-600">District</span>
              </div>
              <div className="w-8 h-0.5 bg-gradient-to-r from-blue-600 to-green-600"></div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                <span className="text-sm text-gray-600">Taluka</span>
              </div>
              <div className="w-8 h-0.5 bg-gradient-to-r from-green-600 to-purple-600"></div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-purple-600 rounded-full"></div>
                <span className="text-sm text-gray-600">Village</span>
              </div>
              <div className="w-8 h-0.5 bg-gradient-to-r from-purple-600 to-orange-600"></div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-orange-600 rounded-full"></div>
                <span className="text-sm text-gray-600">DCS</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Simplified KPI Metrics */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Key Performance Indicators</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {kpiMetrics.map((metric, index) => (
              <Card 
                key={index} 
                className="cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-0 shadow-lg group bg-white"
                title={metric.tooltip}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-blue-50 to-blue-100">
                      {metric.icon}
                    </div>
                    <div className="flex items-center space-x-1">
                      {metric.trend.startsWith('+') ? 
                        <TrendingUp className="h-4 w-4 text-green-600" /> : 
                        <TrendingDown className="h-4 w-4 text-red-600" />
                      }
                      <span className={`text-sm font-bold ${
                        metric.trend.startsWith('+') ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {metric.trend}
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between items-end">
                      <div>
                        <p className="text-sm text-gray-600 font-medium">{metric.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                      </div>
                      <Badge className={`${getStatusColor(metric.status)} border`}>
                        {metric.status.toUpperCase()}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Progress to Target</span>
                        <span>{metric.target}</span>
                      </div>
                      <div className="relative">
                        <Progress value={metric.progress} className="h-3" />
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent to-blue-200 opacity-50 rounded-full animate-pulse"></div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-lg border border-blue-100">
                      <div className="flex items-start space-x-2">
                        <Activity className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <p className="text-xs text-gray-700">{metric.insight}</p>
                          <div className="mt-2 flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <span className="text-xs font-medium text-gray-600">
                                {Math.abs(metric.uplift)}% {metric.uplift > 0 ? 'improvement' : 'decline'}
                              </span>
                              <div className={`w-2 h-2 rounded-full ${
                                metric.uplift > 0 ? 'bg-green-500' : 'bg-red-500'
                              }`}></div>
                            </div>
                            {(metric as any).hierarchical && (
                              <div className="text-xs text-blue-600 font-medium">
                                {(metric as any).hierarchical.level}
                              </div>
                            )}
                          </div>
                          {(metric as any).hierarchical && (
                            <div className="mt-1 text-xs text-gray-500">
                              {(metric as any).hierarchical.breakdown}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Simplified Department Quick Access */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <Factory className="h-5 w-5" />
            <span>Department Quick Access</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {departmentCards.map((dept, index) => (
              <div 
                key={index} 
                className="group p-4 border-2 border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-lg cursor-pointer transition-all duration-300 transform hover:-translate-y-1 bg-white"
                onClick={() => handleDepartmentClick(dept.id)}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="p-3 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 group-hover:from-blue-100 group-hover:to-blue-200 transition-all">
                    <dept.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  {dept.alerts > 0 && (
                    <Badge className="bg-red-500 text-white text-xs h-6 w-6 p-0 flex items-center justify-center">
                      {dept.alerts}
                    </Badge>
                  )}
                </div>
                <h3 className="font-semibold text-gray-900 text-sm mb-2 group-hover:text-blue-700 transition-colors">
                  {dept.name}
                </h3>
                <div className="space-y-2">
                  <div className="text-lg font-bold text-blue-600">{dept.metrics}</div>
                  <p className="text-xs text-gray-500">{dept.description}</p>
                  <Badge className={`${getStatusColor(dept.status)} text-xs border`}>
                    {dept.status.toUpperCase()}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Production Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Production Trend Chart */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span>Production Trend (Today)</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={productionTrendData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Quality Metrics Chart */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Quality Metrics</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Bar data={qualityMetricsData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Department Performance & Energy Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Department Performance Chart */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Factory className="h-5 w-5 text-purple-600" />
              <span>Department Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Bar data={departmentPerformanceData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Energy Consumption Chart */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-yellow-600" />
              <span>Energy Consumption Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Doughnut data={energyConsumptionData} options={pieChartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Critical Alerts */}
      <Card className="border-red-200 bg-gradient-to-r from-red-50 to-orange-50 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-red-800">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-6 w-6" />
              <span>Critical Alerts & AI Analysis</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-red-500 text-white">{criticalAlerts.length} Active</Badge>
              <Button 
                size="sm" 
                className="bg-red-600 hover:bg-red-700 text-white"
                onClick={handleViewAllAlerts}
              >
                View All Alerts
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {criticalAlerts.map((alert) => (
              <div 
                key={alert.id} 
                className="p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-start space-x-4 flex-1">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-semibold text-gray-900">{alert.title}</h4>
                        <Badge className={getCategoryColor(alert.category)}>
                          {alert.category.toUpperCase()}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{alert.description}</p>
                      <div className="space-y-2">
                        <div className="text-xs text-gray-700 bg-blue-50 p-2 rounded">
                          <strong>AI Analysis:</strong> {alert.aiAnalysis}
                        </div>
                        <div className="text-xs text-gray-700 bg-green-50 p-2 rounded">
                          <strong>Recommended Action:</strong> {alert.recommendedAction}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-2 space-y-1">
                        <div>📍 {alert.location}</div>
                        <div>💼 {alert.department} • {alert.time}</div>
                        <div>📊 {alert.impact}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MainDashboard;

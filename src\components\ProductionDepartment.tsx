import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Factory, 
  Clock, 
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Settings,
  BarChart3,
  Brain,
  Lightbulb
} from "lucide-react";
import DateRangeFilter from "@/components/DateRangeFilter";

const ProductionDepartment = () => {
  const [dateRange, setDateRange] = useState("today");
  const [selectedLine, setSelectedLine] = useState("all");

  const productionLines = [
    {
      id: "line1",
      name: "Milk Processing Line 1",
      product: "Milk",
      status: "running",
      currentOutput: 850,
      targetOutput: 900,
      efficiency: 94.4,
      downtime: 0.5,
      lastMaintenance: "2 days ago",
      nextMaintenance: "5 days"
    },
    {
      id: "line2", 
      name: "Ghee Production Line",
      product: "<PERSON>hee",
      status: "running",
      currentOutput: 125,
      targetOutput: 130,
      efficiency: 96.2,
      downtime: 0,
      lastMaintenance: "1 week ago",
      nextMaintenance: "2 days"
    },
    {
      id: "line3",
      name: "Paneer Processing Line",
      product: "Paneer",
      status: "maintenance",
      currentOutput: 0,
      targetOutput: 180,
      efficiency: 0,
      downtime: 2.5,
      lastMaintenance: "now",
      nextMaintenance: "3 hours"
    },
    {
      id: "line4",
      name: "Curd Production Line",
      product: "Curd",
      status: "running",
      currentOutput: 420,
      targetOutput: 450,
      efficiency: 93.3,
      downtime: 0.2,
      lastMaintenance: "3 days ago",
      nextMaintenance: "4 days"
    }
  ];

  const shiftData = [
    {
      shift: "Morning (6 AM - 2 PM)",
      production: 8500,
      target: 9000,
      efficiency: 94.4,
      workers: 45,
      issues: 2
    },
    {
      shift: "Evening (2 PM - 10 PM)",
      production: 7800,
      target: 8500,
      efficiency: 91.8,
      workers: 42,
      issues: 1
    },
    {
      shift: "Night (10 PM - 6 AM)",
      production: 6200,
      target: 7000,
      efficiency: 88.6,
      workers: 38,
      issues: 3
    }
  ];

  const rawMaterialInventory = [
    {
      material: "Raw Milk",
      current: 45000,
      required: 50000,
      unit: "litres",
      supplier: "Local Farmers",
      lastUpdated: "1 hour ago",
      status: "low"
    },
    {
      material: "Packaging Material",
      current: 8500,
      required: 10000,
      unit: "units",
      supplier: "Pack Solutions",
      lastUpdated: "2 hours ago",
      status: "medium"
    },
    {
      material: "Preservatives",
      current: 850,
      required: 500,
      unit: "kg",
      supplier: "Chem Industries",
      lastUpdated: "30 minutes ago",
      status: "good"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'stopped': return 'bg-red-100 text-red-800';
      case 'good': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Clock className="h-4 w-4 text-green-600" />;
      case 'maintenance': return <Settings className="h-4 w-4 text-yellow-600" />;
      case 'stopped': return <Clock className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const aiInsights = [
    {
      title: "Smart Maintenance Prediction",
      description: "Pasteurizer pump on Line 1 shows early wear patterns. Maintenance recommended in next 48 hours to prevent breakdown",
      impact: "High",
      action: "Schedule maintenance"
    },
    {
      title: "Production Optimization",
      description: "Smart analysis suggests 8% efficiency improvement possible by adjusting Line 2 timing during morning shift",
      impact: "Medium", 
      action: "Adjust shift timing"
    },
    {
      title: "Inventory Management",
      description: "Smart forecasting predicts raw milk shortage in 3 days based on collection patterns and production demand",
      impact: "High",
      action: "Increase collection routes"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Production Dashboard</h2>
          <p className="text-sm text-gray-600">Real-time production monitoring and control</p>
        </div>
        <div className="flex items-center space-x-4">
          <select 
            value={selectedLine} 
            onChange={(e) => setSelectedLine(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="all">All Production Lines</option>
            {productionLines.map(line => (
              <option key={line.id} value={line.id}>{line.name}</option>
            ))}
          </select>
          <DateRangeFilter value={dateRange} onChange={setDateRange} />
        </div>
      </div>

      {/* AI Insights Section for Production Department */}
      <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <span>Production Smart Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {aiInsights.map((insight, index) => (
              <div key={index} className="bg-white p-4 rounded-lg border border-purple-100">
                <div className="flex items-start space-x-3">
                  <Lightbulb className="h-5 w-5 text-yellow-500 mt-1" />
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-2">{insight.title}</h4>
                    <p className="text-sm text-gray-700 mb-3">{insight.description}</p>
                    <div className="flex items-center justify-between">
                      <Badge className={insight.impact === 'High' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}>
                        {insight.impact} Impact
                      </Badge>
                      <Button size="sm" variant="outline" className="text-xs">
                        {insight.action}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Production Lines Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Factory className="h-5 w-5 text-sky-600" />
            <span>Production Lines Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {productionLines
              .filter(line => selectedLine === 'all' || line.id === selectedLine)
              .map((line, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-sky-300 transition-colors">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-sky-100 p-2 rounded-lg">
                      <Factory className="h-6 w-6 text-sky-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{line.name}</h3>
                      <p className="text-sm text-gray-600">Product: {line.product}</p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(line.status)}>
                    {line.status}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Current Output</p>
                    <p className="text-xl font-bold text-gray-900">{line.currentOutput}</p>
                    <p className="text-xs text-gray-500">L/hour</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Target Output</p>
                    <p className="text-xl font-bold text-gray-900">{line.targetOutput}</p>
                    <p className="text-xs text-gray-500">L/hour</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Efficiency</p>
                    <p className="text-xl font-bold text-gray-900">{line.efficiency}%</p>
                    <div className="flex items-center justify-center mt-1">
                      {line.efficiency > 95 ? 
                        <TrendingUp className="h-3 w-3 text-green-600" /> : 
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      }
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Downtime</p>
                    <p className="text-xl font-bold text-gray-900">{line.downtime}h</p>
                    <p className="text-xs text-gray-500">today</p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Progress value={(line.currentOutput / line.targetOutput) * 100} className="w-48 h-2" />
                    <p className="text-xs text-gray-500">
                      Last maintenance: {line.lastMaintenance} | Next: {line.nextMaintenance}
                    </p>
                  </div>
                  <Badge className={line.status === 'running' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {line.status === 'running' ? 'Operational' : 'Under Maintenance'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Shift-wise Production */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-sky-600" />
            <span>Shift-wise Production Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {shiftData.map((shift, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">{shift.shift}</h3>
                  {shift.issues > 0 && (
                    <Badge className="bg-red-500 text-white">
                      {shift.issues} issues
                    </Badge>
                  )}
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-600">Production</span>
                      <span className="text-sm font-medium">
                        {shift.production}L / {shift.target}L
                      </span>
                    </div>
                    <Progress value={(shift.production / shift.target) * 100} className="h-2" />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs text-gray-600">Efficiency</p>
                      <p className="text-lg font-semibold text-gray-900">{shift.efficiency}%</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-600">Workers</p>
                      <p className="text-lg font-semibold text-gray-900">{shift.workers}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Raw Material Inventory */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-sky-600" />
            <span>Raw Material Inventory & Smart Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {rawMaterialInventory.map((material, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="bg-sky-100 p-3 rounded-lg">
                    <BarChart3 className="h-6 w-6 text-sky-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{material.material}</h4>
                    <p className="text-sm text-gray-600">Supplier: {material.supplier}</p>
                    <p className="text-xs text-gray-500">Updated: {material.lastUpdated}</p>
                  </div>
                </div>

                <div className="text-center">
                  <p className="text-sm text-gray-600">Current Stock</p>
                  <p className="text-xl font-bold text-gray-900">
                    {material.current.toLocaleString()} {material.unit}
                  </p>
                  <p className="text-xs text-gray-500">
                    Required: {material.required.toLocaleString()} {material.unit}
                  </p>
                </div>

                <div className="text-right">
                  <Badge className={getStatusColor(material.status)}>
                    {material.status}
                  </Badge>
                  {material.status === 'low' && (
                    <p className="text-xs text-red-600 mt-1 font-medium">
                      Smart Alert: Reorder Required
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductionDepartment;

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Brain, Bot, Activity, TrendingUp, Zap, Database, BarChart3, Cpu, Sparkles, Eye } from "lucide-react";
import milkProducts from "/milk1.jpg";
import cheeseVariety from "/milk.jpg";
import freshMilkPour from "/milk2.jpg";

const Products = () => {
  const aiSolutions = [
    {
      name: "AI Health Monitoring",
      description: "Real-time health analytics for 234K+ animals using advanced AI algorithms and predictive modeling.",
      coverage: "234K+ Animals",
      features: ["Real-time Monitoring", "Predictive Analytics", "Health Scoring"],
      icon: Brain,
      gradient: "from-blue-500 to-purple-600",
      image: milkProducts
    },
    {
      name: "Smart Analytics Dashboard",
      description: "Comprehensive data visualization and insights across 12 talukas with interactive charts and reports.",
      coverage: "12 Talukas",
      features: ["Live Dashboards", "Interactive Charts", "Custom Reports"],
      icon: BarChart3,
      gradient: "from-green-500 to-blue-500",
      image: cheeseVariety
    },
    {
      name: "Automated Operations",
      description: "AI-powered automation for production, quality control, and supply chain optimization.",
      coverage: "6K+ Sansthas",
      features: ["Process Automation", "Quality Control", "Supply Chain"],
      icon: Bot,
      gradient: "from-purple-500 to-pink-500",
      image: freshMilkPour
    }
  ];

  return (
    <section id="products" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Clean Header */}
        <div className="text-center max-w-4xl mx-auto mb-16">
          <Badge className="mb-6 bg-blue-100 text-blue-800 px-6 py-2 text-sm font-medium">
            <Database className="w-4 h-4 mr-2" />
            Our Solutions
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-800">
            Professional Dairy Solutions
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Comprehensive dairy management solutions designed for modern farming operations
            with advanced analytics and monitoring capabilities.
          </p>
        </div>

        {/* Solutions Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {aiSolutions.map((solution, index) => {
            const IconComponent = solution.icon;
            return (
              <Card
                key={index}
                className="group hover:shadow-lg transition-all duration-300 border border-gray-200 overflow-hidden bg-white"
              >
                {/* Solution Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={solution.image}
                    alt={solution.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute top-4 right-4">
                    <div className="p-3 rounded-full bg-blue-600 text-white shadow-lg">
                      <IconComponent className="w-6 h-6" />
                    </div>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <Badge className="bg-white text-gray-800 border-0">
                      {solution.coverage}
                    </Badge>
                  </div>
                </div>

                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-xl text-gray-800 mb-3">
                    {solution.name}
                  </CardTitle>
                  <p className="text-gray-600 text-sm leading-relaxed">{solution.description}</p>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="space-y-3 mb-6">
                    {solution.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-blue-600/90 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Button className={`w-full bg-gradient-to-r ${solution.gradient} hover:opacity-90 text-white font-semibold transform transition-all duration-300 hover:scale-105 shadow-lg`}>
                    <Eye className="w-4 h-4 mr-2" />
                    Explore Solution
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* AI Technology Showcase */}
        <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 backdrop-blur-lg rounded-3xl p-8 lg:p-12 relative overflow-hidden border border-white/20">
          {/* Floating AI Brain */}
          <div className="absolute top-4 right-4 w-24 h-24 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-lg border-4 border-white/30 hover:scale-110 transition-transform duration-300 z-10">
            <Brain className="w-12 h-12 text-white animate-pulse" />
          </div>

          {/* Animated Circuit Lines */}
          <div className="absolute inset-0 opacity-20">
            <svg className="w-full h-full" viewBox="0 0 400 300">
              <defs>
                <linearGradient id="ai-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#3B82F6" />
                  <stop offset="100%" stopColor="#8B5CF6" />
                </linearGradient>
              </defs>
              <path
                d="M50,50 L150,50 L150,100 L250,100 L250,150"
                stroke="url(#ai-gradient)"
                strokeWidth="2"
                fill="none"
                className="animate-pulse"
              />
              <circle cx="150" cy="100" r="3" fill="#3B82F6" className="animate-pulse" />
            </svg>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 items-center relative z-10">
            <div className="space-y-6">
              <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2">
                <Cpu className="w-4 h-4 mr-2 animate-spin" />
                AI Technology Platform
              </Badge>
              <h3 className="text-3xl lg:text-4xl font-bold text-white">
                Powered by Advanced AI & Machine Learning
              </h3>
              <p className="text-lg text-blue-100 leading-relaxed">
                Our cutting-edge AI platform revolutionizes dairy farming with intelligent automation, predictive analytics,
                and real-time monitoring across Kolhapur district's 12 talukas and 6,000 sansthas.
              </p>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-white">Real-time health monitoring for 234K+ animals</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                  <span className="text-white">Predictive analytics with 97.8% accuracy</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
                  <span className="text-white">Automated quality control and optimization</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                  <span className="text-white">Smart supply chain management</span>
                </div>
              </div>
              <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold px-8 py-3 transform transition-all duration-300 hover:scale-105 shadow-lg">
                <Database className="w-5 h-5 mr-2" />
                Explore AI Platform
              </Button>
            </div>
            <div className="relative">
              {/* Main AI Dashboard Preview */}
              <div className="relative">
                <div className="w-full h-80 bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-white/20 p-6 overflow-hidden">
                  {/* Dashboard Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-white text-sm font-medium">AI Systems Online</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Activity className="w-4 h-4 text-blue-400" />
                      <span className="text-blue-400 text-xs">Live Data</span>
                    </div>
                  </div>

                  {/* Mock Charts */}
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="bg-white/5 p-3 rounded-lg border border-white/10">
                        <div className="text-green-400 text-lg font-bold">234K</div>
                        <div className="text-white/70 text-xs">Animals</div>
                      </div>
                      <div className="bg-white/5 p-3 rounded-lg border border-white/10">
                        <div className="text-blue-400 text-lg font-bold">97.8%</div>
                        <div className="text-white/70 text-xs">AI Accuracy</div>
                      </div>
                      <div className="bg-white/5 p-3 rounded-lg border border-white/10">
                        <div className="text-purple-400 text-lg font-bold">12</div>
                        <div className="text-white/70 text-xs">Talukas</div>
                      </div>
                    </div>

                    {/* Mock Chart Lines */}
                    <div className="space-y-2">
                      <div className="h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full w-full"></div>
                      <div className="h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full w-3/4"></div>
                      <div className="h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full w-1/2"></div>
                    </div>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-blue-900/20 to-transparent rounded-2xl"></div>
              </div>

              {/* Floating AI Chip */}
              <div className="absolute -bottom-6 -left-6 w-32 h-32 rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center shadow-xl border-4 border-white/20 hover:scale-105 transition-transform duration-300">
                <Cpu className="w-16 h-16 text-white animate-spin" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Products;
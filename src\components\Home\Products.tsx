import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Milk, Truck, Shield, Clock } from "lucide-react";
import milkProducts from "/milk1.jpg";
import cheeseVariety from "/milk.jpg";
import freshMilkPour from "/milk2.jpg";

const Products = () => {
  const products = [
    {
      name: "Fresh Milk",
      description: "Pure, farm-fresh milk delivered daily. Rich in nutrients and free from preservatives.",
      price: "₹60/L",
      features: ["Farm Fresh", "Daily Delivery", "No Preservatives"],
      icon: Milk,
      gradient: "from-cyan-600 to-blue-400"
    },
    {
      name: "Toned Milk",
      description: "Low-fat milk perfect for health-conscious families. Maintains great taste with reduced fat.",
      price: "₹55/L",
      features: ["Low Fat", "Healthy Choice", "Great Taste"],
      icon: Shield,
      gradient: "from-blue-400 to-green-400"
    },
    {
      name: "Cow Milk",
      description: "Pure cow milk from grass-fed cows. Naturally rich in calcium and vitamins.",
      price: "₹70/L",
      features: ["Grass Fed", "A2 Protein", "Vitamin Rich"],
      icon: Clock,
      gradient: "from-orange-400 to-yellow-400"
    }
  ];

  return (
    <section id="products" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <Badge variant="outline" className="mb-4">Our Products</Badge>
          <h2 className="text-4xl lg:text-6xl font-bold text-primary mb-6">
            Premium Dairy Products
          </h2>
          <p className="text-xl text-gray-900">
            From our farm to your family, discover our range of fresh, nutritious dairy products crafted with care and quality.
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {products.map((product, index) => {
            const IconComponent = product.icon;
            return (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-border">
                <CardHeader className="space-y-4">
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${product.gradient} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl text-primary mb-2">{product.name}</CardTitle>
                    <div className="text-3xl font-bold text-gray-800 mb-3">{product.price}</div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <p className="text-gray-900 leading-relaxed">{product.description}</p>
                  
                  <div className="flex flex-wrap gap-2">
                    {product.features.map((feature, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>

                  <Button className="w-full" variant="outline">
                    <Truck className="w-4 h-4 mr-2" />
                    Order Now
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Featured Section */}
        <div className="bg-gradient-to-r from-cream to-dairy-orange-light rounded-3xl p-8 lg:p-12 relative overflow-hidden">
          {/* Floating Cheese Image */}
          <div className="absolute top-4 right-4 w-24 h-24 rounded-full overflow-hidden shadow-lg border-4 border-white/70 hover:scale-110 transition-transform duration-300 z-10">
            <img 
              src={cheeseVariety} 
              alt="Cheese Variety" 
              className="w-full h-full object-cover"
            />
          </div>
          
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <div className="space-y-6">
              <Badge className="bg-secondary text-gray-800-foreground">Featured Products</Badge>
              <h3 className="text-3xl lg:text-4xl font-bold text-primary">
                Fresh Dairy Products Made with Love
              </h3>
              <p className="text-lg text-gray-900">
                Our products are carefully processed in our state-of-the-art facility, ensuring the highest quality and freshness. From milk to yogurt, every product is a testament to our commitment to excellence.
              </p>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-secondary rounded-full"></div>
                  <span className="text-gray-900">Daily fresh collection from local farms</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-secondary rounded-full"></div>
                  <span className="text-gray-900">Advanced pasteurization technology</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-secondary rounded-full"></div>
                  <span className="text-gray-900">Cold chain delivery system</span>
                </div>
              </div>
              <Button variant="default" size="lg">
                Explore All Products
              </Button>
            </div>
            <div className="relative">
              {/* Main product image */}
              <div className="relative">
                <img 
                  src={milkProducts} 
                  alt="Dairy Products" 
                  className="w-full h-80 object-cover rounded-2xl shadow-lg"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent rounded-2xl"></div>
              </div>
              
              {/* Overlapping milk pour image */}
              <div className="absolute -bottom-6 -left-6 w-32 h-32 rounded-xl overflow-hidden shadow-xl border-4 border-background hover:scale-105 transition-transform duration-300">
                <img 
                  src={freshMilkPour} 
                  alt="Fresh Milk Pour" 
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Products;
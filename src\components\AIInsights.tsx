
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  PieChart,
  Target,
  Lightbulb,
  Zap,
  Package,
  Truck,
  Factory
} from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Pie<PERSON>hart as RechartsPieChart, Cell } from "recharts";

const AIInsights = () => {
  const [selectedDepartment, setSelectedDepartment] = useState("all");
  const [selectedInsightType, setSelectedInsightType] = useState("predictions");

  const departments = [
    { id: "all", name: "All Departments", icon: <Brain className="h-4 w-4" /> },
    { id: "procurement", name: "Procurement", icon: <Truck className="h-4 w-4" /> },
    { id: "quality", name: "Quality Control", icon: <Package className="h-4 w-4" /> },
    { id: "processing", name: "Processing", icon: <Factory className="h-4 w-4" /> },
    { id: "packaging", name: "Packaging", icon: <Package className="h-4 w-4" /> },
    { id: "dispatch", name: "Dispatch", icon: <Truck className="h-4 w-4" /> },
    { id: "energy", name: "Energy Management", icon: <Zap className="h-4 w-4" /> },
    { id: "sales", name: "Sales", icon: <BarChart3 className="h-4 w-4" /> }
  ];

  const insightTypes = [
    { id: "predictions", name: "Smart Predictions" },
    { id: "recommendations", name: "AI Recommendations" },
    { id: "anomalies", name: "Issue Detection" },
    { id: "trends", name: "Trend Analysis" }
  ];

  // Sample data that changes based on department
  const getProductionTrendData = () => {
    const baseData = [
      { month: 'Jan', actual: 15.2, predicted: 15.8 },
      { month: 'Feb', actual: 15.8, predicted: 16.2 },
      { month: 'Mar', actual: 16.1, predicted: 16.5 },
      { month: 'Apr', actual: 16.3, predicted: 16.8 },
      { month: 'May', actual: 16.2, predicted: 16.9 },
      { month: 'Jun', actual: 16.5, predicted: 17.1 }
    ];

    if (selectedDepartment === 'sales') {
      return baseData.map(item => ({
        ...item,
        actual: item.actual * 1.2,
        predicted: item.predicted * 1.2
      }));
    } else if (selectedDepartment === 'energy') {
      return baseData.map(item => ({
        ...item,
        actual: item.actual * 0.8,
        predicted: item.predicted * 0.8
      }));
    }
    return baseData;
  };

  const getQualityScoreData = () => {
    if (selectedDepartment === 'quality') {
      return [
        { category: 'Milk Quality', score: 96.8 },
        { category: 'Packaging', score: 94.2 },
        { category: 'Cold Chain', score: 97.1 },
        { category: 'Hygiene', score: 98.5 }
      ];
    } else if (selectedDepartment === 'energy') {
      return [
        { category: 'Solar Efficiency', score: 87.2 },
        { category: 'Grid Usage', score: 92.1 },
        { category: 'Cold Storage', score: 89.5 },
        { category: 'Overall Energy', score: 88.8 }
      ];
    }
    return [
      { category: 'Overall Quality', score: 96.8 },
      { category: 'Production', score: 94.2 },
      { category: 'Distribution', score: 97.1 },
      { category: 'Customer Satisfaction', score: 98.5 }
    ];
  };

  const departmentPerformance = [
    { name: 'Quality', value: 30, color: '#10B981' },
    { name: 'Production', value: 25, color: '#3B82F6' },
    { name: 'Sales', value: 20, color: '#F59E0B' },
    { name: 'Energy', value: 25, color: '#EF4444' }
  ];

  const getAIInsights = () => {
    const allInsights = [
      {
        id: 1,
        department: "procurement",
        type: "prediction",
        title: "Milk Volume Forecasting",
        description: "Smart system predicts 12% increase in milk intake from Kini and Wathar routes during festival season",
        confidence: 87,
        impact: "High",
        timeframe: "Next 7 days",
        action: "Increase tanker capacity on routes",
        icon: <TrendingUp className="h-5 w-5 text-green-600" />
      },
      {
        id: 2,
        department: "quality",
        type: "anomaly",
        title: "Quality Pattern Detection",
        description: "Smart monitoring detected unusual patterns in milk samples from Peth Vadgaon route",
        confidence: 92,
        impact: "Medium",
        timeframe: "Immediate",
        action: "Enhanced quality testing required",
        icon: <AlertTriangle className="h-5 w-5 text-yellow-600" />
      },
      {
        id: 3,
        department: "processing",
        type: "maintenance",
        title: "Smart Maintenance Alert",
        description: "Pasteurizer pump showing early wear indicators. Maintenance recommended before failure",
        confidence: 84,
        impact: "High",
        timeframe: "Next 48 hours",
        action: "Schedule preventive maintenance",
        icon: <Factory className="h-5 w-5 text-orange-600" />
      },
      {
        id: 4,
        department: "packaging",
        type: "quality",
        title: "Packaging Quality Detection",
        description: "Smart system detected 15% increase in seal defects on Line 3",
        confidence: 96,
        impact: "Medium",
        timeframe: "Current shift",
        action: "Adjust sealing parameters",
        icon: <Package className="h-5 w-5 text-red-600" />
      },
      {
        id: 5,
        department: "dispatch",
        type: "optimization",
        title: "Route Optimization",
        description: "Smart routing suggests 18% fuel savings by optimizing delivery routes to Kolhapur district",
        confidence: 89,
        impact: "High",
        timeframe: "Next week",
        action: "Implement optimized routes",
        icon: <Target className="h-5 w-5 text-blue-600" />
      },
      {
        id: 6,
        department: "energy",
        type: "forecast",
        title: "Solar Energy Forecasting",
        description: "Weather prediction model forecasts optimal solar generation for next 5 days",
        confidence: 85,
        impact: "Medium",
        timeframe: "Next 5 days",
        action: "Optimize energy distribution",
        icon: <Zap className="h-5 w-5 text-yellow-500" />
      },
      {
        id: 7,
        department: "sales",
        type: "prediction",
        title: "Festival Demand Forecasting",
        description: "Smart analytics predict 25% increase in ghee demand for upcoming Diwali season",
        confidence: 91,
        impact: "High",
        timeframe: "Next 15 days",
        action: "Increase ghee production capacity",
        icon: <TrendingUp className="h-5 w-5 text-green-600" />
      }
    ];

    return allInsights.filter(insight => 
      selectedDepartment === 'all' || insight.department === selectedDepartment
    );
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'bg-green-100 text-green-800';
    if (confidence >= 80) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getImpactColor = (impact: string) => {
    switch (impact.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            Smart Insights & Analytics
          </h2>
          <p className="text-gray-600 mt-1">Intelligent insights for operational excellence</p>
        </div>
        <div className="flex items-center space-x-4">
          <select 
            value={selectedDepartment}
            onChange={(e) => setSelectedDepartment(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white"
          >
            {departments.map(dept => (
              <option key={dept.id} value={dept.id}>{dept.name}</option>
            ))}
          </select>
          <select 
            value={selectedInsightType}
            onChange={(e) => setSelectedInsightType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white"
          >
            {insightTypes.map(type => (
              <option key={type.id} value={type.id}>{type.name}</option>
            ))}
          </select>
        </div>
      </div>

      {/* AI Insights Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {getAIInsights().map((insight) => (
          <Card key={insight.id} className="border-l-4 border-l-blue-500 hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  {insight.icon}
                  <div>
                    <CardTitle className="text-lg">{insight.title}</CardTitle>
                    <Badge variant="outline" className="mt-1 capitalize">{insight.department}</Badge>
                  </div>
                </div>
                <Badge className={getConfidenceColor(insight.confidence)}>
                  {insight.confidence}% Confidence
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">{insight.description}</p>
              <div className="flex flex-wrap gap-2 mb-4">
                <Badge className={getImpactColor(insight.impact)}>
                  {insight.impact} Impact
                </Badge>
                <Badge variant="outline">
                  {insight.timeframe}
                </Badge>
              </div>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                {insight.action}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Production Trend Prediction */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span>Smart Trend Predictions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={getProductionTrendData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="actual" stroke="#3B82F6" strokeWidth={2} name="Actual" />
                <Line type="monotone" dataKey="predicted" stroke="#10B981" strokeWidth={2} strokeDasharray="5 5" name="Smart Predicted" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Quality Score Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              <span>Performance Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={getQualityScoreData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="score" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Department Performance Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <PieChart className="h-5 w-5 text-purple-600" />
            <span>Smart Integration Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <RechartsPieChart 
                  data={departmentPerformance} 
                  cx="50%" 
                  cy="50%" 
                  outerRadius={80} 
                  dataKey="value"
                >
                  {departmentPerformance.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </RechartsPieChart>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">Smart Implementation Status</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <Lightbulb className="h-5 w-5 text-yellow-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Milk Volume Forecasting</p>
                    <p className="text-xs text-gray-600">95% accuracy in seasonal predictions</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Lightbulb className="h-5 w-5 text-yellow-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Smart Quality Detection</p>
                    <p className="text-xs text-gray-600">Automated quality monitoring system</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Lightbulb className="h-5 w-5 text-yellow-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Smart Maintenance</p>
                    <p className="text-xs text-gray-600">Reduces downtime by 40%</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Lightbulb className="h-5 w-5 text-yellow-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Route Optimization</p>
                    <p className="text-xs text-gray-600">18% fuel savings achieved</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AIInsights;

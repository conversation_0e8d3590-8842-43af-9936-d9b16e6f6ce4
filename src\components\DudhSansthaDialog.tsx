import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { 
  Building, 
  Users, 
  Milk, 
  IndianRupee, 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  Droplets,
  Target,
  Brain,
  Star,
  AlertTriangle
} from "lucide-react";

interface DudhSansthaDialogProps {
  sanstha: {
    name: string;
    milkCollected: number;
    avgRate: number;
    totalAmount: number;
    amountPaid: number;
    pending: number;
    performance: string;
  };
  children: React.ReactNode;
}

const DudhSansthaDialog = ({ sanstha, children }: DudhSansthaDialogProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const formatCurrency = (amount: number) => {
    if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`;
    }
    return `₹${(amount / 1000).toFixed(0)}K`;
  };

  // Generate detailed data for the Sanstha
  const generateSansthaDetails = (sansthaName: string) => {
    const baseData = {
      establishedYear: 2018,
      totalFarmers: 185,
      activeFarmers: 178,
      avgDailyCollection: 1420,
      qualityScore: 96.8,
      fatContent: 3.4,
      snfContent: 8.6,
      lastPaymentDate: "2024-01-15",
      nextPaymentDate: "2024-01-30",
      monthlyTrend: [
        { month: "Oct", collection: 42000, quality: 95.2, payment: 1250000 },
        { month: "Nov", collection: 44500, quality: 96.1, payment: 1320000 },
        { month: "Dec", collection: 43200, quality: 96.8, payment: 1286000 },
        { month: "Jan", collection: 45800, quality: 97.2, payment: 1360000 }
      ],
      aiInsights: sanstha.performance === 'excellent' ? [
        "Exceptional quality maintenance with 97.2% score - industry leading",
        "Consistent 8.5% growth in milk collection over 3 quarters",
        "Zero payment delays - exemplary financial reliability",
        "Qualifies for premium rate tier due to outstanding performance"
      ] : sanstha.performance === 'good' ? [
        "Steady quality improvement trajectory observed",
        "Good collection consistency with moderate growth",
        "Regular payment schedule maintained",
        "Potential for rate optimization with continued performance"
      ] : sanstha.performance === 'average' ? [
        "Quality parameters meet baseline standards",
        "Collection volumes stable but growth potential exists",
        "Payment schedule adherence needs improvement",
        "Recommend quality enhancement training programs"
      ] : [
        "Quality metrics below industry standards - immediate attention required",
        "Declining collection trends affecting overall productivity",
        "Payment delays impacting cash flow - review required",
        "Urgent intervention needed for performance improvement"
      ],
      qualityFactors: [
        { parameter: "Fat Content", current: 3.4, target: 3.5, status: "good" },
        { parameter: "SNF Content", current: 8.6, target: 8.5, status: "excellent" },
        { parameter: "Protein", current: 3.2, target: 3.2, status: "good" },
        { parameter: "Total Solids", current: 12.0, target: 12.0, status: "excellent" }
      ]
    };

    return baseData;
  };

  const details = generateSansthaDetails(sanstha.name);

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'average': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'poor': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getQualityStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'average': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Building className="h-5 w-5 text-blue-600" />
            <span>{sanstha.name} - Detailed Report</span>
            <Badge className={getPerformanceColor(sanstha.performance)}>
              {sanstha.performance.replace('_', ' ').toUpperCase()}
            </Badge>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Key Metrics Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-blue-600">{details.totalFarmers}</div>
                <p className="text-sm text-gray-600">Total Farmers</p>
                <p className="text-xs text-green-600">{details.activeFarmers} Active</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <Milk className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-green-600">{details.avgDailyCollection}L</div>
                <p className="text-sm text-gray-600">Daily Avg Collection</p>
                <p className="text-xs text-green-600">+8.5% vs last quarter</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <Star className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-yellow-600">{details.qualityScore}%</div>
                <p className="text-sm text-gray-600">Quality Score</p>
                <p className="text-xs text-green-600">+2% improvement</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <IndianRupee className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-purple-600">₹{sanstha.avgRate.toFixed(2)}</div>
                <p className="text-sm text-gray-600">Rate per Litre</p>
                <p className="text-xs text-blue-600">Premium rate</p>
              </CardContent>
            </Card>
          </div>

          {/* Financial Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <IndianRupee className="h-5 w-5 text-green-600" />
                <span>Financial Summary</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <div className="text-sm text-gray-600">Total Amount Due</div>
                  <div className="text-2xl font-bold text-gray-900">{formatCurrency(sanstha.totalAmount)}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm text-gray-600">Amount Paid</div>
                  <div className="text-2xl font-bold text-green-600">{formatCurrency(sanstha.amountPaid)}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm text-gray-600">Pending Payment</div>
                  <div className={`text-2xl font-bold ${sanstha.pending > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {sanstha.pending > 0 ? formatCurrency(sanstha.pending) : '₹0'}
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Payment Progress</span>
                  <span>{Math.round((sanstha.amountPaid / sanstha.totalAmount) * 100)}%</span>
                </div>
                <Progress value={(sanstha.amountPaid / sanstha.totalAmount) * 100} className="h-3" />
              </div>

              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Last Payment: </span>
                  <span className="font-medium">{details.lastPaymentDate}</span>
                </div>
                <div>
                  <span className="text-gray-600">Next Payment: </span>
                  <span className="font-medium text-blue-600">{details.nextPaymentDate}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quality Parameters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Droplets className="h-5 w-5 text-blue-600" />
                <span>Quality Parameters</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {details.qualityFactors.map((factor, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">{factor.parameter}</span>
                      <Badge className={getQualityStatusColor(factor.status)}>
                        {factor.status}
                      </Badge>
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {factor.current}% / {factor.target}%
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {factor.current >= factor.target ? 'Above target' : 'Below target'}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* AI Insights & Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-purple-600" />
                <span>AI Insights & Recommendations</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {details.aiInsights.map((insight, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-purple-50 rounded-lg">
                    <Brain className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-purple-800">{insight}</p>
                  </div>
                ))}
              </div>
              
              <div className={`mt-6 p-4 rounded-lg border ${
                sanstha.performance === 'excellent' 
                  ? 'bg-gradient-to-r from-green-50 to-blue-50 border-green-200'
                  : sanstha.performance === 'good'
                  ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200'
                  : sanstha.performance === 'average'
                  ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200'
                  : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'
              }`}>
                <div className="flex items-center space-x-2 mb-2">
                  <Target className={`h-5 w-5 ${
                    sanstha.performance === 'excellent' ? 'text-green-600' 
                    : sanstha.performance === 'good' ? 'text-blue-600'
                    : sanstha.performance === 'average' ? 'text-yellow-600'
                    : 'text-red-600'
                  }`} />
                  <span className={`font-semibold ${
                    sanstha.performance === 'excellent' ? 'text-green-800' 
                    : sanstha.performance === 'good' ? 'text-blue-800'
                    : sanstha.performance === 'average' ? 'text-yellow-800'
                    : 'text-red-800'
                  }`}>AI Recommendation</span>
                </div>
                <p className={`text-sm ${
                  sanstha.performance === 'excellent' ? 'text-green-700' 
                  : sanstha.performance === 'good' ? 'text-blue-700'
                  : sanstha.performance === 'average' ? 'text-yellow-700'
                  : 'text-red-700'
                }`}>
                  {sanstha.performance === 'excellent' 
                    ? `Outstanding performance qualifies for immediate ₹0.75 rate increase and priority processing status. Continue excellence monitoring.`
                    : sanstha.performance === 'good'
                    ? `Good performance metrics support ₹0.25 rate increase. Monitor quality consistency for further optimizations.`
                    : sanstha.performance === 'average'
                    ? `Average performance requires quality improvement plan. Consider training programs before rate adjustments.`
                    : `Poor performance needs immediate intervention. Suspend rate increases and implement quality recovery program.`
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
          <Button className="bg-green-600 hover:bg-green-700">
            Process Payment
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DudhSansthaDialog;
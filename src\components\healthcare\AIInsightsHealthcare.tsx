import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Target,
  Lightbulb,
  Activity,
  MapPin,
  Calendar,
  Thermometer,
  Droplets,
  Shield,
  Zap
} from "lucide-react";

// Mock data for AI insights and predictions
const mockAIInsights = {
  predictions: [
    {
      id: "growth-potential",
      title: "Animal Growth Potential",
      category: "Growth Analysis",
      confidence: 94,
      timeframe: "Next 3 months",
      insights: [
        {
          region: "Kolhapur",
          prediction: "15% increase in milk production expected",
          confidence: 92,
          factors: ["Improved feed quality", "Better health management", "Seasonal advantages"]
        },
        {
          region: "Sangli", 
          prediction: "8% growth potential with optimization",
          confidence: 87,
          factors: ["Feed efficiency improvements needed", "Veterinary care optimization"]
        }
      ]
    },
    {
      id: "expansion-regions",
      title: "Best Regions for Expansion",
      category: "Strategic Planning",
      confidence: 89,
      timeframe: "Next 6 months",
      insights: [
        {
          region: "Satara",
          prediction: "Highest ROI for new animal additions",
          confidence: 91,
          factors: ["Low competition", "Good infrastructure", "Favorable climate"]
        },
        {
          region: "Kolhapur",
          prediction: "Moderate expansion potential",
          confidence: 78,
          factors: ["Market saturation concerns", "Higher operational costs"]
        }
      ]
    }
  ],
  optimizations: [
    {
      id: "feed-optimization",
      title: "Feed Optimization Recommendations",
      category: "Cost Reduction",
      potentialSavings: "₹2.3L per month",
      recommendations: [
        {
          region: "All Regions",
          suggestion: "Switch to high-protein concentrate mix",
          impact: "12% reduction in feed costs, 8% increase in milk yield",
          implementation: "Gradual transition over 2 weeks"
        },
        {
          region: "Sangli",
          suggestion: "Optimize feeding schedule to 3 times daily",
          impact: "5% improvement in feed efficiency",
          implementation: "Immediate implementation possible"
        }
      ]
    }
  ],
  qualityForecasts: [
    {
      id: "milk-quality",
      title: "Milk Quality Forecast",
      category: "Quality Management",
      timeframe: "Next 30 days",
      predictions: [
        {
          region: "Kolhapur",
          currentGrade: "A+",
          predictedGrade: "A+",
          confidence: 96,
          factors: ["Stable health conditions", "Consistent feed quality"]
        },
        {
          region: "Sangli",
          currentGrade: "A",
          predictedGrade: "A+",
          confidence: 84,
          factors: ["Improving health scores", "Better veterinary care"]
        }
      ]
    }
  ],
  riskMonitoring: [
    {
      id: "contamination-risk",
      title: "Milk Contamination Risk",
      level: "Low",
      confidence: 92,
      regions: [
        { name: "Kolhapur", risk: "Very Low", score: 95 },
        { name: "Sangli", risk: "Low", score: 88 },
        { name: "Satara", risk: "Low", score: 91 }
      ]
    },
    {
      id: "outbreak-risk",
      title: "Disease Outbreak Risk",
      level: "Medium",
      confidence: 87,
      regions: [
        { name: "Kolhapur", risk: "Low", score: 92 },
        { name: "Sangli", risk: "Medium", score: 76 },
        { name: "Satara", risk: "Low", score: 89 }
      ]
    }
  ],
  seasonalAnalysis: {
    currentSeason: "Winter",
    healthTrends: [
      {
        season: "Winter",
        healthScore: 87,
        milkProduction: "High",
        commonIssues: ["Respiratory infections", "Feed quality variations"],
        recommendations: ["Increase vitamin supplements", "Monitor ventilation"]
      },
      {
        season: "Summer",
        healthScore: 82,
        milkProduction: "Medium",
        commonIssues: ["Heat stress", "Dehydration"],
        recommendations: ["Provide adequate shade", "Increase water intake"]
      }
    ]
  }
};

const AIInsightsHealthcare = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>("all");

  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case "very low": return "bg-green-100 text-green-800";
      case "low": return "bg-blue-100 text-blue-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "high": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return "text-green-600";
    if (confidence >= 80) return "text-blue-600";
    if (confidence >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">AI-Powered Insights & Recommendations</h2>
          <p className="text-gray-600">Advanced analytics and predictive insights for optimal dairy operations</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="predictions">Predictions</SelectItem>
              <SelectItem value="optimization">Optimization</SelectItem>
              <SelectItem value="risk">Risk Monitoring</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Timeframe" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Timeframes</SelectItem>
              <SelectItem value="short">Short Term</SelectItem>
              <SelectItem value="medium">Medium Term</SelectItem>
              <SelectItem value="long">Long Term</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* AI Predictions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {mockAIInsights.predictions.map(prediction => (
          <Card key={prediction.id} className="shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-purple-600" />
                  {prediction.title}
                </CardTitle>
                <Badge variant="outline" className="text-xs">
                  {prediction.category}
                </Badge>
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>Confidence: <span className={`font-bold ${getConfidenceColor(prediction.confidence)}`}>
                  {prediction.confidence}%
                </span></span>
                <span>{prediction.timeframe}</span>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {prediction.insights.map((insight, index) => (
                <div key={index} className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-blue-600" />
                      <span className="font-semibold text-gray-900">{insight.region}</span>
                    </div>
                    <Badge className={`${getConfidenceColor(insight.confidence)} bg-transparent border`}>
                      {insight.confidence}%
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700 mb-2">{insight.prediction}</p>
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-gray-600">Key Factors:</div>
                    {insight.factors.map((factor, idx) => (
                      <div key={idx} className="text-xs text-gray-600 flex items-center gap-1">
                        <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                        {factor}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Feed Optimization */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-yellow-600" />
            Feed Optimization Recommendations
          </CardTitle>
          <div className="text-sm text-gray-600">
            Potential monthly savings: <span className="font-bold text-green-600">₹2.3L</span>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockAIInsights.optimizations[0].recommendations.map((rec, index) => (
              <div key={index} className="border rounded-lg p-4 bg-yellow-50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-yellow-600" />
                    <span className="font-semibold text-gray-900">{rec.region}</span>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    Implementation: {rec.implementation}
                  </Badge>
                </div>
                <p className="text-sm text-gray-700 mb-2">{rec.suggestion}</p>
                <div className="text-sm text-green-700 font-medium">
                  Expected Impact: {rec.impact}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Risk Monitoring */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {mockAIInsights.riskMonitoring.map(risk => (
          <Card key={risk.id} className="shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-red-600" />
                  {risk.title}
                </CardTitle>
                <Badge className={getRiskColor(risk.level)}>
                  {risk.level} Risk
                </Badge>
              </div>
              <div className="text-sm text-gray-600">
                Confidence: <span className={`font-bold ${getConfidenceColor(risk.confidence)}`}>
                  {risk.confidence}%
                </span>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3">
              {risk.regions.map((region, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="font-medium text-gray-900">{region.name}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className={getRiskColor(region.risk)} size="sm">
                      {region.risk}
                    </Badge>
                    <div className="text-sm font-medium text-gray-600">
                      {region.score}%
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Seasonal Analysis */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Thermometer className="h-5 w-5 text-blue-600" />
            Seasonal Health Variation Analysis
          </CardTitle>
          <div className="text-sm text-gray-600">
            Current Season: <span className="font-bold text-blue-600">{mockAIInsights.seasonalAnalysis.currentSeason}</span>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {mockAIInsights.seasonalAnalysis.healthTrends.map((trend, index) => (
              <div key={index} className="border rounded-lg p-4 bg-blue-50">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">{trend.season}</h4>
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">{trend.healthScore}% Health Score</span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-1">Milk Production</div>
                    <Badge variant="outline" className="text-xs">
                      {trend.milkProduction}
                    </Badge>
                  </div>
                  
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-1">Common Issues</div>
                    <div className="space-y-1">
                      {trend.commonIssues.map((issue, idx) => (
                        <div key={idx} className="text-xs text-gray-600 flex items-center gap-1">
                          <AlertTriangle className="h-3 w-3 text-yellow-500" />
                          {issue}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-1">Recommendations</div>
                    <div className="space-y-1">
                      {trend.recommendations.map((rec, idx) => (
                        <div key={idx} className="text-xs text-green-700 flex items-center gap-1">
                          <Zap className="h-3 w-3 text-green-500" />
                          {rec}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quality Forecast */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Droplets className="h-5 w-5 text-purple-600" />
            Milk Quality Forecast - Next 30 Days
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {mockAIInsights.qualityForecasts[0].predictions.map((forecast, index) => (
              <div key={index} className="border rounded-lg p-4 bg-purple-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-purple-600" />
                    <span className="font-semibold text-gray-900">{forecast.region}</span>
                  </div>
                  <Badge className={`${getConfidenceColor(forecast.confidence)} bg-transparent border`}>
                    {forecast.confidence}%
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Current Grade:</span>
                    <Badge className="bg-blue-100 text-blue-800">{forecast.currentGrade}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Predicted Grade:</span>
                    <Badge className="bg-green-100 text-green-800">{forecast.predictedGrade}</Badge>
                  </div>
                </div>
                
                <div className="mt-3 pt-3 border-t border-purple-200">
                  <div className="text-xs font-medium text-gray-600 mb-1">Contributing Factors:</div>
                  {forecast.factors.map((factor, idx) => (
                    <div key={idx} className="text-xs text-gray-600 flex items-center gap-1">
                      <div className="w-1 h-1 bg-purple-400 rounded-full"></div>
                      {factor}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AIInsightsHealthcare;

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  LayoutDashboard, 
  Heart, 
  Bot, 
  Building2, 
  Activity, 
  Users,
  Milk,
  TrendingUp,
  MapPin,
  Stethoscope,
  BarChart3,
  Target,
  AlertTriangle
} from "lucide-react";

// Import our new components
import InternalOperationsDashboard from "@/components/Dashboard/MilkProcurementFlow";

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState("operations");

  // Dashboard overview stats
  const overviewStats = {
    totalAnimals: 234000,
    healthyAnimals: 221580,
    dailyMilkProduction: 240000,
    activeDCS: 847,
    talukas: 12,
    villages: 1204,
    veterinaryDoctors: 45,
    criticalCases: 3450,
    efficiency: 99.0,
    wastage: 2400,
    resourceUtilization: 94.5
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">गोकुळ दूध डेयरी - Internal Operations</h1>
            <p className="text-gray-600">Enterprise Dashboard - Hierarchical Operations Management</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge className="bg-green-100 text-green-800">
              <Activity className="w-3 h-3 mr-1" />
              Live Operations
            </Badge>
            <Badge className="bg-blue-100 text-blue-800">
              <Target className="w-3 h-3 mr-1" />
              {overviewStats.efficiency}% Efficiency
            </Badge>
            <Button className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
              <Bot className="w-4 h-4 mr-2" />
              Dr. GPT
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          {/* Tab Navigation */}
          <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:grid-cols-5">
            <TabsTrigger value="operations" className="flex items-center gap-2">
              <Building2 className="w-4 h-4" />
              <span className="hidden sm:inline">Operations</span>
            </TabsTrigger>
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <LayoutDashboard className="w-4 h-4" />
              <span className="hidden sm:inline">Overview</span>
            </TabsTrigger>
            <TabsTrigger value="healthcare" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              <span className="hidden sm:inline">Healthcare</span>
            </TabsTrigger>
            <TabsTrigger value="animals" className="flex items-center gap-2">
              <Stethoscope className="w-4 h-4" />
              <span className="hidden sm:inline">Animals</span>
            </TabsTrigger>
            <TabsTrigger value="drgpt" className="flex items-center gap-2">
              <Bot className="w-4 h-4" />
              <span className="hidden sm:inline">Dr. GPT</span>
            </TabsTrigger>
          </TabsList>

          {/* Internal Operations Dashboard */}
          <TabsContent value="operations">
            <InternalOperationsDashboard />
          </TabsContent>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Executive Summary */}
            <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-green-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                  Executive Summary - District Operations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  <div className="text-center p-4 bg-white rounded-lg border border-blue-200">
                    <div className="text-2xl font-bold text-blue-600">
                      {overviewStats.totalAnimals.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Total Animals</div>
                    <div className="text-xs text-green-600 mt-1">
                      <TrendingUp className="w-3 h-3 inline mr-1" />
                      +2.5%
                    </div>
                  </div>

                  <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                    <div className="text-2xl font-bold text-green-600">
                      {overviewStats.dailyMilkProduction.toLocaleString()}L
                    </div>
                    <div className="text-sm text-gray-600">Daily Production</div>
                    <div className="text-xs text-green-600 mt-1">
                      <TrendingUp className="w-3 h-3 inline mr-1" />
                      +1.8%
                    </div>
                  </div>

                  <div className="text-center p-4 bg-white rounded-lg border border-purple-200">
                    <div className="text-2xl font-bold text-purple-600">
                      {overviewStats.efficiency}%
                    </div>
                    <div className="text-sm text-gray-600">Efficiency</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Wastage: {overviewStats.wastage}L
                    </div>
                  </div>

                  <div className="text-center p-4 bg-white rounded-lg border border-orange-200">
                    <div className="text-2xl font-bold text-orange-600">
                      {overviewStats.activeDCS}
                    </div>
                    <div className="text-sm text-gray-600">Active DCS</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {overviewStats.talukas} Talukas
                    </div>
                  </div>

                  <div className="text-center p-4 bg-white rounded-lg border border-red-200">
                    <div className="text-2xl font-bold text-red-600">
                      {overviewStats.criticalCases}
                    </div>
                    <div className="text-sm text-gray-600">Critical Cases</div>
                    <div className="text-xs text-red-600 mt-1">
                      <AlertTriangle className="w-3 h-3 inline mr-1" />
                      Needs Attention
                    </div>
                  </div>

                  <div className="text-center p-4 bg-white rounded-lg border border-teal-200">
                    <div className="text-2xl font-bold text-teal-600">
                      {overviewStats.resourceUtilization}%
                    </div>
                    <div className="text-sm text-gray-600">Resource Utilization</div>
                    <div className="text-xs text-green-600 mt-1">Optimal</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions & Navigation</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button 
                    variant="outline" 
                    className="h-20 flex flex-col items-center justify-center border-2 border-blue-200 hover:bg-blue-50"
                    onClick={() => setActiveTab("operations")}
                  >
                    <Building2 className="w-6 h-6 mb-2 text-blue-600" />
                    <span>Internal Operations</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-20 flex flex-col items-center justify-center border-2 border-green-200 hover:bg-green-50"
                    onClick={() => setActiveTab("healthcare")}
                  >
                    <Heart className="w-6 h-6 mb-2 text-green-600" />
                    <span>Health Overview</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-20 flex flex-col items-center justify-center border-2 border-purple-200 hover:bg-purple-50"
                    onClick={() => setActiveTab("animals")}
                  >
                    <Stethoscope className="w-6 h-6 mb-2 text-purple-600" />
                    <span>Animal Analysis</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-20 flex flex-col items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200"
                    onClick={() => setActiveTab("drgpt")}
                  >
                    <Bot className="w-6 h-6 mb-2 text-blue-600" />
                    <span>Dr. GPT</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Hierarchical Structure Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  Hierarchical Structure Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="text-2xl font-bold text-blue-600">{overviewStats.villages}</div>
                    <div className="text-sm text-gray-600">Villages</div>
                    <div className="text-xs text-gray-500 mt-1">Base Level</div>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="text-2xl font-bold text-green-600">{overviewStats.activeDCS}</div>
                    <div className="text-sm text-gray-600">DCS Centers</div>
                    <div className="text-xs text-gray-500 mt-1">Collection Points</div>
                  </div>
                  <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <div className="text-2xl font-bold text-purple-600">{overviewStats.talukas}</div>
                    <div className="text-sm text-gray-600">Talukas</div>
                    <div className="text-xs text-gray-500 mt-1">Regional Hubs</div>
                  </div>
                  <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="text-2xl font-bold text-orange-600">1</div>
                    <div className="text-sm text-gray-600">Gokul Unit</div>
                    <div className="text-xs text-gray-500 mt-1">Central Processing</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  Calendar,
  Target,
  AlertTriangle,
  CheckCircle,
  Activity,
  Zap,
  Brain
} from "lucide-react";

const TrendsAnalytics = () => {
  // Sample trend data
  const dailyTrends = [
    { date: "2024-01-15", production: 238500, efficiency: 98.8, health: 92.1, wastage: 2385 },
    { date: "2024-01-16", production: 240000, efficiency: 99.0, health: 92.3, wastage: 2400 },
    { date: "2024-01-17", production: 242100, efficiency: 99.2, health: 92.8, wastage: 2421 },
    { date: "2024-01-18", production: 239800, efficiency: 98.9, health: 91.9, wastage: 2398 },
    { date: "2024-01-19", production: 245000, efficiency: 99.5, health: 93.2, wastage: 2450 }
  ];

  const monthlyTrends = [
    { month: "Oct 2023", production: 7200000, efficiency: 98.2, health: 91.5, wastage: 72000 },
    { month: "Nov 2023", production: 7350000, efficiency: 98.5, health: 92.1, wastage: 73500 },
    { month: "Dec 2023", production: 7420000, efficiency: 98.8, health: 92.3, wastage: 74200 },
    { month: "Jan 2024", production: 7500000, efficiency: 99.0, health: 92.8, wastage: 75000 }
  ];

  const aiPredictions = [
    { village: "Rankala", current: 1200, predicted: 1250, confidence: 95, trend: "up" },
    { village: "Mahalaxmi", current: 890, predicted: 920, confidence: 88, trend: "up" },
    { village: "Shivaji Nagar", current: 1560, predicted: 1600, confidence: 92, trend: "up" },
    { village: "Panhala Fort", current: 980, predicted: 1020, confidence: 85, trend: "up" },
    { village: "Shirol", current: 1680, predicted: 1750, confidence: 78, trend: "critical" }
  ];

  const underperformingAreas = [
    { 
      area: "Shirol Taluka", 
      issue: "Health Index Below 85%", 
      severity: "critical",
      recommendation: "Immediate veterinary intervention required",
      impact: "High"
    },
    { 
      area: "Panhala Fort Village", 
      issue: "Resource Utilization < 90%", 
      severity: "moderate",
      recommendation: "Optimize feed distribution and water supply",
      impact: "Medium"
    },
    { 
      area: "Mahalaxmi DCS-2", 
      issue: "Milk Wastage Above 2%", 
      severity: "low",
      recommendation: "Review collection and storage procedures",
      impact: "Low"
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      case 'moderate': return <Activity className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Daily Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Calendar className="h-6 w-6 text-blue-600" />
            Daily Trends (Last 5 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left p-2">Date</th>
                  <th className="text-right p-2">Production (L)</th>
                  <th className="text-right p-2">Efficiency (%)</th>
                  <th className="text-right p-2">Health Index (%)</th>
                  <th className="text-right p-2">Wastage (L)</th>
                  <th className="text-right p-2">Trend</th>
                </tr>
              </thead>
              <tbody>
                {dailyTrends.map((day, index) => {
                  const prevDay = index > 0 ? dailyTrends[index - 1] : null;
                  const productionTrend = prevDay ? day.production > prevDay.production : true;
                  
                  return (
                    <tr key={day.date} className="border-b border-gray-100">
                      <td className="p-2 font-medium">{day.date}</td>
                      <td className="p-2 text-right">{day.production.toLocaleString()}</td>
                      <td className="p-2 text-right">{day.efficiency}%</td>
                      <td className="p-2 text-right">{day.health}%</td>
                      <td className="p-2 text-right text-red-600">{day.wastage.toLocaleString()}</td>
                      <td className="p-2 text-right">
                        {productionTrend ? (
                          <TrendingUp className="w-4 h-4 text-green-600 inline" />
                        ) : (
                          <TrendingDown className="w-4 h-4 text-red-600 inline" />
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Monthly Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <BarChart3 className="h-6 w-6 text-green-600" />
            Monthly Trends (Last 4 Months)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {monthlyTrends.map((month, index) => (
              <div key={month.month} className="p-4 bg-gray-50 rounded-lg border">
                <div className="text-sm font-medium text-gray-700 mb-2">{month.month}</div>
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>Production:</span>
                    <span className="font-medium">{(month.production / 1000000).toFixed(1)}M L</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Efficiency:</span>
                    <span className="font-medium">{month.efficiency}%</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Health:</span>
                    <span className="font-medium">{month.health}%</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Wastage:</span>
                    <span className="font-medium text-red-600">{(month.wastage / 1000).toFixed(0)}K L</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Predictions */}
      <Card className="border-2 border-purple-200 bg-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Brain className="h-6 w-6 text-purple-600" />
            AI-Predicted Output Per Village
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {aiPredictions.map((prediction, index) => (
              <Card key={index} className="border border-purple-200">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-gray-800">{prediction.village}</h4>
                      <Badge className={`${
                        prediction.trend === 'critical' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                      } text-xs`}>
                        {prediction.trend === 'critical' ? (
                          <AlertTriangle className="w-3 h-3 mr-1" />
                        ) : (
                          <TrendingUp className="w-3 h-3 mr-1" />
                        )}
                        {prediction.trend}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Current:</span>
                        <span className="font-medium">{prediction.current}L</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Predicted:</span>
                        <span className="font-medium text-purple-600">{prediction.predicted}L</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Confidence:</span>
                        <span className="font-medium">{prediction.confidence}%</span>
                      </div>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-purple-600 h-2 rounded-full" 
                        style={{ width: `${prediction.confidence}%` }}
                      ></div>
                    </div>

                    <div className="text-xs text-gray-500">
                      Expected increase: {((prediction.predicted - prediction.current) / prediction.current * 100).toFixed(1)}%
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Underperforming Areas */}
      <Card className="border-2 border-red-200 bg-red-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <AlertTriangle className="h-6 w-6 text-red-600" />
            Underperforming Areas - Action Required
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {underperformingAreas.map((area, index) => (
              <Card key={index} className="border border-red-200">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-semibold text-gray-800">{area.area}</h4>
                        <Badge className={`${getSeverityColor(area.severity)} border text-xs`}>
                          {getSeverityIcon(area.severity)}
                          <span className="ml-1 capitalize">{area.severity}</span>
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {area.impact} Impact
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="text-sm">
                          <span className="text-gray-600">Issue: </span>
                          <span className="font-medium text-red-700">{area.issue}</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-600">Recommendation: </span>
                          <span className="text-gray-800">{area.recommendation}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 ml-4">
                      <Button size="sm" variant="outline" className="text-xs">
                        <Target className="w-3 h-3 mr-1" />
                        Action Plan
                      </Button>
                      <Button size="sm" className="text-xs bg-red-600 hover:bg-red-700">
                        <Zap className="w-3 h-3 mr-1" />
                        Priority
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TrendsAnalytics;

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Clock, Building2, Bot, Database, Sparkles, Zap, Activity, Star, Truck } from "lucide-react";

const Contact = () => {
  return (
    <section id="contact" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Clean Header */}
        <div className="text-center max-w-4xl mx-auto mb-16">
          <Badge className="mb-6 bg-purple-100 text-purple-800 px-6 py-2 text-sm font-medium">
            <Phone className="w-4 h-4 mr-2" />
            Contact Us
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-800">
            Get in Touch
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Ready to enhance your dairy operations? Contact our professional team to learn more
            about our comprehensive solutions and services.
          </p>
        </div>

        <div className="grid lg:grid-cols-1 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center gap-3">
                <div className="p-3 bg-blue-600 rounded-full">
                  <Building2 className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-800">
                  गोकुळ दूध डेयरी
                </h3>
              </div>
              <p className="text-lg text-gray-600 leading-relaxed max-w-2xl mx-auto">
                Professional dairy management solutions serving the region with modern technology
                and comprehensive support for all your dairy operation needs.
              </p>
            </div>

            {/* Contact Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="p-6 bg-white border border-gray-200 hover:shadow-lg transition-all duration-300">
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Phone className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">
                      Phone Support
                    </h4>
                    <p className="text-gray-600">+91 98765 43210</p>
                    <p className="text-gray-600">+91 87654 32109</p>
                    <p className="text-xs text-green-600 mt-1">Professional Support</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 bg-white border border-gray-200 hover:shadow-lg transition-all duration-300">
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-green-100 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">
                      Email Contact
                    </h4>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-xs text-green-600 mt-1">Quick Response</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 backdrop-blur-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300 animate-slide-up" style={{ animationDelay: '0.2s' }}>
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-2 flex items-center gap-2">
                      AI Operations Center
                      <Activity className="w-4 h-4 text-purple-400 animate-pulse" />
                    </h4>
                    <p className="text-blue-200">Kolhapur District, Maharashtra</p>
                    <p className="text-blue-200">12 Talukas Coverage</p>
                    <p className="text-xs text-green-400 mt-1">Real-time Monitoring Active</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 backdrop-blur-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300 animate-slide-up" style={{ animationDelay: '0.3s' }}>
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-2 flex items-center gap-2">
                      AI System Availability
                      <Zap className="w-4 h-4 text-yellow-400 animate-pulse" />
                    </h4>
                    <p className="text-blue-200">24/7 AI Monitoring</p>
                    <p className="text-blue-200">Real-time Analytics</p>
                    <p className="text-xs text-green-400 mt-1">99.9% Uptime Guaranteed</p>
                  </div>
                </div>
              </Card>
            </div>

            {/* AI Platform Stats */}
            <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="flex justify-center mb-3">
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
                      <Activity className="w-6 h-6 text-white animate-pulse" />
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-white">24/7</div>
                  <div className="text-sm text-blue-200">AI Monitoring</div>
                  <div className="text-xs text-green-400 mt-1">Always Active</div>
                </div>
                <div className="text-center">
                  <div className="flex justify-center mb-3">
                    <div className="p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-full">
                      <Star className="w-6 h-6 text-white animate-pulse" />
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-white">97.8%</div>
                  <div className="text-sm text-blue-200">AI Accuracy</div>
                  <div className="text-xs text-green-400 mt-1">Precision Rate</div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          {/* <Card className="p-8 border-border">
            <CardHeader className="px-0 pt-0">
              <CardTitle className="text-2xl text-primary">Send us a Message</CardTitle>
              <p className="text-gray-900">
                Fill out the form below and we'll get back to you as soon as possible.
              </p>
            </CardHeader>
            <CardContent className="px-0 pb-0">
              <form className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      First Name
                    </label>
                    <Input placeholder="Your first name" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Last Name
                    </label>
                    <Input placeholder="Your last name" />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Email
                  </label>
                  <Input type="email" placeholder="<EMAIL>" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Phone Number
                  </label>
                  <Input type="tel" placeholder="+91 98765 43210" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Subject
                  </label>
                  <Input placeholder="Order inquiry, General question, etc." />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Message
                  </label>
                  <Textarea 
                    placeholder="Tell us about your requirements or questions..." 
                    rows={5}
                  />
                </div>
                
                <Button type="submit" variant="outline" size="lg" className="w-full">
                  Send Message
                </Button>
                
                <p className="text-sm text-gray-900 text-center">
                  We typically respond within 2-4 hours during business hours.
                </p>
              </form>
            </CardContent>
          </Card> */}
        </div>
      </div>
    </section>
  );
};

export default Contact;
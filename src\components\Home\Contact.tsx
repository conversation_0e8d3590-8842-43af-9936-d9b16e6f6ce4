import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Clock, Brain, Bot, Database, Sparkles, Zap, Activity, Star, Truck } from "lucide-react";

const Contact = () => {
  return (
    <section id="contact" className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-12 grid-rows-12 h-full w-full">
          {[...Array(144)].map((_, i) => (
            <div
              key={i}
              className="border border-white/10 animate-pulse"
              style={{ animationDelay: `${Math.random() * 3}s` }}
            />
          ))}
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${4 + Math.random() * 2}s`
            }}
          >
            <Sparkles className="h-4 w-4 text-blue-300/20" />
          </div>
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* AI Contact Header */}
        <div className="text-center max-w-4xl mx-auto mb-16 animate-fade-in">
          <Badge className="mb-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 text-sm font-medium">
            <Brain className="w-4 h-4 mr-2 animate-pulse" />
            Connect with AI Platform
          </Badge>
          <h2 className="text-4xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent">
            Get Started with AI Dairy Solutions
          </h2>
          <p className="text-xl text-blue-100 leading-relaxed">
            Ready to revolutionize your dairy operations? Connect with our AI platform team to explore
            intelligent solutions for monitoring, analytics, and optimization across Kolhapur district.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
                  <Brain className="w-6 h-6 text-white animate-pulse" />
                </div>
                <h3 className="text-3xl font-bold text-white">
                  गोकुळ AI Platform
                </h3>
              </div>
              <p className="text-lg text-blue-100 leading-relaxed">
                Your intelligent partner for revolutionary dairy operations in Kolhapur district.
                Connect with our AI-powered platform for advanced analytics, monitoring, and optimization solutions.
              </p>
            </div>

            {/* Contact Cards */}
            <div className="grid gap-4">
              <Card className="p-6 backdrop-blur-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300 animate-slide-up">
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <Phone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-2 flex items-center gap-2">
                      AI Support Hotline
                      <Bot className="w-4 h-4 text-blue-400 animate-bounce" />
                    </h4>
                    <p className="text-blue-200">+91 98765 43210</p>
                    <p className="text-blue-200">+91 87654 32109</p>
                    <p className="text-xs text-green-400 mt-1">24/7 AI-Powered Support</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 hover:shadow-lg transition-all duration-300 border-border">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-secondary/10 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-gray-800" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Email</h4>
                    <p className="text-gray-900"><EMAIL></p>
                    <p className="text-gray-900"><EMAIL></p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 hover:shadow-lg transition-all duration-300 border-border">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-dairy-green/10 rounded-xl flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-dairy-green" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Location</h4>
                    <p className="text-gray-900">Kolhapur, Maharashtra</p>
                    <p className="text-gray-900">India</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 hover:shadow-lg transition-all duration-300 border-border">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-dairy-orange/10 rounded-xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-dairy-orange" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Operating Hours</h4>
                    <p className="text-gray-900">Monday - Saturday: 6:00 AM - 8:00 PM</p>
                    <p className="text-gray-900">Sunday: 6:00 AM - 2:00 PM</p>
                  </div>
                </div>
              </Card>
            </div>

            {/* Quick Stats */}
            <div className="bg-gradient-to-r from-cream to-dairy-orange-light rounded-2xl p-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <Truck className="w-8 h-8 text-primary mx-auto mb-2" />
                  <div className="text-2xl font-bold text-primary">Daily</div>
                  <div className="text-sm text-gray-900">Delivery Available</div>
                </div>
                <div className="text-center">
                  <Star className="w-8 h-8 text-gray-800 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-800">4.9/5</div>
                  <div className="text-sm text-gray-900">Customer Rating</div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          {/* <Card className="p-8 border-border">
            <CardHeader className="px-0 pt-0">
              <CardTitle className="text-2xl text-primary">Send us a Message</CardTitle>
              <p className="text-gray-900">
                Fill out the form below and we'll get back to you as soon as possible.
              </p>
            </CardHeader>
            <CardContent className="px-0 pb-0">
              <form className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      First Name
                    </label>
                    <Input placeholder="Your first name" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Last Name
                    </label>
                    <Input placeholder="Your last name" />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Email
                  </label>
                  <Input type="email" placeholder="<EMAIL>" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Phone Number
                  </label>
                  <Input type="tel" placeholder="+91 98765 43210" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Subject
                  </label>
                  <Input placeholder="Order inquiry, General question, etc." />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Message
                  </label>
                  <Textarea 
                    placeholder="Tell us about your requirements or questions..." 
                    rows={5}
                  />
                </div>
                
                <Button type="submit" variant="outline" size="lg" className="w-full">
                  Send Message
                </Button>
                
                <p className="text-sm text-gray-900 text-center">
                  We typically respond within 2-4 hours during business hours.
                </p>
              </form>
            </CardContent>
          </Card> */}
        </div>
      </div>
    </section>
  );
};

export default Contact;
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Clock, Brain, Bot, Database, Sparkles, Zap, Activity, Star, Truck } from "lucide-react";

const Contact = () => {
  return (
    <section id="contact" className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-12 grid-rows-12 h-full w-full">
          {[...Array(144)].map((_, i) => (
            <div
              key={i}
              className="border border-white/10 animate-pulse"
              style={{ animationDelay: `${Math.random() * 3}s` }}
            />
          ))}
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${4 + Math.random() * 2}s`
            }}
          >
            <Sparkles className="h-4 w-4 text-blue-300/20" />
          </div>
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* AI Contact Header */}
        <div className="text-center max-w-4xl mx-auto mb-16 animate-fade-in">
          <Badge className="mb-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 text-sm font-medium">
            <Brain className="w-4 h-4 mr-2 animate-pulse" />
            Connect with AI Platform
          </Badge>
          <h2 className="text-4xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent">
            Get Started with AI Dairy Solutions
          </h2>
          <p className="text-xl text-blue-100 leading-relaxed">
            Ready to revolutionize your dairy operations? Connect with our AI platform team to explore
            intelligent solutions for monitoring, analytics, and optimization across Kolhapur district.
          </p>
        </div>

        <div className="grid lg:grid-cols-1 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
                  <Brain className="w-6 h-6 text-white animate-pulse" />
                </div>
                <h3 className="text-3xl font-bold text-white">
                  गोकुळ AI Platform
                </h3>
              </div>
              <p className="text-lg text-blue-100 leading-relaxed">
                Your intelligent partner for revolutionary dairy operations in Kolhapur district.
                Connect with our AI-powered platform for advanced analytics, monitoring, and optimization solutions.
              </p>
            </div>

            {/* Contact Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-6 backdrop-blur-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300 animate-slide-up">
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <Phone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-2 flex items-center gap-2">
                      AI Support Hotline
                      <Bot className="w-4 h-4 text-blue-400 animate-bounce" />
                    </h4>
                    <p className="text-blue-200">+91 98765 43210</p>
                    <p className="text-blue-200">+91 87654 32109</p>
                    <p className="text-xs text-green-400 mt-1">24/7 AI-Powered Support</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 backdrop-blur-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300 animate-slide-up" style={{ animationDelay: '0.1s' }}>
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-2 flex items-center gap-2">
                      AI Platform Access
                      <Database className="w-4 h-4 text-green-400 animate-pulse" />
                    </h4>
                    <p className="text-blue-200"><EMAIL></p>
                    <p className="text-blue-200"><EMAIL></p>
                    <p className="text-xs text-green-400 mt-1">Instant AI Response</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 backdrop-blur-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300 animate-slide-up" style={{ animationDelay: '0.2s' }}>
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-2 flex items-center gap-2">
                      AI Operations Center
                      <Activity className="w-4 h-4 text-purple-400 animate-pulse" />
                    </h4>
                    <p className="text-blue-200">Kolhapur District, Maharashtra</p>
                    <p className="text-blue-200">12 Talukas Coverage</p>
                    <p className="text-xs text-green-400 mt-1">Real-time Monitoring Active</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 backdrop-blur-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300 animate-slide-up" style={{ animationDelay: '0.3s' }}>
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white mb-2 flex items-center gap-2">
                      AI System Availability
                      <Zap className="w-4 h-4 text-yellow-400 animate-pulse" />
                    </h4>
                    <p className="text-blue-200">24/7 AI Monitoring</p>
                    <p className="text-blue-200">Real-time Analytics</p>
                    <p className="text-xs text-green-400 mt-1">99.9% Uptime Guaranteed</p>
                  </div>
                </div>
              </Card>
            </div>

            {/* AI Platform Stats */}
            <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="flex justify-center mb-3">
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
                      <Activity className="w-6 h-6 text-white animate-pulse" />
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-white">24/7</div>
                  <div className="text-sm text-blue-200">AI Monitoring</div>
                  <div className="text-xs text-green-400 mt-1">Always Active</div>
                </div>
                <div className="text-center">
                  <div className="flex justify-center mb-3">
                    <div className="p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-full">
                      <Star className="w-6 h-6 text-white animate-pulse" />
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-white">97.8%</div>
                  <div className="text-sm text-blue-200">AI Accuracy</div>
                  <div className="text-xs text-green-400 mt-1">Precision Rate</div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          {/* <Card className="p-8 border-border">
            <CardHeader className="px-0 pt-0">
              <CardTitle className="text-2xl text-primary">Send us a Message</CardTitle>
              <p className="text-gray-900">
                Fill out the form below and we'll get back to you as soon as possible.
              </p>
            </CardHeader>
            <CardContent className="px-0 pb-0">
              <form className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      First Name
                    </label>
                    <Input placeholder="Your first name" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Last Name
                    </label>
                    <Input placeholder="Your last name" />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Email
                  </label>
                  <Input type="email" placeholder="<EMAIL>" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Phone Number
                  </label>
                  <Input type="tel" placeholder="+91 98765 43210" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Subject
                  </label>
                  <Input placeholder="Order inquiry, General question, etc." />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Message
                  </label>
                  <Textarea 
                    placeholder="Tell us about your requirements or questions..." 
                    rows={5}
                  />
                </div>
                
                <Button type="submit" variant="outline" size="lg" className="w-full">
                  Send Message
                </Button>
                
                <p className="text-sm text-gray-900 text-center">
                  We typically respond within 2-4 hours during business hours.
                </p>
              </form>
            </CardContent>
          </Card> */}
        </div>
      </div>
    </section>
  );
};

export default Contact;
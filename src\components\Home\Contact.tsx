import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Clock, Truck, Star } from "lucide-react";

const Contact = () => {
  return (
    <section id="contact" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <Badge variant="outline" className="mb-4">Get in Touch</Badge>
          <h2 className="text-4xl lg:text-6xl font-bold text-primary mb-6">
            Contact Us Today
          </h2>
          <p className="text-xl text-gray-900">
            Ready to experience the finest dairy products? Get in touch with us for orders, inquiries, or to learn more about our quality processes.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-3xl font-bold text-primary mb-6">
                गोकुल दूध डेयरी
              </h3>
              <p className="text-lg text-gray-900 mb-8">
                Your trusted partner for fresh, high-quality dairy products in Kolhapur and surrounding areas. We're here to serve you with excellence.
              </p>
            </div>

            {/* Contact Cards */}
            <div className="grid gap-4">
              <Card className="p-6 hover:shadow-lg transition-all duration-300 border-border">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <Phone className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Phone</h4>
                    <p className="text-gray-900">+91 98765 43210</p>
                    <p className="text-gray-900">+91 87654 32109</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 hover:shadow-lg transition-all duration-300 border-border">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-secondary/10 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-gray-800" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Email</h4>
                    <p className="text-gray-900"><EMAIL></p>
                    <p className="text-gray-900"><EMAIL></p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 hover:shadow-lg transition-all duration-300 border-border">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-dairy-green/10 rounded-xl flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-dairy-green" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Location</h4>
                    <p className="text-gray-900">Kolhapur, Maharashtra</p>
                    <p className="text-gray-900">India</p>
                  </div>
                </div>
              </Card>

              <Card className="p-6 hover:shadow-lg transition-all duration-300 border-border">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-dairy-orange/10 rounded-xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-dairy-orange" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Operating Hours</h4>
                    <p className="text-gray-900">Monday - Saturday: 6:00 AM - 8:00 PM</p>
                    <p className="text-gray-900">Sunday: 6:00 AM - 2:00 PM</p>
                  </div>
                </div>
              </Card>
            </div>

            {/* Quick Stats */}
            <div className="bg-gradient-to-r from-cream to-dairy-orange-light rounded-2xl p-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <Truck className="w-8 h-8 text-primary mx-auto mb-2" />
                  <div className="text-2xl font-bold text-primary">Daily</div>
                  <div className="text-sm text-gray-900">Delivery Available</div>
                </div>
                <div className="text-center">
                  <Star className="w-8 h-8 text-gray-800 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-800">4.9/5</div>
                  <div className="text-sm text-gray-900">Customer Rating</div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <Card className="p-8 border-border">
            <CardHeader className="px-0 pt-0">
              <CardTitle className="text-2xl text-primary">Send us a Message</CardTitle>
              <p className="text-gray-900">
                Fill out the form below and we'll get back to you as soon as possible.
              </p>
            </CardHeader>
            <CardContent className="px-0 pb-0">
              <form className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      First Name
                    </label>
                    <Input placeholder="Your first name" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Last Name
                    </label>
                    <Input placeholder="Your last name" />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Email
                  </label>
                  <Input type="email" placeholder="<EMAIL>" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Phone Number
                  </label>
                  <Input type="tel" placeholder="+91 98765 43210" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Subject
                  </label>
                  <Input placeholder="Order inquiry, General question, etc." />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Message
                  </label>
                  <Textarea 
                    placeholder="Tell us about your requirements or questions..." 
                    rows={5}
                  />
                </div>
                
                <Button type="submit" variant="outline" size="lg" className="w-full">
                  Send Message
                </Button>
                
                <p className="text-sm text-gray-900 text-center">
                  We typically respond within 2-4 hours during business hours.
                </p>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default Contact;
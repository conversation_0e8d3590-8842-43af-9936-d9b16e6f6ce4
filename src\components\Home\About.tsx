import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Building2, Users, Award, TrendingUp, Shield, Heart } from "lucide-react";

const About = () => {
  const features = [
    {
      icon: Building2,
      title: "Modern Infrastructure",
      description: "State-of-the-art dairy processing facilities with advanced technology and quality control systems."
    },
    {
      icon: Users,
      title: "Expert Team",
      description: "Experienced professionals dedicated to maintaining the highest standards in dairy operations."
    },
    {
      icon: Award,
      title: "Quality Certified",
      description: "ISO 9001:2015 certified operations ensuring consistent quality and safety standards."
    },
    {
      icon: TrendingUp,
      title: "Continuous Growth",
      description: "Expanding operations across Kolhapur district with sustainable and scalable solutions."
    }
  ];

  const stats = [
    { number: "25+", label: "Years Experience" },
    { number: "234K+", label: "Animals Monitored" },
    { number: "6K+", label: "Centers Connected" },
    { number: "240K+", label: "Daily Production (L)" }
  ];

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center max-w-4xl mx-auto mb-16">
          <Badge className="mb-6 bg-green-100 text-green-800 px-6 py-2 text-sm font-medium">
            <Shield className="w-4 h-4 mr-2" />
            About Us
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-800">
            Leading Dairy Excellence in Maharashtra
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            गोकुळ दूध डेयरी has been serving the Kolhapur region with premium dairy products and 
            professional management solutions, combining traditional values with modern technology.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-blue-600 mb-2">
                {stat.number}
              </div>
              <div className="text-sm text-gray-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Content */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-3xl font-bold text-gray-800">
                Committed to Quality & Innovation
              </h3>
              <p className="text-lg text-gray-600 leading-relaxed">
                Our journey began with a simple mission: to provide the finest dairy products 
                while supporting local farmers and communities. Today, we've grown into a 
                comprehensive dairy management platform serving the entire Kolhapur district.
              </p>
              <p className="text-gray-600 leading-relaxed">
                We combine time-tested dairy practices with cutting-edge technology to ensure 
                every drop of milk meets the highest quality standards. Our commitment extends 
                beyond products to building sustainable relationships with farmers and communities.
              </p>
            </div>

            {/* Mission & Values */}
            <div className="bg-blue-50 p-6 rounded-xl border border-blue-100">
              <div className="flex items-center gap-3 mb-4">
                <Heart className="h-6 w-6 text-blue-600" />
                <h4 className="text-xl font-semibold text-gray-800">Our Mission</h4>
              </div>
              <p className="text-gray-700 leading-relaxed">
                To revolutionize dairy farming through technology-driven solutions while 
                preserving the traditional values of quality, trust, and community support 
                that have defined our heritage.
              </p>
            </div>
          </div>

          {/* Right Content - Features */}
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-800 mb-6">
              Why Choose गोकुळ दूध डेयरी
            </h3>
            <div className="grid gap-6">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <Card key={index} className="border border-gray-200 hover:shadow-md transition-shadow duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="p-3 bg-blue-100 rounded-lg flex-shrink-0">
                          <IconComponent className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-gray-800 mb-2">
                            {feature.title}
                          </h4>
                          <p className="text-gray-600 leading-relaxed">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center bg-gray-50 p-8 rounded-2xl">
          <h3 className="text-2xl font-bold text-gray-800 mb-4">
            Ready to Experience Premium Dairy Solutions?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Join thousands of satisfied customers who trust गोकुळ दूध डेयरी for their dairy needs. 
            Contact us today to learn more about our products and services.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#contact"
              className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-300"
            >
              Get in Touch
            </a>
            <a
              href="#products"
              className="inline-flex items-center justify-center px-6 py-3 border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold rounded-lg transition-colors duration-300"
            >
              View Our Solutions
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;

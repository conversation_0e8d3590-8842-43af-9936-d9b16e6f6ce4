
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Settings, 
  AlertTriangle, 
  CheckCircle,
  TrendingUp,
  Activity,
  Thermometer,
  Gauge,
  Brain,
  Wrench
} from "lucide-react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const ProcessingDepartment = () => {
  const [selectedShift, setSelectedShift] = useState("current");

  const processingUnits = [
    {
      unit: "Pasteurizer Unit 1",
      status: "running",
      temperature: 72.5,
      targetTemp: 72,
      pressure: 2.3,
      targetPressure: 2.5,
      efficiency: 94.2,
      lastMaintenance: "2 days ago",
      nextMaintenance: "5 days",
      aiPrediction: "Normal operation, optimal efficiency"
    },
    {
      unit: "Pasteurizer Unit 2", 
      status: "warning",
      temperature: 71.8,
      targetTemp: 72,
      pressure: 2.1,
      targetPressure: 2.5,
      efficiency: 88.7,
      lastMaintenance: "8 days ago",
      nextMaintenance: "2 days",
      aiPrediction: "Pump bearing wear detected, maintenance required"
    },
    {
      unit: "Separator Unit",
      status: "running",
      temperature: 45.2,
      targetTemp: 45,
      pressure: 3.2,
      targetPressure: 3.0,
      efficiency: 96.8,
      lastMaintenance: "1 day ago",
      nextMaintenance: "14 days",
      aiPrediction: "Excellent performance, no issues detected"
    },
    {
      unit: "Homogenizer",
      status: "running",
      temperature: 65.0,
      targetTemp: 65,
      pressure: 15.2,
      targetPressure: 15.0,
      efficiency: 92.1,
      lastMaintenance: "4 days ago",
      nextMaintenance: "10 days",
      aiPrediction: "Normal operation with slight efficiency decline"
    }
  ];

  const shiftData = [
    {
      shift: "Morning (6AM-2PM)",
      current: selectedShift === "morning",
      processed: 5800,
      target: 6000,
      quality: 96.2,
      efficiency: 94.1,
      issues: 2
    },
    {
      shift: "Evening (2PM-10PM)",
      current: selectedShift === "evening", 
      processed: 5950,
      target: 6000,
      quality: 97.1,
      efficiency: 96.8,
      issues: 1
    },
    {
      shift: "Night (10PM-6AM)",
      current: selectedShift === "night",
      processed: 5200,
      target: 5500,
      quality: 95.8,
      efficiency: 93.2,
      issues: 3
    }
  ];

  const maintenancePredictions = [
    {
      equipment: "Pasteurizer Unit 2 - Pump",
      urgency: "high",
      predictedDate: "Next 48 hours",
      confidence: 92,
      issue: "Bearing wear detected",
      action: "Replace pump bearings"
    },
    {
      equipment: "Separator - Motor",
      urgency: "medium",
      predictedDate: "Next 7 days",
      confidence: 78,
      issue: "Vibration increase detected",
      action: "Motor alignment check"
    },
    {
      equipment: "Heat Exchanger - Plates",
      urgency: "low",
      predictedDate: "Next 15 days",
      confidence: 65,
      issue: "Efficiency decline trend",
      action: "Cleaning and inspection"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Chart Data
  const processingEfficiencyData = {
    labels: ['6 AM', '9 AM', '12 PM', '3 PM', '6 PM', '9 PM', '12 AM', '3 AM'],
    datasets: [
      {
        label: 'Pasteurizer 1 (%)',
        data: [94.2, 95.1, 94.8, 93.5, 94.7, 95.2, 94.0, 93.8],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Pasteurizer 2 (%)',
        data: [88.7, 89.2, 87.5, 86.8, 88.1, 89.5, 87.9, 88.3],
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Separator (%)',
        data: [91.5, 92.1, 91.8, 90.9, 91.3, 92.4, 91.0, 91.7],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true,
      }
    ],
  };

  const temperatureTrendData = {
    labels: ['6 AM', '9 AM', '12 PM', '3 PM', '6 PM', '9 PM', '12 AM', '3 AM'],
    datasets: [
      {
        label: 'Pasteurizer 1 (°C)',
        data: [72.5, 72.3, 72.7, 72.1, 72.4, 72.6, 72.2, 72.5],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Target Temperature (°C)',
        data: [72, 72, 72, 72, 72, 72, 72, 72],
        borderColor: 'rgb(156, 163, 175)',
        backgroundColor: 'rgba(156, 163, 175, 0.1)',
        borderDash: [5, 5],
        tension: 0.4,
      }
    ],
  };

  const maintenanceStatusData = {
    labels: ['Completed', 'Scheduled', 'Overdue', 'In Progress'],
    datasets: [
      {
        data: [65, 20, 10, 5],
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 158, 11, 0.8)',
        ],
        borderColor: [
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
          'rgb(239, 68, 68)',
          'rgb(245, 158, 11)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const throughputAnalysisData = {
    labels: ['Pasteurizer 1', 'Pasteurizer 2', 'Separator', 'Homogenizer', 'Cooler'],
    datasets: [
      {
        label: 'Current Throughput (L/hr)',
        data: [2500, 2200, 2800, 2400, 2600],
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
      },
      {
        label: 'Capacity (L/hr)',
        data: [3000, 2800, 3200, 2800, 3000],
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: false,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header with Shift Selection */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Processing Department</h2>
          <p className="text-sm text-gray-600">Predictive maintenance and real-time processing monitoring</p>
        </div>
        <div className="flex items-center space-x-4">
          <select 
            value={selectedShift}
            onChange={(e) => setSelectedShift(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="current">Current Shift</option>
            <option value="morning">Morning Shift</option>
            <option value="evening">Evening Shift</option> 
            <option value="night">Night Shift</option>
          </select>
        </div>
      </div>

      {/* Shift Performance Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-blue-600" />
            <span>Shift-wise Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {shiftData.map((shift, index) => (
              <div key={index} className={`p-4 rounded-lg border-2 ${
                shift.current ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">{shift.shift}</h3>
                  {shift.current && <Badge className="bg-blue-500 text-white">Current</Badge>}
                </div>
                
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Processed: {shift.processed}L</span>
                      <span>Target: {shift.target}L</span>
                    </div>
                    <Progress value={(shift.processed / shift.target) * 100} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Quality Score</span>
                      <p className="font-semibold text-green-600">{shift.quality}%</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Efficiency</span>
                      <p className="font-semibold text-blue-600">{shift.efficiency}%</p>
                    </div>
                  </div>
                  
                  <div className="text-sm">
                    <span className="text-gray-600">Issues: </span>
                    <span className={`font-semibold ${shift.issues > 2 ? 'text-red-600' : 'text-green-600'}`}>
                      {shift.issues}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Processing Units Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-sky-600" />
            <span>Processing Units Real-time Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {processingUnits.map((unit, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-sky-100 p-2 rounded-lg">
                      <Settings className="h-5 w-5 text-sky-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{unit.unit}</h3>
                      <p className="text-sm text-gray-600">Efficiency: {unit.efficiency}%</p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(unit.status)}>
                    {unit.status.toUpperCase()}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <Thermometer className="h-4 w-4 text-orange-500" />
                    <div>
                      <p className="text-sm text-gray-600">Temperature</p>
                      <p className="font-medium">{unit.temperature}°C / {unit.targetTemp}°C</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Gauge className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="text-sm text-gray-600">Pressure</p>
                      <p className="font-medium">{unit.pressure} / {unit.targetPressure} bar</p>
                    </div>
                  </div>
                </div>

                {/* AI Prediction */}
                <div className="bg-purple-50 p-3 rounded-lg mb-3">
                  <div className="flex items-start space-x-2">
                    <Brain className="h-4 w-4 text-purple-600 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-purple-900">AI Analysis</h4>
                      <p className="text-sm text-purple-800">{unit.aiPrediction}</p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between text-sm text-gray-500">
                  <span>Last Maintenance: {unit.lastMaintenance}</span>
                  <span>Next: {unit.nextMaintenance}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Predictive Maintenance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Wrench className="h-5 w-5 text-orange-600" />
            <span>AI-Powered Predictive Maintenance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {maintenancePredictions.map((prediction, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className={`p-2 rounded-lg ${
                    prediction.urgency === 'high' ? 'bg-red-100' :
                    prediction.urgency === 'medium' ? 'bg-yellow-100' :
                    'bg-green-100'
                  }`}>
                    <AlertTriangle className={`h-5 w-5 ${
                      prediction.urgency === 'high' ? 'text-red-600' :
                      prediction.urgency === 'medium' ? 'text-yellow-600' :
                      'text-green-600'
                    }`} />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{prediction.equipment}</h4>
                    <p className="text-sm text-gray-600">{prediction.issue}</p>
                    <p className="text-sm text-blue-600">{prediction.action}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <Badge className={getUrgencyColor(prediction.urgency)}>
                    {prediction.urgency.toUpperCase()}
                  </Badge>
                  <p className="text-sm text-gray-600 mt-1">{prediction.predictedDate}</p>
                  <p className="text-xs text-gray-500">AI Confidence: {prediction.confidence}%</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Processing Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Processing Efficiency Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span>Processing Efficiency Trend</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={processingEfficiencyData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Temperature Monitoring */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Thermometer className="h-5 w-5 text-red-600" />
              <span>Temperature Monitoring</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={temperatureTrendData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Maintenance & Throughput Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Maintenance Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Wrench className="h-5 w-5 text-purple-600" />
              <span>Maintenance Status Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Doughnut data={maintenanceStatusData} options={pieChartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Throughput Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Gauge className="h-5 w-5 text-green-600" />
              <span>Equipment Throughput Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Bar data={throughputAnalysisData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProcessingDepartment;

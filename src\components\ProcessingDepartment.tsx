
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Settings, 
  AlertTriangle, 
  CheckCircle,
  TrendingUp,
  Activity,
  Thermometer,
  Gauge,
  Brain,
  Wrench
} from "lucide-react";

const ProcessingDepartment = () => {
  const [selectedShift, setSelectedShift] = useState("current");

  const processingUnits = [
    {
      unit: "Pasteurizer Unit 1",
      status: "running",
      temperature: 72.5,
      targetTemp: 72,
      pressure: 2.3,
      targetPressure: 2.5,
      efficiency: 94.2,
      lastMaintenance: "2 days ago",
      nextMaintenance: "5 days",
      aiPrediction: "Normal operation, optimal efficiency"
    },
    {
      unit: "Pasteurizer Unit 2", 
      status: "warning",
      temperature: 71.8,
      targetTemp: 72,
      pressure: 2.1,
      targetPressure: 2.5,
      efficiency: 88.7,
      lastMaintenance: "8 days ago",
      nextMaintenance: "2 days",
      aiPrediction: "Pump bearing wear detected, maintenance required"
    },
    {
      unit: "Separator Unit",
      status: "running",
      temperature: 45.2,
      targetTemp: 45,
      pressure: 3.2,
      targetPressure: 3.0,
      efficiency: 96.8,
      lastMaintenance: "1 day ago",
      nextMaintenance: "14 days",
      aiPrediction: "Excellent performance, no issues detected"
    },
    {
      unit: "Homogenizer",
      status: "running",
      temperature: 65.0,
      targetTemp: 65,
      pressure: 15.2,
      targetPressure: 15.0,
      efficiency: 92.1,
      lastMaintenance: "4 days ago",
      nextMaintenance: "10 days",
      aiPrediction: "Normal operation with slight efficiency decline"
    }
  ];

  const shiftData = [
    {
      shift: "Morning (6AM-2PM)",
      current: selectedShift === "morning",
      processed: 5800,
      target: 6000,
      quality: 96.2,
      efficiency: 94.1,
      issues: 2
    },
    {
      shift: "Evening (2PM-10PM)",
      current: selectedShift === "evening", 
      processed: 5950,
      target: 6000,
      quality: 97.1,
      efficiency: 96.8,
      issues: 1
    },
    {
      shift: "Night (10PM-6AM)",
      current: selectedShift === "night",
      processed: 5200,
      target: 5500,
      quality: 95.8,
      efficiency: 93.2,
      issues: 3
    }
  ];

  const maintenancePredictions = [
    {
      equipment: "Pasteurizer Unit 2 - Pump",
      urgency: "high",
      predictedDate: "Next 48 hours",
      confidence: 92,
      issue: "Bearing wear detected",
      action: "Replace pump bearings"
    },
    {
      equipment: "Separator - Motor",
      urgency: "medium",
      predictedDate: "Next 7 days",
      confidence: 78,
      issue: "Vibration increase detected",
      action: "Motor alignment check"
    },
    {
      equipment: "Heat Exchanger - Plates",
      urgency: "low",
      predictedDate: "Next 15 days",
      confidence: 65,
      issue: "Efficiency decline trend",
      action: "Cleaning and inspection"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Shift Selection */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Processing Department</h2>
          <p className="text-sm text-gray-600">Predictive maintenance and real-time processing monitoring</p>
        </div>
        <div className="flex items-center space-x-4">
          <select 
            value={selectedShift}
            onChange={(e) => setSelectedShift(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="current">Current Shift</option>
            <option value="morning">Morning Shift</option>
            <option value="evening">Evening Shift</option> 
            <option value="night">Night Shift</option>
          </select>
        </div>
      </div>

      {/* Shift Performance Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-blue-600" />
            <span>Shift-wise Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {shiftData.map((shift, index) => (
              <div key={index} className={`p-4 rounded-lg border-2 ${
                shift.current ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">{shift.shift}</h3>
                  {shift.current && <Badge className="bg-blue-500 text-white">Current</Badge>}
                </div>
                
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Processed: {shift.processed}L</span>
                      <span>Target: {shift.target}L</span>
                    </div>
                    <Progress value={(shift.processed / shift.target) * 100} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Quality Score</span>
                      <p className="font-semibold text-green-600">{shift.quality}%</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Efficiency</span>
                      <p className="font-semibold text-blue-600">{shift.efficiency}%</p>
                    </div>
                  </div>
                  
                  <div className="text-sm">
                    <span className="text-gray-600">Issues: </span>
                    <span className={`font-semibold ${shift.issues > 2 ? 'text-red-600' : 'text-green-600'}`}>
                      {shift.issues}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Processing Units Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-sky-600" />
            <span>Processing Units Real-time Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {processingUnits.map((unit, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-sky-100 p-2 rounded-lg">
                      <Settings className="h-5 w-5 text-sky-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{unit.unit}</h3>
                      <p className="text-sm text-gray-600">Efficiency: {unit.efficiency}%</p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(unit.status)}>
                    {unit.status.toUpperCase()}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <Thermometer className="h-4 w-4 text-orange-500" />
                    <div>
                      <p className="text-sm text-gray-600">Temperature</p>
                      <p className="font-medium">{unit.temperature}°C / {unit.targetTemp}°C</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Gauge className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="text-sm text-gray-600">Pressure</p>
                      <p className="font-medium">{unit.pressure} / {unit.targetPressure} bar</p>
                    </div>
                  </div>
                </div>

                {/* AI Prediction */}
                <div className="bg-purple-50 p-3 rounded-lg mb-3">
                  <div className="flex items-start space-x-2">
                    <Brain className="h-4 w-4 text-purple-600 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-purple-900">AI Analysis</h4>
                      <p className="text-sm text-purple-800">{unit.aiPrediction}</p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between text-sm text-gray-500">
                  <span>Last Maintenance: {unit.lastMaintenance}</span>
                  <span>Next: {unit.nextMaintenance}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Predictive Maintenance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Wrench className="h-5 w-5 text-orange-600" />
            <span>AI-Powered Predictive Maintenance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {maintenancePredictions.map((prediction, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className={`p-2 rounded-lg ${
                    prediction.urgency === 'high' ? 'bg-red-100' :
                    prediction.urgency === 'medium' ? 'bg-yellow-100' :
                    'bg-green-100'
                  }`}>
                    <AlertTriangle className={`h-5 w-5 ${
                      prediction.urgency === 'high' ? 'text-red-600' :
                      prediction.urgency === 'medium' ? 'text-yellow-600' :
                      'text-green-600'
                    }`} />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{prediction.equipment}</h4>
                    <p className="text-sm text-gray-600">{prediction.issue}</p>
                    <p className="text-sm text-blue-600">{prediction.action}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <Badge className={getUrgencyColor(prediction.urgency)}>
                    {prediction.urgency.toUpperCase()}
                  </Badge>
                  <p className="text-sm text-gray-600 mt-1">{prediction.predictedDate}</p>
                  <p className="text-xs text-gray-500">AI Confidence: {prediction.confidence}%</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProcessingDepartment;

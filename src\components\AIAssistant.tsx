
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Bot, 
  X, 
  Send, 
  Lightbulb,
  TrendingUp,
  AlertTriangle,
  MessageSquare
} from "lucide-react";

interface AIAssistantProps {
  onClose: () => void;
}

const AIAssistant = ({ onClose }: AIAssistantProps) => {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: "ai",
      content: "Hello! I'm your AI operations assistant. I can help you with quality analysis, production optimization, and predictive insights. What would you like to know?",
      time: "now"
    }
  ]);

  const quickActions = [
    {
      title: "Quality Analysis",
      description: "Analyze today's quality metrics",
      icon: <AlertTriangle className="h-4 w-4" />,
      action: "Show me quality issues from the last 24 hours"
    },
    {
      title: "Production Forecast",
      description: "Predict tomorrow's production",
      icon: <TrendingUp className="h-4 w-4" />,
      action: "What's the production forecast for tomorrow?"
    },
    {
      title: "Energy Optimization",
      description: "Get energy saving recommendations",
      icon: <Lightbulb className="h-4 w-4" />,
      action: "How can we optimize energy consumption today?"
    }
  ];

  const sampleResponses = {
    "Show me quality issues from the last 24 hours": {
      type: "analysis",
      content: "🔍 Quality Analysis Summary (Last 24 Hours):\n\n• Packaging Line 3: Seal defect rate 2.1% (↑0.8% from yesterday)\n• Milk SNF levels: 8.2% average (Target: 8.5%)\n• 3 batches flagged for review\n\n💡 AI Recommendation: Adjust sealing temperature by 2°C and recalibrate SNF sensors on Line 2."
    },
    "What's the production forecast for tomorrow?": {
      type: "forecast",
      content: "📊 Tomorrow's Production Forecast:\n\n• Milk Processing: 16.8L litres (98% of target)\n• Peak hours: 8-10 AM, 2-4 PM\n• Weather impact: Minimal\n• Demand spike expected for Ghee (+12%)\n\n⚠️ Alert: Schedule maintenance for Line 2 after 6 PM to avoid peak hours."
    },
    "How can we optimize energy consumption today?": {
      type: "optimization",
      content: "⚡ Energy Optimization Opportunities:\n\n• Solar peak utilization: 11 AM - 3 PM (87% efficiency)\n• Cold storage cycling: Reduce by 8% using predictive cooling\n• Potential savings: ₹15,000/day\n\n🎯 Action: Shift energy-intensive operations to solar peak hours."
    }
  };

  const handleSend = () => {
    if (!message.trim()) return;

    const userMessage = {
      id: messages.length + 1,
      type: "user",
      content: message,
      time: "now"
    };

    const aiResponse = sampleResponses[message as keyof typeof sampleResponses] || {
      type: "general",
      content: "I understand your question. Based on current data analysis, I recommend checking the specific department dashboard for detailed insights. Would you like me to guide you to the relevant section?"
    };

    const aiMessage = {
      id: messages.length + 2,
      type: "ai",
      content: aiResponse.content,
      time: "now"
    };

    setMessages([...messages, userMessage, aiMessage]);
    setMessage("");
  };

  const handleQuickAction = (action: string) => {
    const userMessage = {
      id: messages.length + 1,
      type: "user",
      content: action,
      time: "now"
    };

    const aiResponse = sampleResponses[action as keyof typeof sampleResponses] || {
      type: "general",
      content: "I understand your question. Based on current data analysis, I recommend checking the specific department dashboard for detailed insights. Would you like me to guide you to the relevant section?"
    };

    const aiMessage = {
      id: messages.length + 2,
      type: "ai",
      content: aiResponse.content,
      time: "now"
    };

    setMessages([...messages, userMessage, aiMessage]);
  };

  return (
    <Card className="w-full h-full flex flex-col shadow-2xl">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 bg-sky-50 rounded-t-lg">
        <CardTitle className="flex items-center space-x-2">
          <div className="bg-sky-600 p-2 rounded-full">
            <Bot className="h-4 w-4 text-white" />
          </div>
          <div>
            <span className="text-sky-800">AI Assistant</span>
            <Badge className="ml-2 bg-green-500 text-white text-xs">Online</Badge>
          </div>
        </CardTitle>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>

      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Quick Actions - Compact */}
        <div className="p-3 border-b border-gray-200 bg-gray-50">
          <p className="text-xs font-medium text-gray-700 mb-2">Quick Actions:</p>
          <div className="flex gap-2 overflow-x-auto">
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={() => handleQuickAction(action.action)}
                className="flex-shrink-0 p-2 text-left border border-gray-200 rounded-lg hover:border-sky-300 hover:bg-sky-50 transition-colors min-w-[120px]"
              >
                <div className="flex items-center space-x-2">
                  {action.icon}
                  <div>
                    <p className="text-xs font-medium text-gray-900">{action.title}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Messages - Scrollable */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((msg) => (
            <div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[85%] p-3 rounded-lg ${
                msg.type === 'user' 
                  ? 'bg-sky-600 text-white' 
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <div className="flex items-center space-x-2 mb-1">
                  {msg.type === 'ai' && <MessageSquare className="h-3 w-3" />}
                  <span className="text-xs opacity-75">
                    {msg.type === 'ai' ? 'AI Assistant' : 'You'}
                  </span>
                </div>
                <p className="text-sm whitespace-pre-line">{msg.content}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Input - Fixed at bottom */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <div className="flex space-x-2">
          <Input
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Ask about production, quality, energy..."
            onKeyPress={(e) => e.key === 'Enter' && handleSend()}
            className="flex-1"
          />
          <Button onClick={handleSend} className="bg-sky-600 hover:bg-sky-700">
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default AIAssistant;

import { useState } from "react";
import { <PERSON>, <PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  MapPin, 
  Building2, 
  Cow, 
  Users, 
  TrendingUp, 
  BarChart3,
  Filter,
  Eye
} from "lucide-react";

// Mock data for regions and sansthas
const mockData = {
  regions: [
    {
      id: "kolhapur",
      name: "Kolhapur",
      sansthas: [
        {
          id: "dca-kol-1",
          name: "Kolhapur DCA Central",
          animals: { cows: 450, buffalos: 320, goats: 180 },
          status: "active"
        },
        {
          id: "dca-kol-2", 
          name: "Kolhapur DCA South",
          animals: { cows: 380, buffalos: 290, goats: 150 },
          status: "active"
        }
      ]
    },
    {
      id: "sangli",
      name: "<PERSON><PERSON>",
      sansthas: [
        {
          id: "dca-san-1",
          name: "<PERSON><PERSON> DCA East",
          animals: { cows: 520, buffalos: 410, goats: 220 },
          status: "active"
        },
        {
          id: "dca-san-2",
          name: "Sangli DCA West", 
          animals: { cows: 340, buffalos: 280, goats: 160 },
          status: "maintenance"
        }
      ]
    },
    {
      id: "satara",
      name: "Satara",
      sansthas: [
        {
          id: "dca-sat-1",
          name: "Satara DCA North",
          animals: { cows: 420, buffalos: 350, goats: 190 },
          status: "active"
        }
      ]
    }
  ]
};

const RegionSansthaOverview = () => {
  const [selectedRegion, setSelectedRegion] = useState<string>("all");
  const [selectedSanstha, setSelectedSanstha] = useState<string>("all");

  // Calculate totals
  const calculateTotals = () => {
    let totalCows = 0;
    let totalBuffalos = 0;
    let totalGoats = 0;
    let totalSansthas = 0;

    mockData.regions.forEach(region => {
      if (selectedRegion === "all" || selectedRegion === region.id) {
        region.sansthas.forEach(sanstha => {
          if (selectedSanstha === "all" || selectedSanstha === sanstha.id) {
            totalCows += sanstha.animals.cows;
            totalBuffalos += sanstha.animals.buffalos;
            totalGoats += sanstha.animals.goats;
            totalSansthas++;
          }
        });
      }
    });

    return { totalCows, totalBuffalos, totalGoats, totalSansthas };
  };

  const totals = calculateTotals();

  const getFilteredData = () => {
    if (selectedRegion === "all") {
      return mockData.regions;
    }
    return mockData.regions.filter(region => region.id === selectedRegion);
  };

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Region & Sanstha Overview</h2>
          <p className="text-gray-600">Monitor animal inventory across regions and organizations</p>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={selectedRegion} onValueChange={setSelectedRegion}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select Region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Regions</SelectItem>
                {mockData.regions.map(region => (
                  <SelectItem key={region.id} value={region.id}>
                    {region.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Total Cows</p>
                <p className="text-3xl font-bold text-blue-900">🐄 {totals.totalCows.toLocaleString()}</p>
              </div>
              <div className="bg-blue-200 p-3 rounded-full">
                <Cow className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Total Buffalos</p>
                <p className="text-3xl font-bold text-green-900">🐃 {totals.totalBuffalos.toLocaleString()}</p>
              </div>
              <div className="bg-green-200 p-3 rounded-full">
                <Cow className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">Total Goats</p>
                <p className="text-3xl font-bold text-orange-900">🐐 {totals.totalGoats.toLocaleString()}</p>
              </div>
              <div className="bg-orange-200 p-3 rounded-full">
                <Users className="h-6 w-6 text-orange-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Active Sansthas</p>
                <p className="text-3xl font-bold text-purple-900">{totals.totalSansthas}</p>
              </div>
              <div className="bg-purple-200 p-3 rounded-full">
                <Building2 className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Regional Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {getFilteredData().map(region => (
          <Card key={region.id} className="shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  {region.name} Region
                </CardTitle>
                <Badge variant="outline" className="text-xs">
                  {region.sansthas.length} Sansthas
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {region.sansthas.map(sanstha => (
                <div key={sanstha.id} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">{sanstha.name}</h4>
                    <Badge 
                      variant={sanstha.status === "active" ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {sanstha.status}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-3 text-sm">
                    <div className="text-center">
                      <div className="text-blue-600 font-semibold">🐄 {sanstha.animals.cows}</div>
                      <div className="text-gray-500 text-xs">Cows</div>
                    </div>
                    <div className="text-center">
                      <div className="text-green-600 font-semibold">🐃 {sanstha.animals.buffalos}</div>
                      <div className="text-gray-500 text-xs">Buffalos</div>
                    </div>
                    <div className="text-center">
                      <div className="text-orange-600 font-semibold">🐐 {sanstha.animals.goats}</div>
                      <div className="text-gray-500 text-xs">Goats</div>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <Button variant="outline" size="sm" className="w-full">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default RegionSansthaOverview;

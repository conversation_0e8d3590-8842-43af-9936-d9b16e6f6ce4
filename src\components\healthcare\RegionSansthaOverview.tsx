import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  MapPin,
  Building2,
  Users,
  TrendingUp,
  BarChart3,
  Filter,
  Eye,
  Heart,
  Search,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

// Mock data for Kolhapur district talukas - representing 6000 sansthas across talukas
const mockData = {
  regions: [
    {
      id: "kolhapur-city",
      name: "Kolhapur City",
      totalSansthas: 850,
      activeSansthas: 820,
      animals: { cows: 32000, buffalos: 28000 },
      avgAnimalsPerSanstha: { cows: 37.6, buffalos: 32.9 },
      topPerformingSansthas: [
        {
          id: "dca-kol-city-1",
          name: "Kolhapur City DCA Central",
          animals: { cows: 450, buffalos: 380 },
          status: "active",
          efficiency: 96.2
        },
        {
          id: "dca-kol-city-2",
          name: "Kolhapur City DCA South",
          animals: { cows: 420, buffalos: 350 },
          status: "active",
          efficiency: 94.8
        }
      ]
    },
    {
      id: "panhala",
      name: "Panhala",
      totalSansthas: 720,
      activeSansthas: 695,
      animals: { cows: 28000, buffalos: 22000 },
      avgAnimalsPerSanstha: { cows: 38.9, buffalos: 30.6 },
      topPerformingSansthas: [
        {
          id: "dca-pan-1",
          name: "Panhala DCA Main",
          animals: { cows: 480, buffalos: 390 },
          status: "active",
          efficiency: 95.1
        },
        {
          id: "dca-pan-2",
          name: "Panhala DCA Hills",
          animals: { cows: 380, buffalos: 320 },
          status: "active",
          efficiency: 92.3
        }
      ]
    },
    {
      id: "shirol",
      name: "Shirol",
      totalSansthas: 950,
      activeSansthas: 920,
      animals: { cows: 38000, buffalos: 31000 },
      avgAnimalsPerSanstha: { cows: 40.0, buffalos: 32.6 },
      topPerformingSansthas: [
        {
          id: "dca-shi-1",
          name: "Shirol DCA Central",
          animals: { cows: 520, buffalos: 420 },
          status: "active",
          efficiency: 97.1
        }
      ]
    },
    {
      id: "hatkanangle",
      name: "Hatkanangle",
      totalSansthas: 680,
      activeSansthas: 650,
      animals: { cows: 26000, buffalos: 20000 },
      avgAnimalsPerSanstha: { cows: 38.2, buffalos: 29.4 },
      topPerformingSansthas: [
        {
          id: "dca-hat-1",
          name: "Hatkanangle DCA Main",
          animals: { cows: 410, buffalos: 330 },
          status: "active",
          efficiency: 93.8
        }
      ]
    },
    {
      id: "shahuwadi",
      name: "Shahuwadi",
      totalSansthas: 580,
      activeSansthas: 560,
      animals: { cows: 22000, buffalos: 18000 },
      avgAnimalsPerSanstha: { cows: 37.9, buffalos: 31.0 },
      topPerformingSansthas: [
        {
          id: "dca-sha-1",
          name: "Shahuwadi DCA Central",
          animals: { cows: 390, buffalos: 310 },
          status: "active",
          efficiency: 94.5
        }
      ]
    },
    {
      id: "kagal",
      name: "Kagal",
      totalSansthas: 620,
      activeSansthas: 595,
      animals: { cows: 24000, buffalos: 19000 },
      avgAnimalsPerSanstha: { cows: 38.7, buffalos: 30.6 },
      topPerformingSansthas: [
        {
          id: "dca-kag-1",
          name: "Kagal DCA Main",
          animals: { cows: 430, buffalos: 340 },
          status: "active",
          efficiency: 95.7
        }
      ]
    },
    {
      id: "bhudargad",
      name: "Bhudargad",
      totalSansthas: 450,
      activeSansthas: 430,
      animals: { cows: 18000, buffalos: 14000 },
      avgAnimalsPerSanstha: { cows: 40.0, buffalos: 31.1 },
      topPerformingSansthas: [
        {
          id: "dca-bhu-1",
          name: "Bhudargad DCA Hills",
          animals: { cows: 380, buffalos: 290 },
          status: "active",
          efficiency: 92.1
        }
      ]
    },
    {
      id: "radhanagari",
      name: "Radhanagari",
      totalSansthas: 380,
      activeSansthas: 365,
      animals: { cows: 15000, buffalos: 12000 },
      avgAnimalsPerSanstha: { cows: 39.5, buffalos: 31.6 },
      topPerformingSansthas: [
        {
          id: "dca-rad-1",
          name: "Radhanagari DCA Forest",
          animals: { cows: 350, buffalos: 280 },
          status: "active",
          efficiency: 91.8
        }
      ]
    },
    {
      id: "ajra",
      name: "Ajra",
      totalSansthas: 420,
      activeSansthas: 400,
      animals: { cows: 16000, buffalos: 13000 },
      avgAnimalsPerSanstha: { cows: 38.1, buffalos: 31.0 },
      topPerformingSansthas: [
        {
          id: "dca-ajr-1",
          name: "Ajra DCA Central",
          animals: { cows: 370, buffalos: 300 },
          status: "active",
          efficiency: 93.2
        }
      ]
    },
    {
      id: "chandgad",
      name: "Chandgad",
      totalSansthas: 350,
      activeSansthas: 335,
      animals: { cows: 14000, buffalos: 11000 },
      avgAnimalsPerSanstha: { cows: 40.0, buffalos: 31.4 },
      topPerformingSansthas: [
        {
          id: "dca-cha-1",
          name: "Chandgad DCA Border",
          animals: { cows: 340, buffalos: 270 },
          status: "active",
          efficiency: 90.5
        }
      ]
    },
    {
      id: "gaganbawada",
      name: "Gaganbawada",
      totalSansthas: 300,
      activeSansthas: 285,
      animals: { cows: 12000, buffalos: 9000 },
      avgAnimalsPerSanstha: { cows: 40.0, buffalos: 30.0 },
      topPerformingSansthas: [
        {
          id: "dca-gag-1",
          name: "Gaganbawada DCA Tribal",
          animals: { cows: 320, buffalos: 250 },
          status: "active",
          efficiency: 89.7
        }
      ]
    },
    {
      id: "karvir",
      name: "Karvir",
      totalSansthas: 700,
      activeSansthas: 680,
      animals: { cows: 28000, buffalos: 22000 },
      avgAnimalsPerSanstha: { cows: 40.0, buffalos: 31.4 },
      topPerformingSansthas: [
        {
          id: "dca-kar-1",
          name: "Karvir DCA Industrial",
          animals: { cows: 460, buffalos: 370 },
          status: "active",
          efficiency: 96.8
        }
      ]
    }
  ]
};

const RegionSansthaOverview = () => {
  const [selectedRegion, setSelectedRegion] = useState<string>("all");
  const [selectedSanstha, setSelectedSanstha] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const itemsPerPage = 6;

  // Calculate totals
  const calculateTotals = () => {
    let totalCows = 0;
    let totalBuffalos = 0;
    let totalSansthas = 0;
    let activeSansthas = 0;

    mockData.regions.forEach(region => {
      if (selectedRegion === "all" || selectedRegion === region.id) {
        totalCows += region.animals.cows;
        totalBuffalos += region.animals.buffalos;
        totalSansthas += region.totalSansthas;
        activeSansthas += region.activeSansthas;
      }
    });

    return { totalCows, totalBuffalos, totalSansthas, activeSansthas };
  };

  const totals = calculateTotals();

  // Filter data based on selections and search
  const getFilteredData = () => {
    let filteredRegions = mockData.regions.filter(region =>
      selectedRegion === "all" || selectedRegion === region.id
    );

    if (searchTerm) {
      filteredRegions = filteredRegions.filter(region =>
        region.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        region.topPerformingSansthas.some(sanstha =>
          sanstha.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    return filteredRegions;
  };

  // Pagination
  const filteredData = getFilteredData();
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Kolhapur District - Taluka & Sanstha Overview</h2>
          <p className="text-gray-600">Monitor animal inventory across 12 talukas and 6,000 sansthas in Kolhapur district</p>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search talukas or sansthas..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to first page on search
              }}
              className="pl-10 w-64"
            />
          </div>

          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={selectedRegion} onValueChange={(value) => {
              setSelectedRegion(value);
              setCurrentPage(1); // Reset to first page on filter change
            }}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select Taluka" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Talukas</SelectItem>
                {mockData.regions.map(region => (
                  <SelectItem key={region.id} value={region.id}>
                    {region.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Total Cows</p>
                <p className="text-3xl font-bold text-blue-900">🐄 {totals.totalCows.toLocaleString()}</p>
                <p className="text-xs text-blue-600 mt-1">Across all regions</p>
              </div>
              <div className="bg-blue-200 p-3 rounded-full">
                <Heart className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Total Buffalos</p>
                <p className="text-3xl font-bold text-green-900">🐃 {totals.totalBuffalos.toLocaleString()}</p>
                <p className="text-xs text-green-600 mt-1">Across all regions</p>
              </div>
              <div className="bg-green-200 p-3 rounded-full">
                <Heart className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Total Sansthas</p>
                <p className="text-3xl font-bold text-purple-900">{totals.totalSansthas.toLocaleString()}</p>
                <p className="text-xs text-purple-600 mt-1">Registered sansthas</p>
              </div>
              <div className="bg-purple-200 p-3 rounded-full">
                <Building2 className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">Active Sansthas</p>
                <p className="text-3xl font-bold text-orange-900">{totals.activeSansthas.toLocaleString()}</p>
                <p className="text-xs text-orange-600 mt-1">{((totals.activeSansthas/totals.totalSansthas)*100).toFixed(1)}% active</p>
              </div>
              <div className="bg-orange-200 p-3 rounded-full">
                <Users className="h-6 w-6 text-orange-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search Results Info */}
      {searchTerm && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-800">
            Found <strong>{filteredData.length}</strong> taluka(s) matching "<strong>{searchTerm}</strong>"
          </p>
        </div>
      )}

      {/* Taluka Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {paginatedData.map(region => (
          <Card key={region.id} className="shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  {region.name} Taluka
                </CardTitle>
                <Badge variant="outline" className="text-xs">
                  {region.totalSansthas.toLocaleString()} Sansthas
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Regional Summary */}
              <div className="bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">🐄 {region.animals.cows.toLocaleString()}</div>
                    <div className="text-xs text-gray-600">Total Cows</div>
                    <div className="text-xs text-blue-500">Avg: {region.avgAnimalsPerSanstha.cows}/sanstha</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">🐃 {region.animals.buffalos.toLocaleString()}</div>
                    <div className="text-xs text-gray-600">Total Buffalos</div>
                    <div className="text-xs text-green-500">Avg: {region.avgAnimalsPerSanstha.buffalos}/sanstha</div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Active Sansthas:</span>
                  <span className="font-semibold text-green-600">
                    {region.activeSansthas.toLocaleString()} / {region.totalSansthas.toLocaleString()}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${(region.activeSansthas / region.totalSansthas) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* Top Performing Sansthas */}
              <div>
                <h5 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  Top Performing Sansthas
                </h5>
                {region.topPerformingSansthas.map(sanstha => (
                  <div key={sanstha.id} className="border rounded-lg p-3 bg-white mb-2">
                    <div className="flex items-center justify-between mb-2">
                      <h6 className="font-medium text-gray-900 text-sm">{sanstha.name}</h6>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={sanstha.status === "active" ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {sanstha.status}
                        </Badge>
                        <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                          {sanstha.efficiency}% efficiency
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="text-center bg-blue-50 p-2 rounded">
                        <div className="text-blue-600 font-semibold">🐄 {sanstha.animals.cows}</div>
                        <div className="text-gray-500 text-xs">Cows</div>
                      </div>
                      <div className="text-center bg-green-50 p-2 rounded">
                        <div className="text-green-600 font-semibold">🐃 {sanstha.animals.buffalos}</div>
                        <div className="text-gray-500 text-xs">Buffalos</div>
                      </div>
                    </div>
                  </div>
                ))}

                <Button variant="outline" size="sm" className="w-full mt-2">
                  <Eye className="h-4 w-4 mr-2" />
                  View All {region.totalSansthas.toLocaleString()} Sansthas
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredData.length)} of {filteredData.length} talukas
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                  className="w-8 h-8 p-0"
                >
                  {page}
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RegionSansthaOverview;

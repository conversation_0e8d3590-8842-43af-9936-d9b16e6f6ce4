import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  MapPin,
  Building2,
  Users,
  TrendingUp,
  BarChart3,
  Filter,
  Eye,
  Heart,
  Search,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

// Mock data for regions and sansthas - representing 6000 sansthas across regions
const mockData = {
  regions: [
    {
      id: "kolhapur",
      name: "Kolhapur",
      totalSansthas: 1200,
      activeSansthas: 1150,
      animals: { cows: 45000, buffalos: 32000 },
      avgAnimalsPerSanstha: { cows: 37.5, buffalos: 26.7 },
      topPerformingSansthas: [
        {
          id: "dca-kol-1",
          name: "Kolhapur DCA Central",
          animals: { cows: 450, buffalos: 320 },
          status: "active",
          efficiency: 94.2
        },
        {
          id: "dca-kol-2",
          name: "Kolhapur DCA South",
          animals: { cows: 380, buffalos: 290 },
          status: "active",
          efficiency: 91.8
        }
      ]
    },
    {
      id: "sangli",
      name: "Sangli",
      totalSansthas: 1500,
      activeSansthas: 1420,
      animals: { cows: 52000, buffalos: 41000 },
      avgAnimalsPerSanstha: { cows: 34.7, buffalos: 27.3 },
      topPerformingSansthas: [
        {
          id: "dca-san-1",
          name: "Sangli DCA East",
          animals: { cows: 520, buffalos: 410 },
          status: "active",
          efficiency: 96.5
        },
        {
          id: "dca-san-2",
          name: "Sangli DCA West",
          animals: { cows: 340, buffalos: 280 },
          status: "maintenance",
          efficiency: 87.2
        }
      ]
    },
    {
      id: "satara",
      name: "Satara",
      totalSansthas: 900,
      activeSansthas: 875,
      animals: { cows: 38000, buffalos: 29000 },
      avgAnimalsPerSanstha: { cows: 42.2, buffalos: 32.2 },
      topPerformingSansthas: [
        {
          id: "dca-sat-1",
          name: "Satara DCA North",
          animals: { cows: 420, buffalos: 350 },
          status: "active",
          efficiency: 93.1
        }
      ]
    },
    {
      id: "pune",
      name: "Pune",
      totalSansthas: 800,
      activeSansthas: 780,
      animals: { cows: 35000, buffalos: 28000 },
      avgAnimalsPerSanstha: { cows: 43.8, buffalos: 35.0 },
      topPerformingSansthas: [
        {
          id: "dca-pun-1",
          name: "Pune DCA Central",
          animals: { cows: 480, buffalos: 380 },
          status: "active",
          efficiency: 95.3
        }
      ]
    },
    {
      id: "solapur",
      name: "Solapur",
      totalSansthas: 1100,
      activeSansthas: 1050,
      animals: { cows: 42000, buffalos: 35000 },
      avgAnimalsPerSanstha: { cows: 38.2, buffalos: 31.8 },
      topPerformingSansthas: [
        {
          id: "dca-sol-1",
          name: "Solapur DCA West",
          animals: { cows: 390, buffalos: 310 },
          status: "active",
          efficiency: 92.7
        }
      ]
    },
    {
      id: "ahmednagar",
      name: "Ahmednagar",
      totalSansthas: 500,
      activeSansthas: 485,
      animals: { cows: 22000, buffalos: 18000 },
      avgAnimalsPerSanstha: { cows: 44.0, buffalos: 36.0 },
      topPerformingSansthas: [
        {
          id: "dca-ahm-1",
          name: "Ahmednagar DCA East",
          animals: { cows: 410, buffalos: 340 },
          status: "active",
          efficiency: 94.8
        }
      ]
    }
  ]
};

const RegionSansthaOverview = () => {
  const [selectedRegion, setSelectedRegion] = useState<string>("all");
  const [selectedSanstha, setSelectedSanstha] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const itemsPerPage = 6;

  // Calculate totals
  const calculateTotals = () => {
    let totalCows = 0;
    let totalBuffalos = 0;
    let totalSansthas = 0;
    let activeSansthas = 0;

    mockData.regions.forEach(region => {
      if (selectedRegion === "all" || selectedRegion === region.id) {
        totalCows += region.animals.cows;
        totalBuffalos += region.animals.buffalos;
        totalSansthas += region.totalSansthas;
        activeSansthas += region.activeSansthas;
      }
    });

    return { totalCows, totalBuffalos, totalSansthas, activeSansthas };
  };

  const totals = calculateTotals();

  // Filter data based on selections and search
  const getFilteredData = () => {
    let filteredRegions = mockData.regions.filter(region =>
      selectedRegion === "all" || selectedRegion === region.id
    );

    if (searchTerm) {
      filteredRegions = filteredRegions.filter(region =>
        region.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        region.topPerformingSansthas.some(sanstha =>
          sanstha.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    return filteredRegions;
  };

  // Pagination
  const filteredData = getFilteredData();
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Region & Sanstha Overview</h2>
          <p className="text-gray-600">Monitor animal inventory across regions and organizations</p>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search regions or sansthas..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to first page on search
              }}
              className="pl-10 w-64"
            />
          </div>

          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={selectedRegion} onValueChange={(value) => {
              setSelectedRegion(value);
              setCurrentPage(1); // Reset to first page on filter change
            }}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select Region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Regions</SelectItem>
                {mockData.regions.map(region => (
                  <SelectItem key={region.id} value={region.id}>
                    {region.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Total Cows</p>
                <p className="text-3xl font-bold text-blue-900">🐄 {totals.totalCows.toLocaleString()}</p>
                <p className="text-xs text-blue-600 mt-1">Across all regions</p>
              </div>
              <div className="bg-blue-200 p-3 rounded-full">
                <Heart className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Total Buffalos</p>
                <p className="text-3xl font-bold text-green-900">🐃 {totals.totalBuffalos.toLocaleString()}</p>
                <p className="text-xs text-green-600 mt-1">Across all regions</p>
              </div>
              <div className="bg-green-200 p-3 rounded-full">
                <Heart className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Total Sansthas</p>
                <p className="text-3xl font-bold text-purple-900">{totals.totalSansthas.toLocaleString()}</p>
                <p className="text-xs text-purple-600 mt-1">Registered sansthas</p>
              </div>
              <div className="bg-purple-200 p-3 rounded-full">
                <Building2 className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">Active Sansthas</p>
                <p className="text-3xl font-bold text-orange-900">{totals.activeSansthas.toLocaleString()}</p>
                <p className="text-xs text-orange-600 mt-1">{((totals.activeSansthas/totals.totalSansthas)*100).toFixed(1)}% active</p>
              </div>
              <div className="bg-orange-200 p-3 rounded-full">
                <Users className="h-6 w-6 text-orange-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search Results Info */}
      {searchTerm && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-800">
            Found <strong>{filteredData.length}</strong> region(s) matching "<strong>{searchTerm}</strong>"
          </p>
        </div>
      )}

      {/* Regional Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {paginatedData.map(region => (
          <Card key={region.id} className="shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  {region.name} Region
                </CardTitle>
                <Badge variant="outline" className="text-xs">
                  {region.totalSansthas.toLocaleString()} Sansthas
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Regional Summary */}
              <div className="bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">🐄 {region.animals.cows.toLocaleString()}</div>
                    <div className="text-xs text-gray-600">Total Cows</div>
                    <div className="text-xs text-blue-500">Avg: {region.avgAnimalsPerSanstha.cows}/sanstha</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">🐃 {region.animals.buffalos.toLocaleString()}</div>
                    <div className="text-xs text-gray-600">Total Buffalos</div>
                    <div className="text-xs text-green-500">Avg: {region.avgAnimalsPerSanstha.buffalos}/sanstha</div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Active Sansthas:</span>
                  <span className="font-semibold text-green-600">
                    {region.activeSansthas.toLocaleString()} / {region.totalSansthas.toLocaleString()}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${(region.activeSansthas / region.totalSansthas) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* Top Performing Sansthas */}
              <div>
                <h5 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  Top Performing Sansthas
                </h5>
                {region.topPerformingSansthas.map(sanstha => (
                  <div key={sanstha.id} className="border rounded-lg p-3 bg-white mb-2">
                    <div className="flex items-center justify-between mb-2">
                      <h6 className="font-medium text-gray-900 text-sm">{sanstha.name}</h6>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={sanstha.status === "active" ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {sanstha.status}
                        </Badge>
                        <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                          {sanstha.efficiency}% efficiency
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="text-center bg-blue-50 p-2 rounded">
                        <div className="text-blue-600 font-semibold">🐄 {sanstha.animals.cows}</div>
                        <div className="text-gray-500 text-xs">Cows</div>
                      </div>
                      <div className="text-center bg-green-50 p-2 rounded">
                        <div className="text-green-600 font-semibold">🐃 {sanstha.animals.buffalos}</div>
                        <div className="text-gray-500 text-xs">Buffalos</div>
                      </div>
                    </div>
                  </div>
                ))}

                <Button variant="outline" size="sm" className="w-full mt-2">
                  <Eye className="h-4 w-4 mr-2" />
                  View All {region.totalSansthas.toLocaleString()} Sansthas
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredData.length)} of {filteredData.length} regions
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                  className="w-8 h-8 p-0"
                >
                  {page}
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RegionSansthaOverview;

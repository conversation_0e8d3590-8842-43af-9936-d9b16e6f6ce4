import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle, Clock, Thermometer, Droplets, TrendingDown, Brain } from "lucide-react";

interface QualityAlertDialogProps {
  product: string;
  alertCount: number;
  children: React.ReactNode;
}

const QualityAlertDialog = ({ product, alertCount, children }: QualityAlertDialogProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const getProductAlerts = (productName: string) => {
    const alertsMap: { [key: string]: any[] } = {
      "Milk": [
        {
          id: 1,
          severity: "critical",
          title: "SNF Level Below Standard",
          description: "SNF: 8.2% (Standard: 8.5%)",
          location: "Collection Center - Hatkanangale",
          time: "15 minutes ago",
          aiAnalysis: "Water content increased due to recent rains affecting feed quality",
          recommendation: "Advise farmers on proper cattle feeding during monsoon",
          impact: "Quality rejection risk",
          icon: <Droplets className="h-4 w-4" />
        },
        {
          id: 2,
          severity: "warning",
          title: "Fat Content Variation",
          description: "Fat: 3.2% (Target: 3.5%)",
          location: "Collection Center - Shirol",
          time: "32 minutes ago",
          aiAnalysis: "Seasonal variation in cattle diet affecting fat content",
          recommendation: "Monitor feed quality and suggest supplements",
          impact: "Price adjustment needed",
          icon: <TrendingDown className="h-4 w-4" />
        }
      ],
      "Curd": [
        {
          id: 3,
          severity: "warning",
          title: "Culture Activity Low",
          description: "pH: 4.8 (Target: 4.2-4.4)",
          location: "Processing Unit B",
          time: "8 minutes ago",
          aiAnalysis: "Temperature fluctuation affecting culture performance",
          recommendation: "Check incubation temperature control",
          impact: "Taste and texture quality",
          icon: <Thermometer className="h-4 w-4" />
        }
      ],
      "Paneer": [
        {
          id: 4,
          severity: "info",
          title: "Moisture Content Check",
          description: "Moisture: 58% (Standard: <60%)",
          location: "Processing Unit A",
          time: "22 minutes ago",
          aiAnalysis: "Within acceptable range but trending upward",
          recommendation: "Monitor pressing time and temperature",
          impact: "Shelf life optimization",
          icon: <Clock className="h-4 w-4" />
        }
      ],
      "Ghee": [
        {
          id: 5,
          severity: "critical",
          title: "Color Variation Detected",
          description: "Color index: 2.8 (Standard: 2.0-2.5)",
          location: "Ghee Processing Unit",
          time: "5 minutes ago",
          aiAnalysis: "Extended heating time causing darker color",
          recommendation: "Adjust heating schedule and temperature profile",
          impact: "Customer quality perception",
          icon: <AlertTriangle className="h-4 w-4" />
        }
      ]
    };
    return alertsMap[productName] || [];
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'info': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'info': return <Clock className="h-4 w-4 text-blue-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const alerts = getProductAlerts(product);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            <span>{product} Quality Alerts</span>
            <Badge className="bg-red-100 text-red-800">{alerts.length} Active</Badge>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {alerts.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-2">No quality alerts for {product}</div>
              <Badge className="bg-green-100 text-green-800">All parameters normal</Badge>
            </div>
          ) : (
            alerts.map((alert) => (
              <Card key={alert.id} className="border-l-4 border-l-orange-500">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 mt-1">
                      {getSeverityIcon(alert.severity)}
                    </div>
                    
                    <div className="flex-1 space-y-3">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900">{alert.title}</h4>
                          <p className="text-sm text-gray-600 mt-1">{alert.description}</p>
                        </div>
                        <Badge className={getSeverityColor(alert.severity)}>
                          {alert.severity.toUpperCase()}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-blue-50 p-3 rounded-lg">
                          <div className="flex items-center space-x-2 mb-2">
                            <Brain className="h-4 w-4 text-blue-600" />
                            <span className="text-sm font-medium text-blue-900">AI Analysis</span>
                          </div>
                          <p className="text-sm text-blue-800">{alert.aiAnalysis}</p>
                        </div>
                        
                        <div className="bg-green-50 p-3 rounded-lg">
                          <div className="flex items-center space-x-2 mb-2">
                            <AlertTriangle className="h-4 w-4 text-green-600" />
                            <span className="text-sm font-medium text-green-900">Recommendation</span>
                          </div>
                          <p className="text-sm text-green-800">{alert.recommendation}</p>
                        </div>
                      </div>

                      <div className="text-xs text-gray-500 space-y-1">
                        <div>📍 Location: {alert.location}</div>
                        <div>⏰ Time: {alert.time}</div>
                        <div>📊 Impact: {alert.impact}</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            View Full Report
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QualityAlertDialog;

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Zap, 
  Sun, 
  Thermometer,
  TrendingDown,
  TrendingUp,
  Leaf,
  Settings,
  BarChart3,
  Battery,
  Cloud
} from "lucide-react";

const EnergyManagement = () => {
  const [solarOutput, setSolarOutput] = useState(4.2);
  const [energyConsumption, setEnergyConsumption] = useState(6.8);
  const [weatherForecast, setWeatherForecast] = useState("Sunny");

  // Simulate real-time data
  useEffect(() => {
    const interval = setInterval(() => {
      const time = new Date().getHours();
      // Simulate solar output based on time of day
      if (time >= 6 && time <= 18) {
        setSolarOutput(prev => Math.max(0, <PERSON>.min(6.5, prev + (Math.random() - 0.5) * 0.3)));
      } else {
        setSolarOutput(0);
      }
      setEnergyConsumption(prev => Math.max(5, Math.min(8, prev + (Math.random() - 0.5) * 0.2)));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const energyMetrics = [
    {
      title: "Current Solar Output",
      value: `${solarOutput.toFixed(1)} MW`,
      target: "6.5 MW peak",
      percentage: (solarOutput / 6.5) * 100,
      icon: <Sun className="h-6 w-6 text-yellow-600" />,
      trend: solarOutput > 3 ? "up" : "down"
    },
    {
      title: "Total Consumption",
      value: `${energyConsumption.toFixed(1)} MW`,
      target: "7.5 MW capacity",
      percentage: (energyConsumption / 7.5) * 100,
      icon: <Zap className="h-6 w-6 text-blue-600" />,
      trend: energyConsumption < 7 ? "down" : "up"
    },
    {
      title: "Grid Independence",
      value: `${Math.min(100, (solarOutput / energyConsumption * 100)).toFixed(0)}%`,
      target: "75% target",
      percentage: Math.min(100, (solarOutput / energyConsumption) * 100),
      icon: <Battery className="h-6 w-6 text-green-600" />,
      trend: solarOutput > energyConsumption * 0.6 ? "up" : "down"
    },
    {
      title: "Energy Savings",
      value: "₹28.5L/year",
      target: "₹30L target",
      percentage: 95,
      icon: <Leaf className="h-6 w-6 text-green-600" />,
      trend: "up"
    }
  ];

  const coldStorageUnits = [
    {
      unit: "Cold Room A",
      temperature: -2.1,
      targetTemp: -2.0,
      efficiency: 92,
      compressorCycles: 45,
      powerUsage: 1.2,
      status: "optimal"
    },
    {
      unit: "Cold Room B", 
      temperature: -1.8,
      targetTemp: -2.0,
      efficiency: 88,
      compressorCycles: 52,
      powerUsage: 1.4,
      status: "suboptimal"
    },
    {
      unit: "Cold Room C",
      temperature: -2.3,
      targetTemp: -2.0,
      efficiency: 94,
      compressorCycles: 41,
      powerUsage: 1.1,
      status: "optimal"
    },
    {
      unit: "Freezer Unit 1",
      temperature: -18.2,
      targetTemp: -18.0,
      efficiency: 91, 
      compressorCycles: 38,
      powerUsage: 0.8,
      status: "optimal"
    }
  ];

  const solarForecast = [
    { time: "06:00", predicted: 0.2, weather: "Clear" },
    { time: "09:00", predicted: 3.8, weather: "Sunny" },
    { time: "12:00", predicted: 6.2, weather: "Sunny" },
    { time: "15:00", predicted: 5.1, weather: "Partly Cloudy" },
    { time: "18:00", predicted: 1.4, weather: "Clear" },
    { time: "21:00", predicted: 0.0, weather: "Clear" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'optimal': return 'bg-green-100 text-green-800 border-green-200';
      case 'suboptimal': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    return trend === "up" ? 
      <TrendingUp className="h-4 w-4 text-green-600" /> : 
      <TrendingDown className="h-4 w-4 text-red-600" />;
  };

  const weatherIcons = {
    "Clear": <Sun className="h-4 w-4 text-yellow-600" />,
    "Sunny": <Sun className="h-4 w-4 text-yellow-600" />,
    "Partly Cloudy": <Cloud className="h-4 w-4 text-gray-600" />,
    "Cloudy": <Cloud className="h-4 w-4 text-gray-600" />
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-sky-200 bg-gradient-to-r from-sky-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-6 w-6 text-sky-600" />
            <span>AI-Powered Energy & Sustainability Management</span>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Solar optimization and intelligent cold storage management
          </p>
        </CardHeader>
      </Card>

      {/* Energy Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {energyMetrics.map((metric, index) => (
          <Card key={index} className="border-sky-200">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {metric.icon}
                    <h4 className="text-sm font-medium text-gray-700">{metric.title}</h4>
                  </div>
                  {getTrendIcon(metric.trend)}
                </div>
                <div className="space-y-2">
                  <div className="flex items-end space-x-2">
                    <span className="text-2xl font-bold text-sky-700">{metric.value}</span>
                  </div>
                  <p className="text-xs text-gray-500">{metric.target}</p>
                  <Progress value={metric.percentage} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Solar Forecast */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sun className="h-5 w-5 text-yellow-600" />
            <span>Solar Output Forecast (Today)</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            {solarForecast.map((forecast, index) => (
              <div key={index} className="text-center p-3 bg-gradient-to-b from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
                <div className="flex items-center justify-center mb-2">
                  {weatherIcons[forecast.weather as keyof typeof weatherIcons]}
                </div>
                <p className="text-sm font-medium text-gray-900">{forecast.time}</p>
                <p className="text-lg font-bold text-yellow-700">{forecast.predicted} MW</p>
                <p className="text-xs text-gray-600">{forecast.weather}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Cold Storage Optimization */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Thermometer className="h-5 w-5 text-blue-600" />
            <span>Cold Storage AI Optimization</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {coldStorageUnits.map((unit, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <Badge className={getStatusColor(unit.status)}>
                    {unit.status}
                  </Badge>
                  <div>
                    <h4 className="font-medium text-gray-900">{unit.unit}</h4>
                    <p className="text-sm text-gray-600">
                      Temp: {unit.temperature}°C (Target: {unit.targetTemp}°C)
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Efficiency</p>
                    <p className="font-medium text-sky-700">{unit.efficiency}%</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Cycles/Hr</p>
                    <p className="font-medium text-gray-900">{unit.compressorCycles}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Power</p>
                    <p className="font-medium text-gray-900">{unit.powerUsage} MW</p>
                  </div>
                  <Button size="sm" variant="outline">
                    <Settings className="h-4 w-4 mr-1" />
                    Adjust
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Recommendations */}
      <Card className="border-sky-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            <span>AI Energy Recommendations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-blue-200">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Sun className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Optimize Solar Usage</h4>
                <p className="text-sm text-gray-600">Increase cold storage cooling during peak solar hours (12-3 PM) to maximize renewable energy usage.</p>
                <p className="text-xs text-green-600 mt-1">Potential savings: ₹2.5L/month</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-blue-200">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Thermometer className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Compressor Cycle Optimization</h4>
                <p className="text-sm text-gray-600">Cold Room B showing suboptimal cycles. Recommend maintenance check and temperature sensor calibration.</p>
                <p className="text-xs text-green-600 mt-1">Potential savings: ₹1.8L/month</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-blue-200">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Leaf className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Load Balancing</h4>
                <p className="text-sm text-gray-600">Distribute processing loads to align with solar peak hours for maximum grid independence.</p>
                <p className="text-xs text-green-600 mt-1">Potential savings: ₹3.2L/month</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Benefits Summary */}
      <Card className="border-sky-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Leaf className="h-6 w-6 text-green-600" />
            <span>Energy Optimization Benefits</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">15%</div>
              <p className="text-sm text-gray-700">Energy cost reduction</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">₹30L</div>
              <p className="text-sm text-gray-700">Annual savings target</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">25%</div>
              <p className="text-sm text-gray-700">Improved load balancing</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnergyManagement;

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Package, 
  AlertTriangle, 
  CheckCircle,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Filter,
  Brain,
  Lightbulb
} from "lucide-react";
import DateRangeFilter from "@/components/DateRangeFilter";
import QualityAlertDialog from "@/components/QualityAlertDialog";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const QualityDepartment = () => {
  const [dateRange, setDateRange] = useState("today");
  const [selectedProduct, setSelectedProduct] = useState("all");

  const products = ["all", "milk", "ghee", "paneer", "curd", "butter"];
  
  const qualityMetrics = [
    {
      product: "Milk",
      batchesProcessed: 45,
      passRate: 96.8,
      defectRate: 3.2,
      avgSNF: 8.2,
      targetSNF: 8.5,
      status: "warning",
      alerts: 2
    },
    {
      product: "Ghee",
      batchesProcessed: 12,
      passRate: 98.5,
      defectRate: 1.5,
      avgSNF: 99.1,
      targetSNF: 99.0,
      status: "good",
      alerts: 0
    },
    {
      product: "Paneer",
      batchesProcessed: 8,
      passRate: 94.2,
      defectRate: 5.8,
      avgSNF: 18.5,
      targetSNF: 18.0,
      status: "critical",
      alerts: 3
    },
    {
      product: "Curd",
      batchesProcessed: 22,
      passRate: 97.1,
      defectRate: 2.9,
      avgSNF: 3.2,
      targetSNF: 3.0,
      status: "good",
      alerts: 1
    }
  ];

  const packagingLines = [
    {
      line: "Line 1",
      product: "Milk",
      sealDefects: 0.8,
      labelIssues: 0.2,
      totalDefects: 1.0,
      target: 1.0,
      status: "good",
      lastInspection: "2 hours ago"
    },
    {
      line: "Line 2",
      product: "Ghee",
      sealDefects: 0.5,
      labelIssues: 0.1,
      totalDefects: 0.6,
      target: 1.0,
      status: "good",
      lastInspection: "1 hour ago"
    },
    {
      line: "Line 3",
      product: "Paneer",
      sealDefects: 2.1,
      labelIssues: 0.3,
      totalDefects: 2.4,
      target: 1.0,
      status: "critical",
      lastInspection: "30 minutes ago"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const calculatePenalty = (defectRate: number, product: string) => {
    const basePrice = { milk: 50, ghee: 500, paneer: 300, curd: 80, butter: 450 };
    const penaltyRate = defectRate > 2 ? defectRate * 0.05 : 0;
    return Math.round(penaltyRate * (basePrice[product.toLowerCase() as keyof typeof basePrice] || 100));
  };

  const aiInsights = [
    {
      title: "Smart Quality Detection",
      description: "Automated monitoring system detected 95% accuracy in identifying quality issues before they impact production",
      impact: "High",
      action: "Continue monitoring patterns"
    },
    {
      title: "Pattern Recognition",
      description: "Smart system identified seasonal quality variations in milk samples from different collection routes",
      impact: "Medium", 
      action: "Adjust testing frequency during monsoon"
    },
    {
      title: "Defect Prediction",
      description: "Packaging line monitoring predicts potential seal defects 2 hours before they occur",
      impact: "High",
      action: "Schedule proactive maintenance"
    }
  ];

  // Chart Data
  const qualityTrendData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Milk Quality (%)',
        data: [96.8, 97.2, 96.5, 97.8, 96.9, 97.5, 97.1],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Ghee Quality (%)',
        data: [98.5, 98.2, 98.8, 98.1, 98.7, 98.3, 98.6],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Paneer Quality (%)',
        data: [94.2, 95.1, 94.8, 95.5, 94.9, 95.2, 95.0],
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.4,
        fill: true,
      }
    ],
  };

  const defectAnalysisData = {
    labels: ['Packaging Defects', 'Contamination', 'Temperature Issues', 'pH Variations', 'Other'],
    datasets: [
      {
        data: [35, 25, 20, 15, 5],
        backgroundColor: [
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(168, 85, 247, 0.8)',
          'rgba(156, 163, 175, 0.8)',
        ],
        borderColor: [
          'rgb(239, 68, 68)',
          'rgb(245, 158, 11)',
          'rgb(59, 130, 246)',
          'rgb(168, 85, 247)',
          'rgb(156, 163, 175)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const batchComparisonData = {
    labels: ['Milk', 'Ghee', 'Paneer', 'Curd', 'Butter'],
    datasets: [
      {
        label: 'Pass Rate (%)',
        data: [96.8, 98.5, 94.2, 95.8, 97.1],
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 2,
      },
      {
        label: 'Target (%)',
        data: [95, 98, 94, 95, 96],
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
      },
    ],
  };

  const qualityParametersData = {
    labels: ['Fat Content', 'Protein', 'SNF', 'pH Level', 'Temperature', 'Purity'],
    datasets: [
      {
        label: 'Current Values',
        data: [85, 92, 88, 95, 90, 96],
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgb(59, 130, 246)',
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(59, 130, 246)',
      },
      {
        label: 'Target Values',
        data: [90, 90, 90, 90, 90, 90],
        backgroundColor: 'rgba(34, 197, 94, 0.2)',
        borderColor: 'rgb(34, 197, 94)',
        pointBackgroundColor: 'rgb(34, 197, 94)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(34, 197, 94)',
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: false,
      },
    },
  };

  const radarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      r: {
        beginAtZero: true,
        max: 100,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Quality Control Dashboard</h2>
          <p className="text-sm text-gray-600">Real-time quality monitoring and analysis</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select 
              value={selectedProduct} 
              onChange={(e) => setSelectedProduct(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
            >
              {products.map(product => (
                <option key={product} value={product}>
                  {product === 'all' ? 'All Products' : product.charAt(0).toUpperCase() + product.slice(1)}
                </option>
              ))}
            </select>
          </div>
          <DateRangeFilter value={dateRange} onChange={setDateRange} />
        </div>
      </div>

      {/* AI Insights Section for Quality Department */}
      <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <span>Quality Smart Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {aiInsights.map((insight, index) => (
              <div key={index} className="bg-white p-4 rounded-lg border border-purple-100">
                <div className="flex items-start space-x-3">
                  <Lightbulb className="h-5 w-5 text-yellow-500 mt-1" />
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-2">{insight.title}</h4>
                    <p className="text-sm text-gray-700 mb-3">{insight.description}</p>
                    <div className="flex items-center justify-between">
                      <Badge className={insight.impact === 'High' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}>
                        {insight.impact} Impact
                      </Badge>
                      <Button size="sm" variant="outline" className="text-xs">
                        {insight.action}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quality Metrics by Product */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-sky-600" />
            <span>Product Quality Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {qualityMetrics
              .filter(metric => selectedProduct === 'all' || metric.product.toLowerCase() === selectedProduct)
              .map((metric, index) => (
                <QualityAlertDialog key={index} product={metric.product} alertCount={metric.alerts}>
                  <div className="border border-gray-200 rounded-lg p-4 hover:border-sky-300 transition-colors cursor-pointer">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="bg-sky-100 p-2 rounded-lg">
                          <Package className="h-5 w-5 text-sky-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{metric.product}</h3>
                          <p className="text-sm text-gray-600">{metric.batchesProcessed} batches processed</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(metric.status)}>
                          {metric.status}
                        </Badge>
                        {metric.alerts > 0 && (
                          <Badge className="bg-red-500 text-white ml-2">
                            {metric.alerts} alerts
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-600">Pass Rate</span>
                          <div className="flex items-center space-x-1">
                            {metric.passRate > 95 ? 
                              <TrendingUp className="h-3 w-3 text-green-600" /> : 
                              <TrendingDown className="h-3 w-3 text-red-600" />
                            }
                            <span className="text-sm font-medium">{metric.passRate}%</span>
                          </div>
                        </div>
                        <Progress value={metric.passRate} className="h-2" />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-600">SNF Level</span>
                          <span className="text-sm font-medium">{metric.avgSNF}%</span>
                        </div>
                        <Progress 
                          value={(metric.avgSNF / metric.targetSNF) * 100} 
                          className="h-2" 
                        />
                        <p className="text-xs text-gray-500 mt-1">Target: {metric.targetSNF}%</p>
                      </div>
                    </div>

                    {/* Quality Parameters by Product */}
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <h4 className="text-sm font-medium text-blue-800 mb-2">Key Quality Parameters</h4>
                      {metric.product === 'Milk' && (
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div><span className="font-medium">SNF:</span> {metric.avgSNF}%</div>
                          <div><span className="font-medium">Fat:</span> 4.2%</div>
                          <div><span className="font-medium">Temp:</span> 4°C</div>
                        </div>
                      )}
                      {metric.product === 'Ghee' && (
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div><span className="font-medium">Moisture:</span> 0.3%</div>
                          <div><span className="font-medium">FFA:</span> 0.15%</div>
                          <div><span className="font-medium">Color:</span> Golden</div>
                        </div>
                      )}
                      {metric.product === 'Curd' && (
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div><span className="font-medium">pH:</span> 4.5</div>
                          <div><span className="font-medium">Consistency:</span> Good</div>
                          <div><span className="font-medium">Freshness:</span> 8h</div>
                        </div>
                      )}
                      {metric.product === 'Paneer' && (
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div><span className="font-medium">Moisture:</span> 55%</div>
                          <div><span className="font-medium">Texture:</span> Firm</div>
                          <div><span className="font-medium">Color:</span> White</div>
                        </div>
                      )}
                    </div>

                    {/* Penalty Calculation */}
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Quality Penalty</span>
                        <span className="text-sm font-medium text-red-600">
                          ₹{calculatePenalty(metric.defectRate, metric.product)}/batch
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Based on {metric.defectRate}% defect rate
                      </p>
                    </div>
                  </div>
                </QualityAlertDialog>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quality Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quality Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span>Weekly Quality Trends</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={qualityTrendData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Defect Analysis Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <span>Defect Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Doughnut data={defectAnalysisData} options={pieChartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Batch Comparison & Quality Parameters */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Batch Comparison Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              <span>Product Quality Comparison</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Bar data={batchComparisonData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Quality Parameters Radar Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-purple-600" />
              <span>Quality Parameters Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Radar data={qualityParametersData} options={radarOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Packaging Line Monitoring */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-sky-600" />
            <span>Packaging Line Monitoring</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {packagingLines.map((line, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="bg-sky-100 p-3 rounded-lg">
                    <Package className="h-6 w-6 text-sky-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{line.line}</h4>
                    <p className="text-sm text-gray-600">Product: {line.product}</p>
                    <p className="text-xs text-gray-500">Last inspection: {line.lastInspection}</p>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-6 text-center">
                  <div>
                    <p className="text-sm text-gray-600">Seal Defects</p>
                    <p className="text-lg font-semibold text-gray-900">{line.sealDefects}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Label Issues</p>
                    <p className="text-lg font-semibold text-gray-900">{line.labelIssues}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Total Defects</p>
                    <p className={`text-lg font-semibold ${
                      line.totalDefects > line.target ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {line.totalDefects}%
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QualityDepartment;
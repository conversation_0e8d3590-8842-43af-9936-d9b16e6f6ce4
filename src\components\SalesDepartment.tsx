
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Truck, 
  TrendingUp, 
  TrendingDown,
  MapPin,
  Calendar,
  BarChart3,
  Package,
  IndianRupee,
  Brain,
  Lightbulb,
  AlertTriangle,
  Target,
  Clock
} from "lucide-react";
import DateRangeFilter from "@/components/DateRangeFilter";

const SalesDepartment = () => {
  const [dateRange, setDateRange] = useState("today");
  const [selectedRegion, setSelectedRegion] = useState("all");

  const regions = ["all", "radhanagri", "hamidwada", "chandgad", "shirol", "kini", "wathar", "gaganbawda"];

  const salesMetrics = [
    {
      period: "Today",
      revenue: 2850000,
      target: 3000000,
      growth: 12.5,
      orders: 145,
      avgOrderValue: 19655
    },
    {
      period: "This Week",
      revenue: 18500000,
      target: 20000000,
      growth: 8.3,
      orders: 892,
      avgOrderValue: 20740
    },
    {
      period: "This Month",
      revenue: 75200000,
      target: 80000000,
      growth: 15.2,
      orders: 3456,
      avgOrderValue: 21759
    }
  ];

  const productSales = [
    {
      product: "Ghee",
      dailySales: 890000,
      weeklySales: 5800000,
      monthlySales: 24000000,
      growth: 18.5,
      units: 1780,
      avgPrice: 500,
      insight: "Festival-driven surge in Radhanagri - reallocate stocks"
    },
    {
      product: "Dahi",
      dailySales: 420000,
      weeklySales: 2800000,
      monthlySales: 12500000,
      growth: 8.7,
      units: 4000,
      avgPrice: 80,
      insight: "Steady growth - maintain current strategy"
    },
    {
      product: "Paneer",
      dailySales: 650000,
      weeklySales: 4200000,
      monthlySales: 18500000,
      growth: 5.2,
      units: 1400,
      avgPrice: 300,
      insight: "Forecasts +8% demand in Kolhapur urban areas"
    },
    {
      product: "Milk",
      dailySales: 320000,
      weeklySales: 2100000,
      monthlySales: 8900000,
      growth: -12.0,
      units: 13000,
      avgPrice: 50,
      insight: "Declining in Chandgad - needs targeted promotions"
    }
  ];

  const regionalData = [
    {
      region: "Radhanagri",
      sales: 8500000,
      target: 9000000,
      growth: 15.0,
      topProduct: "Ghee",
      distributors: 25,
      penetration: 78,
      aiInsight: "High festival demand - increase ghee production capacity",
      delayRatio: 3.2,
      status: "Excellent"
    },
    {
      region: "Hamidwada", 
      sales: 6200000,
      target: 6500000,
      growth: 8.8,
      topProduct: "Milk",
      distributors: 18,
      penetration: 65,
      aiInsight: "Stable performance - maintain current distribution",
      delayRatio: 5.1,
      status: "Good"
    },
    {
      region: "Chandgad",
      sales: 4800000,
      target: 5200000,
      growth: -12.0,
      topProduct: "Paneer",
      distributors: 15,
      penetration: 52,
      aiInsight: "Declining milk sales - urgent distributor check-in needed",
      delayRatio: 6.8,
      status: "Needs Attention"
    },
    {
      region: "Shirol",
      sales: 3200000,
      target: 3800000,
      growth: 6.2,
      topProduct: "Dahi",
      distributors: 12,
      penetration: 45,
      aiInsight: "Under-penetration area - expand retail network",
      delayRatio: 4.5,
      status: "Average"
    },
    {
      region: "Kini",
      sales: 4500000,
      target: 4800000,
      growth: 11.5,
      topProduct: "Ghee",
      distributors: 16,
      penetration: 58,
      aiInsight: "High growth potential - optimize Kini-Wathar route",
      delayRatio: 8.2,
      status: "Good"
    },
    {
      region: "Wathar",
      sales: 3800000,
      target: 4200000,
      growth: 8.9,
      topProduct: "Milk",
      distributors: 14,
      penetration: 62,
      aiInsight: "Route optimization needed - 20% efficiency gain possible",
      delayRatio: 8.2,
      status: "Average"
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatLargeNumber = (num: number) => {
    if (num >= 10000000) return `${(num / 10000000).toFixed(1)}Cr`;
    if (num >= 100000) return `${(num / 100000).toFixed(1)}L`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getFilteredRegionalData = () => {
    return regionalData.filter(region => 
      selectedRegion === 'all' || region.region.toLowerCase() === selectedRegion
    );
  };

  const getStatusColor = (status: string) => {
    switch(status) {
      case 'Excellent': return 'bg-green-100 text-green-800';
      case 'Good': return 'bg-blue-100 text-blue-800';
      case 'Average': return 'bg-yellow-100 text-yellow-800';
      case 'Needs Attention': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const keyMetrics = [
    {
      title: "July Revenue vs Target",
      current: "₹7.2 Cr achieved vs ₹9 Cr goal",
      insight: "20% shortfall – needs strategic push in lagging SKUs",
      icon: <Target className="h-5 w-5 text-red-600" />,
      status: "warning"
    },
    {
      title: "Ghee Sales - Radhanagri",
      current: "15% increase YoY in 1st week of Shravan",
      insight: "Festival-driven surge → Suggest reallocating stocks to meet demand",
      icon: <TrendingUp className="h-5 w-5 text-green-600" />,
      status: "success"
    },
    {
      title: "Milk Sales - Chandgad Region",
      current: "Declined by 12% in 3 months",
      insight: "AI suggests targeted promotions & distributor check-in",
      icon: <TrendingDown className="h-5 w-5 text-red-600" />,
      status: "error"
    },
    {
      title: "Daily Dispatch Delay Ratio",
      current: "8.2% delays on Kini-Wathar Route",
      insight: "Rescheduling via AI = potential 20% efficiency gain",
      icon: <Clock className="h-5 w-5 text-orange-600" />,
      status: "warning"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Sales & Distribution Dashboard</h2>
          <p className="text-sm text-gray-600">Revenue, orders, and regional performance tracking</p>
        </div>
        <div className="flex items-center space-x-4">
          <select 
            value={selectedRegion} 
            onChange={(e) => setSelectedRegion(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            {regions.map(region => (
              <option key={region} value={region}>
                {region === 'all' ? 'All Regions' : region.charAt(0).toUpperCase() + region.slice(1)}
              </option>
            ))}
          </select>
          <DateRangeFilter value={dateRange} onChange={setDateRange} />
        </div>
      </div>

      {/* Key Performance Metrics */}
      <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <span>Key Performance Metrics & AI Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {keyMetrics.map((metric, index) => (
              <div key={index} className="bg-white p-4 rounded-lg border border-purple-100">
                <div className="flex items-start space-x-3">
                  {metric.icon}
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-2">{metric.title}</h4>
                    <p className="text-sm text-gray-700 mb-2">{metric.current}</p>
                    <div className="flex items-center space-x-2">
                      <Lightbulb className="h-4 w-4 text-yellow-500" />
                      <p className="text-xs text-gray-600">{metric.insight}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Sales Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {salesMetrics.map((metric, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="bg-green-100 p-2 rounded-lg">
                  <IndianRupee className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex items-center space-x-1">
                  {metric.growth > 0 ? 
                    <TrendingUp className="h-4 w-4 text-green-600" /> : 
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  }
                  <span className={`text-sm font-medium ${
                    metric.growth > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {metric.growth > 0 ? '+' : ''}{metric.growth}%
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">{metric.period} Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ₹{formatLargeNumber(metric.revenue)}
                  </p>
                </div>

                <div className="space-y-1">
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Progress vs Target</span>
                    <span>₹{formatLargeNumber(metric.target)}</span>
                  </div>
                  <Progress value={(metric.revenue / metric.target) * 100} className="h-2" />
                </div>

                <div className="grid grid-cols-2 gap-4 pt-2 border-t border-gray-200">
                  <div>
                    <p className="text-xs text-gray-600">Orders</p>
                    <p className="text-lg font-semibold text-gray-900">{metric.orders}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-600">Avg Order</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{formatLargeNumber(metric.avgOrderValue)}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Product-wise Sales with AI Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-sky-600" />
            <span>Top 3 Performing Products - AI Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {productSales.map((product, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-sky-300 transition-colors">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-sky-100 p-2 rounded-lg">
                      <Package className="h-5 w-5 text-sky-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{product.product}</h3>
                      <p className="text-sm text-gray-600">{product.units} units sold today</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {product.growth > 0 ? 
                      <TrendingUp className="h-4 w-4 text-green-600" /> : 
                      <TrendingDown className="h-4 w-4 text-red-600" />
                    }
                    <span className={`text-sm font-medium ${
                      product.growth > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {product.growth > 0 ? '+' : ''}{product.growth}%
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-4 gap-4 mb-3">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Daily</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{formatLargeNumber(product.dailySales)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Weekly</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{formatLargeNumber(product.weeklySales)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Monthly</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{formatLargeNumber(product.monthlySales)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Avg Price</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{product.avgPrice}
                    </p>
                  </div>
                </div>

                <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-400">
                  <div className="flex items-center space-x-2">
                    <Brain className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">AI Insight:</span>
                  </div>
                  <p className="text-sm text-blue-700 mt-1">{product.insight}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Regional Distribution with Enhanced AI Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5 text-sky-600" />
            <span>Regional Distribution Performance - Sales Heatmap</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {getFilteredRegionalData().map((region, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-green-100 p-2 rounded-lg">
                      <MapPin className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{region.region}</h3>
                      <p className="text-sm text-gray-600">Top Product: {region.topProduct}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(region.status)}>
                      {region.status}
                    </Badge>
                    <div className="flex items-center space-x-1" title={`${region.growth > 0 ? 'Growth' : 'Decline'}: ${Math.abs(region.growth)}%`}>
                      {region.growth > 0 ? 
                        <TrendingUp className="h-4 w-4 text-green-600" /> : 
                        <TrendingDown className="h-4 w-4 text-red-600" />
                      }
                      <span className={`text-sm font-medium ${
                        region.growth > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {region.growth > 0 ? '+' : ''}{region.growth}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-600">Sales vs Target</span>
                      <span className="text-sm font-medium">
                        ₹{formatLargeNumber(region.sales)} / ₹{formatLargeNumber(region.target)}
                      </span>
                    </div>
                    <Progress value={(region.sales / region.target) * 100} className="h-2" />
                  </div>

                  <div className="grid grid-cols-4 gap-3 pt-3 border-t border-gray-200">
                    <div className="text-center">
                      <p className="text-xs text-gray-600">Distributors</p>
                      <p className="text-lg font-semibold text-gray-900">{region.distributors}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-600">Penetration</p>
                      <p className="text-lg font-semibold text-gray-900">{region.penetration}%</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-600">Delay Ratio</p>
                      <p className={`text-lg font-semibold ${region.delayRatio > 7 ? 'text-red-600' : 'text-green-600'}`}>
                        {region.delayRatio}%
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-purple-600 font-medium">Status</p>
                      <Badge className={getStatusColor(region.status)} variant="secondary">
                        {region.status}
                      </Badge>
                    </div>
                  </div>

                  <div className="bg-purple-50 p-3 rounded-lg border-l-4 border-purple-400">
                    <div className="flex items-center space-x-2">
                      <Brain className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium text-purple-800">AI Insight:</span>
                    </div>
                    <p className="text-sm text-purple-700 mt-1">{region.aiInsight}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SalesDepartment;

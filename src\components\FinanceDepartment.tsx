
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  IndianRupee, 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Building,
  Truck,
  Users,
  Package,
  Factory,
  Activity,
  BarChart3,
  PieChart,
  FileText,
  Target,
  Gift,
  Brain,
  Zap,
  AlertCircle,
  TrendingUpIcon
} from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart as RechartsPie<PERSON>hart, Cell, BarChart, Bar, Pie } from 'recharts';
import DateRangeFilter from "@/components/DateRangeFilter";
import DudhSansthaDialog from "@/components/DudhSansthaDialog";

const FinanceDepartment = () => {
  const [dateRange, setDateRange] = useState("today");
  
  // Government Subsidy & Grant data
  const subsidyData = [
    {
      name: "Animal Feed Subsidy",
      sanctioned: 1000000,
      received: 600000,
      pending: 400000,
      progress: 60,
      status: "In Progress"
    },
    {
      name: "Solar Chilling Grant",
      sanctioned: 1500000,
      received: 1500000,
      pending: 0,
      progress: 100,
      status: "Completed"
    },
    {
      name: "Dairy Development Scheme",
      sanctioned: 850000,
      received: 550000,
      pending: 300000,
      progress: 65,
      status: "In Progress"
    }
  ];

  // AI Financial Insights data
  const aiInsights = [
    {
      title: "Collection Performance",
      icon: <TrendingUpIcon className="h-5 w-5" />,
      description: "Hatkanangale Sangh leads with 42K litres daily collection. Their farmer base of 185 generates highest revenue at ₹15.12L monthly.",
      impact: "High Impact",
      impactColor: "bg-green-100 text-green-700"
    },
    {
      title: "Expense Control",
      icon: <PieChart className="h-5 w-5" />,
      description: "Staff salaries (32%) and diesel transport (22%) are major expenses. Route optimization can reduce transport costs by ₹25K monthly.",
      impact: "Cost Saving",
      impactColor: "bg-purple-100 text-purple-700"
    },
    {
      title: "Payment Optimization",
      icon: <AlertCircle className="h-5 w-5" />,
      description: "₹4.82L pending payments to sanstha need immediate attention. Focus on high-due accounts for better cash flow.",
      impact: "Action Required",
      impactColor: "bg-orange-100 text-orange-700"
    },
    {
      title: "Subsidy Tracking",
      icon: <Gift className="h-5 w-5" />,
      description: "₹7L pending from government schemes. Animal Feed Subsidy (₹4L) and Dairy Development (₹3L) approvals needed for cash flow improvement.",
      impact: "Improvement",
      impactColor: "bg-blue-100 text-blue-700"
    }
  ];

  // Monthly revenue and expense data
  const monthlyData = [
    { month: 'Jan', revenue: 8200000, expense: 7720000, profit: 480000 },
    { month: 'Feb', revenue: 7800000, expense: 7200000, profit: 600000 },
    { month: 'Mar', revenue: 8500000, expense: 7800000, profit: 700000 },
    { month: 'Apr', revenue: 9200000, expense: 8100000, profit: 1100000 },
    { month: 'May', revenue: 8800000, expense: 7900000, profit: 900000 },
    { month: 'Jun', revenue: 8200000, expense: 7720000, profit: 480000 },
    { month: 'Jul', revenue: 8800000, expense: 7650000, profit: 1150000 }
  ];

  // Simplified expenditure breakdown with fewer categories
  const expenditureData = [
    { name: 'Dudh Sanstha Payments', amount: 4500000, percentage: 58.8, color: '#8B5CF6', description: 'Milk procurement from sanghas' },
    { name: 'Staff Salaries', amount: 900000, percentage: 11.8, color: '#06B6D4', description: 'Employee compensation' },
    { name: 'Transportation', amount: 680000, percentage: 8.9, color: '#F59E0B', description: 'Vehicle fuel & maintenance' },
    { name: 'Packaging & Materials', amount: 520000, percentage: 6.8, color: '#10B981', description: 'Bottles, pouches, labels' },
    { name: 'Utilities & Maintenance', amount: 620000, percentage: 8.1, color: '#EF4444', description: 'Electricity, water, equipment servicing' },
    { name: 'Other Expenses', amount: 425000, percentage: 5.6, color: '#6B7280', description: 'Taxes, insurance, marketing' }
  ];

  const totalExpenditure = expenditureData.reduce((sum, item) => sum + item.amount, 0);

  // Dudh Sanstha wise financial details
  const dudhSansthaData = [
    { 
      name: 'Hatkanangale Sangh', 
      milkCollected: 42000, 
      avgRate: 36.00, 
      totalAmount: 1512000, 
      amountPaid: 1300000, 
      pending: 212000, 
      lastPayment: '12-07-2025',
      performance: 'excellent'
    },
    { 
      name: 'Gokulwadi Sangh', 
      milkCollected: 31500, 
      avgRate: 35.75, 
      totalAmount: 1126125, 
      amountPaid: 1126125, 
      pending: 0, 
      lastPayment: '14-07-2025',
      performance: 'excellent'
    },
    { 
      name: 'Kagal Sangh', 
      milkCollected: 28750, 
      avgRate: 35.50, 
      totalAmount: 1021625, 
      amountPaid: 900000, 
      pending: 121625, 
      lastPayment: '10-07-2025',
      performance: 'good'
    },
    { 
      name: 'Shirol Sangh', 
      milkCollected: 34000, 
      avgRate: 36.20, 
      totalAmount: 1230800, 
      amountPaid: 1230800, 
      pending: 0, 
      lastPayment: '14-07-2025',
      performance: 'excellent'
    },
    { 
      name: 'Ajara Sangh', 
      milkCollected: 18500, 
      avgRate: 35.00, 
      totalAmount: 647500, 
      amountPaid: 500000, 
      pending: 147500, 
      lastPayment: '08-07-2025',
      performance: 'needs_attention'
    }
  ];

  // Product profitability data
  const productProfitData = [
    { product: 'Milk (500ml)', revenue: 3200000, cost: 2560000, profit: 640000, margin: 20.0 },
    { product: 'Ghee (1kg)', revenue: 2800000, cost: 2100000, profit: 700000, margin: 25.0 },
    { product: 'Dahi (200g)', revenue: 1500000, cost: 1200000, profit: 300000, margin: 20.0 },
    { product: 'Paneer (250g)', revenue: 900000, cost: 630000, profit: 270000, margin: 30.0 },
    { product: 'Butter (100g)', revenue: 400000, cost: 320000, profit: 80000, margin: 20.0 }
  ];

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'needs_attention': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'In Progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
            Finance & Operations Dashboard
          </h2>
          <p className="text-gray-600 mt-1">Financial health monitoring & collection management</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 bg-green-50 px-3 py-2 rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-green-700">Live Data</span>
          </div>
          <DateRangeFilter value={dateRange} onChange={setDateRange} />
        </div>
      </div>

      {/* Financial Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700">Today's Revenue</p>
                <p className="text-2xl font-bold text-green-800">{formatCurrency(625000)}</p>
                <p className="text-xs text-green-600">+8.5% from yesterday</p>
              </div>
              <div className="p-3 bg-green-200 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Monthly Profit</p>
                <p className="text-2xl font-bold text-blue-800">{formatCurrency(1150000)}</p>
                <p className="text-xs text-blue-600">+18.2% from last month</p>
              </div>
              <div className="p-3 bg-blue-200 rounded-full">
                <IndianRupee className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-700">Pending Payments</p>
                <p className="text-2xl font-bold text-yellow-800">{formatCurrency(481125)}</p>
                <p className="text-xs text-yellow-600">3 sanghas pending</p>
              </div>
              <div className="p-3 bg-yellow-200 rounded-full">
                <Clock className="h-6 w-6 text-yellow-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Daily Expenses</p>
                <p className="text-2xl font-bold text-purple-800">{formatCurrency(470000)}</p>
                <p className="text-xs text-purple-600">-2.1% from yesterday</p>
              </div>
              <div className="p-3 bg-purple-200 rounded-full">
                <TrendingDown className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Government Subsidy & Grant Tracking */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Gift className="h-6 w-6 text-yellow-600" />
            <span>Government Subsidy & Grant Tracking</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {subsidyData.map((subsidy, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="font-semibold text-gray-800">{subsidy.name}</h3>
                  <Badge className={getStatusColor(subsidy.status)}>
                    {subsidy.status}
                  </Badge>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Sanctioned:</span>
                    <span className="font-medium">{formatCurrency(subsidy.sanctioned)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Received:</span>
                    <span className="font-medium text-green-600">{formatCurrency(subsidy.received)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Pending:</span>
                    <span className={`font-medium ${subsidy.pending > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {formatCurrency(subsidy.pending)}
                    </span>
                  </div>
                </div>
                <div className="mt-3">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Progress</span>
                    <span>{subsidy.progress}%</span>
                  </div>
                  <Progress value={subsidy.progress} className="h-2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Financial Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-6 w-6 text-purple-600" />
            <span>AI Financial Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {aiInsights.map((insight, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-white rounded-lg">
                    {insight.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-800 mb-2">{insight.title}</h3>
                    <p className="text-sm text-gray-600 mb-3">{insight.description}</p>
                    <Badge className={insight.impactColor}>
                      {insight.impact}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Expenditure Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChart className="h-6 w-6 text-blue-600" />
              <span>Detailed Expenditure Breakdown</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    data={expenditureData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percentage }) => `${name}: ${percentage}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="amount"
                  >
                    {expenditureData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-6 w-6 text-purple-600" />
              <span>Expense Categories</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {expenditureData.slice(0, 6).map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium" title={item.description}>{item.name}</span>
                    <span className="text-gray-600">{formatCurrency(item.amount)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="h-2 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${item.percentage}%`,
                        backgroundColor: item.color
                      }}
                    />
                  </div>
                  <div className="text-xs text-gray-500 flex justify-between">
                    <span>{item.description}</span>
                    <span>{item.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dudh Sanstha Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building className="h-6 w-6 text-green-600" />
            <span>Dudh Sanstha-wise Financial Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border p-3 text-left">Sanstha Name</th>
                  <th className="border p-3 text-right">Milk Collected (L)</th>
                  <th className="border p-3 text-right">Avg Rate (₹/L)</th>
                  <th className="border p-3 text-right">Total Amount</th>
                  <th className="border p-3 text-right">Amount Paid</th>
                  <th className="border p-3 text-right">Pending</th>
                  <th className="border p-3 text-center">Performance</th>
                </tr>
              </thead>
              <tbody>
                {dudhSansthaData.map((sanstha, index) => (
                  <DudhSansthaDialog key={index} sanstha={sanstha}>
                    <tr className="hover:bg-gray-50 cursor-pointer">
                      <td className="border p-3 font-medium">{sanstha.name}</td>
                      <td className="border p-3 text-right">{sanstha.milkCollected.toLocaleString()}</td>
                      <td className="border p-3 text-right">₹{sanstha.avgRate.toFixed(2)}</td>
                      <td className="border p-3 text-right">{formatCurrency(sanstha.totalAmount)}</td>
                      <td className="border p-3 text-right">{formatCurrency(sanstha.amountPaid)}</td>
                      <td className="border p-3 text-right">
                        <span className={sanstha.pending > 0 ? 'text-red-600 font-semibold' : 'text-green-600'}>
                          {sanstha.pending > 0 ? formatCurrency(sanstha.pending) : '₹0'}
                        </span>
                      </td>
                      <td className="border p-3 text-center">
                        <Badge className={getPerformanceColor(sanstha.performance)}>
                          {sanstha.performance.replace('_', ' ').toUpperCase()}
                        </Badge>
                      </td>
                    </tr>
                  </DudhSansthaDialog>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Product Profitability */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-6 w-6 text-orange-600" />
            <span>Product-wise Profitability</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={productProfitData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="product" />
                <YAxis />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Bar dataKey="revenue" fill="#3B82F6" name="Revenue" />
                <Bar dataKey="cost" fill="#EF4444" name="Cost" />
                <Bar dataKey="profit" fill="#10B981" name="Profit" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 flex justify-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
              <span className="text-sm">Revenue</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span className="text-sm">Cost</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span className="text-sm">Profit</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FinanceDepartment;

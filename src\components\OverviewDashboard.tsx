
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Factory, 
  Milk, 
  TrendingUp, 
  Award, 
  Zap, 
  Thermometer,
  Users,
  Calendar
} from "lucide-react";

const OverviewDashboard = () => {
  const companyStats = [
    {
      title: "Daily Processing Capacity",
      value: "17 Lakh Litres",
      icon: <Milk className="h-8 w-8 text-sky-600" />,
      trend: "+12% vs last year"
    },
    {
      title: "Product Range",
      value: "6 Categories",
      icon: <Factory className="h-8 w-8 text-sky-600" />,
      detail: "<PERSON>, <PERSON>hee, Paneer, Shrikhand, Curd, Butter"
    },
    {
      title: "Solar Capacity",
      value: "6.5 MWp",
      icon: <Zap className="h-8 w-8 text-sky-600" />,
      detail: "Under Development"
    },
    {
      title: "Certifications",
      value: "3 ISO Certified",
      icon: <Award className="h-8 w-8 text-sky-600" />,
      detail: "ISO 9001, 22000, HACCP"
    }
  ];

  const currentChallenges = [
    { challenge: "Manual quality test reporting delays", severity: "high", impact: 85 },
    { challenge: "Packaging defects leading to rejections", severity: "high", impact: 78 },
    { challenge: "Cold chain energy inefficiency", severity: "medium", impact: 65 },
    { challenge: "Manual dispatch planning", severity: "medium", impact: 58 },
    { challenge: "Lack of real-time MIS insights", severity: "high", impact: 82 },
    { challenge: "Seasonal demand-supply mismatch", severity: "low", impact: 45 }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Company Overview */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Factory className="h-6 w-6 text-sky-600" />
            <span>Gokul Dairy Office Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {companyStats.map((stat, index) => (
              <div key={index} className="bg-gradient-to-r from-sky-50 to-blue-50 p-4 rounded-lg border border-sky-200">
                <div className="flex items-center space-x-3 mb-3">
                  {stat.icon}
                  <div>
                    <h3 className="font-semibold text-gray-900">{stat.title}</h3>
                    <p className="text-2xl font-bold text-sky-700">{stat.value}</p>
                  </div>
                </div>
                {stat.trend && (
                  <p className="text-sm text-green-600 flex items-center">
                    <TrendingUp className="h-4 w-4 mr-1" />
                    {stat.trend}
                  </p>
                )}
                {stat.detail && (
                  <p className="text-sm text-gray-600">{stat.detail}</p>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Challenges */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Thermometer className="h-6 w-6 text-red-600" />
            <span>Current Operational Challenges</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {currentChallenges.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 hover:shadow-sm transition-shadow">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <Badge className={getSeverityColor(item.severity)}>
                      {item.severity.toUpperCase()}
                    </Badge>
                    <h4 className="font-medium text-gray-900">{item.challenge}</h4>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-600">Business Impact:</span>
                    <Progress value={item.impact} className="flex-1 max-w-32" />
                    <span className="text-sm font-medium text-gray-700">{item.impact}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Vision Summary */}
      <Card className="border-sky-200 bg-gradient-to-r from-sky-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-6 w-6 text-sky-600" />
            <span>AI Transformation Vision</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Goals</h4>
              <ul className="space-y-2">
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-sky-600 rounded-full"></div>
                  <span className="text-sm">Improve product quality through CV-based detection</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-sky-600 rounded-full"></div>
                  <span className="text-sm">Reduce operational downtime with predictive maintenance</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-sky-600 rounded-full"></div>
                  <span className="text-sm">Optimize dispatch planning with AI forecasting</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-sky-600 rounded-full"></div>
                  <span className="text-sm">Save energy through ML optimization</span>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">3-Month Pilot Plan</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-sky-600" />
                  <span className="text-sm">Location: Main dairy processing plant</span>
                </div>
                <div className="bg-white p-3 rounded border border-sky-200">
                  <p className="text-sm font-medium mb-2">Technology Stack:</p>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="text-xs">YOLOv8/EdgeCV</Badge>
                    <Badge variant="outline" className="text-xs">XGBoost</Badge>
                    <Badge variant="outline" className="text-xs">PowerBI</Badge>
                    <Badge variant="outline" className="text-xs">Solar Forecasting</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OverviewDashboard;

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Send,
  Mic,
  Stethoscope,
  Heart,
  Activity,
  AlertTriangle,
  TrendingUp,
  Thermometer,
  Pill,
} from "lucide-react";
import { useSpeechRecognition } from "react-speech-kit";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { cn } from "@/lib/utils";

const quickHealthActions = [
  {
    title: "Animal Health Diagnosis",
    description: "AI-powered health assessment and diagnosis",
    icon: <Stethoscope className="h-5 w-5" />,
    action: "Analyze the health symptoms of cow ID COW-001: decreased milk production, loss of appetite, and elevated temperature",
  },
  {
    title: "Treatment Recommendations",
    description: "Evidence-based treatment protocols",
    icon: <Pill className="h-5 w-5" />,
    action: "Recommend treatment protocol for buffalo with mastitis symptoms including medication dosage and care instructions",
  },
  {
    title: "Preventive Care Plan",
    description: "Customized prevention strategies",
    icon: <Heart className="h-5 w-5" />,
    action: "Create a preventive care plan for dairy cattle in Satara region to prevent seasonal respiratory infections",
  },
  {
    title: "Emergency Response",
    description: "Urgent health situation guidance",
    icon: <AlertTriangle className="h-5 w-5" />,
    action: "Emergency protocol for suspected foot-and-mouth disease outbreak in dairy cattle - immediate steps required",
  },
];

const DoctorGPT = () => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [isListening, setIsListening] = useState(false);
  const bottomRef = useRef(null);

  const { listen, listening, stop, supported } = useSpeechRecognition({
    onResult: (result) => {
      let cleanResult = '';
      if (typeof result === 'string') {
        cleanResult = result.trim();
      } else if (result && typeof result === 'object') {
        if (result.transcript) {
          cleanResult = String(result.transcript).trim();
        } else if (result.text) {
          cleanResult = String(result.text).trim();
        }
      }

      if (cleanResult && cleanResult !== '[object Object]' && cleanResult.length > 0) {
        setNewMessage(cleanResult);
      }
    },
    onEnd: () => {
      setIsListening(false);
    },
    onError: (error) => {
      console.error("Speech recognition error:", error);
      setIsListening(false);
    },
  });

  useEffect(() => {
    const generateSessionId = () => {
      return Date.now().toString(36) + Math.random().toString(36).substr(2);
    };
    const newSessionId = generateSessionId();
    setSessionId(newSessionId);
    setMessages([
      {
        id: 1,
        content: "🩺 **Welcome to DoctorGPT!** \n\nI'm your AI-powered veterinary assistant specialized in **dairy cattle and buffalo healthcare** across Gokul's 6,000 sansthas. I can help you with:\n\n• **Health Diagnosis** - Analyze symptoms for 234K+ animals\n• **Treatment Plans** - Evidence-based treatment recommendations\n• **Preventive Care** - Customized prevention strategies for cows & buffalos\n• **Emergency Protocols** - Urgent health situation guidance\n• **Medication Dosage** - Safe and effective dosing guidelines\n\nHow can I assist you with dairy animal healthcare today?",
        type: "ai",
        time: new Date().toLocaleTimeString(),
      }
    ]);
  }, []);

  const handleSendMessage = async (messageText = null) => {
    let messageToSend = messageText || newMessage;
    
    if (typeof messageToSend !== 'string') {
      messageToSend = String(messageToSend);
    }
    
    messageToSend = messageToSend.trim();
    
    if (!messageToSend || messageToSend === '[object Object]') {
      return;
    }

    const userMessage = {
      id: messages.length + 1,
      content: messageToSend,
      type: "user",
      time: new Date().toLocaleTimeString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setNewMessage("");
    setIsListening(false);
    stop();
    setIsTyping(true);

    try {
      // Simulate DoctorGPT API call with healthcare-specific responses
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      let response = "";
      const lowerMessage = messageToSend.toLowerCase();
      
      if (lowerMessage.includes("diagnosis") || lowerMessage.includes("symptoms")) {
        response = `🔍 **Health Assessment Analysis**

Based on the symptoms described, here's my preliminary assessment:

**Primary Concerns:**
• Decreased milk production may indicate stress, infection, or nutritional deficiency
• Loss of appetite could suggest digestive issues or systemic illness
• Elevated temperature indicates possible infection or inflammatory response

**Recommended Actions:**
1. **Immediate**: Check vital signs (temperature, heart rate, respiratory rate)
2. **Laboratory**: Collect blood samples for complete blood count and metabolic panel
3. **Physical Exam**: Thorough examination of udder, mouth, and digestive system
4. **Isolation**: Consider temporary isolation until diagnosis is confirmed

**Veterinary Consultation**: Recommend immediate veterinary examination within 4-6 hours.

Would you like specific treatment protocols or need guidance on emergency care?`;
      } else if (lowerMessage.includes("treatment") || lowerMessage.includes("medication")) {
        response = `💊 **Treatment Protocol Recommendation**

**For Mastitis Management:**

**Immediate Treatment:**
• **Antibiotic Therapy**: Intramammary infusion with appropriate antibiotic
• **Anti-inflammatory**: NSAIDs to reduce inflammation and pain
• **Supportive Care**: Frequent milking, warm compresses

**Medication Dosage:**
• Penicillin G: 22,000 IU/kg body weight, IM, twice daily for 5 days
• Meloxicam: 0.5 mg/kg body weight, IV/IM, once daily for 3 days

**Monitoring:**
• Daily temperature checks
• Milk quality assessment
• Udder examination for improvement

**Prevention:**
• Proper milking hygiene
• Teat dipping post-milking
• Regular equipment sanitization

⚠️ **Important**: Always consult with a licensed veterinarian before administering medications.`;
      } else if (lowerMessage.includes("prevention") || lowerMessage.includes("preventive")) {
        response = `🛡️ **Preventive Care Plan - Respiratory Health**

**Seasonal Prevention Strategy:**

**Environmental Management:**
• Ensure proper ventilation in housing areas
• Maintain optimal humidity levels (60-70%)
• Regular cleaning and disinfection of living spaces

**Nutritional Support:**
• Vitamin C supplementation: 500mg daily per animal
• Zinc supplementation for immune support
• High-quality hay and balanced nutrition

**Vaccination Schedule:**
• Annual respiratory disease vaccination
• Booster shots before seasonal changes
• Maintain vaccination records

**Monitoring Protocol:**
• Weekly health checks during high-risk seasons
• Temperature monitoring for early detection
• Respiratory rate assessment

**Early Warning Signs:**
• Nasal discharge
• Coughing or labored breathing
• Reduced feed intake
• Lethargy

This plan should reduce respiratory infections by 70-80% when properly implemented.`;
      } else if (lowerMessage.includes("emergency") || lowerMessage.includes("outbreak")) {
        response = `🚨 **EMERGENCY PROTOCOL - Suspected FMD Outbreak**

**IMMEDIATE ACTIONS (First 30 minutes):**

1. **Isolation**: Immediately isolate affected animals
2. **Quarantine**: Stop all animal movement on and off premises
3. **Contact**: Notify veterinary authorities and local animal health department
4. **Documentation**: Record all symptoms, affected animals, and timeline

**Biosecurity Measures:**
• Disinfect all equipment and vehicles
• Limit human movement between areas
• Use dedicated clothing and footwear for affected areas
• Establish disinfection stations at entry/exit points

**Sample Collection:**
• Collect vesicular fluid samples (if safe to do so)
• Blood samples from affected and contact animals
• Follow proper sample handling protocols

**Communication:**
• Notify neighboring farms immediately
• Contact insurance provider
• Prepare for potential media inquiries

**DO NOT:**
• Move any animals
• Allow visitors on premises
• Dispose of any materials without guidance

⚠️ **CRITICAL**: This is a notifiable disease. Legal reporting is mandatory within 24 hours.

Emergency Hotline: Contact your regional veterinary emergency service immediately.`;
      } else {
        response = `🩺 **DoctorGPT Healthcare Assistant**

I'm here to help with veterinary healthcare questions. I can assist with:

• **Symptom Analysis** - Describe animal symptoms for assessment
• **Treatment Guidance** - Get evidence-based treatment recommendations  
• **Preventive Care** - Develop prevention strategies
• **Emergency Protocols** - Urgent health situation guidance
• **Medication Information** - Dosage and administration guidance

Please describe the specific health concern or question you have about your animals, and I'll provide detailed, professional guidance.

For immediate emergencies, always contact your local veterinarian first.`;
      }

      const botMessage = {
        id: messages.length + 2,
        content: response,
        type: "ai",
        time: new Date().toLocaleTimeString(),
      };

      setMessages((prev) => [...prev, botMessage]);
    } catch (err) {
      console.error("Error:", err);
      setMessages((prev) => [
        ...prev,
        {
          id: messages.length + 2,
          content: "I apologize, but I'm experiencing technical difficulties. Please try again or contact your veterinarian for immediate assistance.",
          type: "ai",
          time: new Date().toLocaleTimeString(),
        },
      ]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleQuickAction = (actionMessage) => {
    handleSendMessage(actionMessage);
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleMic = () => {
    if (!supported) {
      alert("Speech recognition is not supported in your browser. Please use Chrome, Edge, or Safari.");
      return;
    }

    if (listening || isListening) {
      stop();
      setIsListening(false);
    } else {
      setNewMessage("");
      setIsListening(true);
      try {
        listen({
          lang: "en-US",
          continuous: true,
          interimResults: true,
        });
      } catch (error) {
        console.error("Error starting speech recognition:", error);
        setIsListening(false);
        alert("Failed to start speech recognition. Please check your microphone permissions.");
      }
    }
  };

  useEffect(() => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  return (
    <div className="h-full bg-gradient-to-br from-green-50 to-blue-50 p-6">
      <Card className="w-full h-full flex flex-col shadow-xl border-green-100">
        <CardHeader className="flex-shrink-0 bg-white border-b border-green-100">
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-r from-green-600 to-green-700 p-3 rounded-full">
                <Stethoscope className="h-8 w-8 text-white" />
              </div>
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900">DoctorGPT</h2>
                <p className="text-sm text-green-600">AI Veterinary Healthcare Assistant</p>
                <Badge variant="outline" className="mt-1 bg-green-50 text-green-700 border-green-200">
                  🐄 Specialized for Dairy Animals
                </Badge>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-hidden bg-gradient-to-br from-green-50 to-blue-50">
          <ScrollArea className="h-full p-4 space-y-4">
            {messages.length === 1 && (
              <div className="grid grid-cols-2 gap-4 max-w-4xl w-full mb-6">
                {quickHealthActions.map((action, index) => (
                  <Card
                    key={index}
                    className="cursor-pointer hover:shadow-lg transition-all duration-300 hover:border-green-400 border-green-100 hover:scale-105 transform active:scale-95"
                    onClick={() => handleQuickAction(action.action)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="bg-gradient-to-r from-green-100 to-green-200 p-2 rounded-lg flex-shrink-0 text-green-600">
                          {action.icon}
                        </div>
                        <div className="min-w-0">
                          <h4 className="font-medium text-gray-900 text-sm">
                            {action.title}
                          </h4>
                          <p className="text-xs text-green-600 mt-1">
                            {action.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.type === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`${
                    message.type === "user"
                      ? "max-w-xs lg:max-w-md"
                      : "max-w-sm lg:max-w-lg"
                  } p-3 rounded-lg ${
                    message.type === "user"
                      ? "bg-gradient-to-r from-green-600 to-green-700 text-white shadow-md"
                      : "bg-white shadow-md border border-green-100"
                  }`}
                >
                  <div className="text-sm">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                        ul: ({ children }) => <ul className="list-disc list-inside mb-2">{children}</ul>,
                        ol: ({ children }) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
                        li: ({ children }) => <li className="mb-1">{children}</li>,
                        h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                        h2: ({ children }) => <h2 className="text-base font-semibold mb-2">{children}</h2>,
                        h3: ({ children }) => <h3 className="text-sm font-medium mb-1">{children}</h3>,
                        strong: ({ children }) => <strong className="font-bold">{children}</strong>,
                        code: ({ children, className }) => {
                          const isInline = !className;
                          return isInline ? (
                            <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                          ) : (
                            <code className="block bg-gray-100 p-2 rounded text-xs font-mono overflow-x-auto">{children}</code>
                          );
                        },
                      }}
                    >
                      {typeof message.content === 'string' ? message.content : String(message.content || '')}
                    </ReactMarkdown>
                  </div>
                  {message.time && (
                    <p className="text-xs mt-2 opacity-70">{message.time}</p>
                  )}
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start">
                <div className="max-w-sm lg:max-w-lg p-3 rounded-lg bg-white shadow-md border border-green-100">
                  <div className="flex items-center space-x-2">
                    <div className="animate-pulse text-green-600">
                      🩺 Analyzing your healthcare query...
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={bottomRef} />
          </ScrollArea>
        </CardContent>

        <div className="p-4 bg-white border-t border-green-100 flex-shrink-0">
          <div className="flex space-x-2">
            <Input
              placeholder={
                listening || isListening
                  ? "🎤 Listening for healthcare questions..."
                  : "Ask about animal health, symptoms, treatments..."
              }
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              className={cn(
                "flex-1 border-green-200 focus:border-green-400 focus:ring-green-400",
                (listening || isListening) &&
                  "border-red-400 ring-4 ring-red-100 bg-red-50"
              )}
            />

            <Button
              type="button"
              onClick={toggleMic}
              disabled={!supported}
              className={cn(
                "flex-shrink-0 text-white relative",
                listening || isListening
                  ? "bg-red-500 hover:bg-red-600 animate-pulse"
                  : "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800",
                !supported && "opacity-50 cursor-not-allowed"
              )}
              title={
                !supported
                  ? "Speech recognition not supported"
                  : listening || isListening
                  ? "Click to stop recording"
                  : "Click to start voice input"
              }
            >
              <Mic className={cn(
                "h-4 w-4",
                (listening || isListening) && "animate-bounce"
              )} />
              {(listening || isListening) && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full animate-ping" />
              )}
            </Button>

            <Button
              onClick={() => handleSendMessage()}
              size="icon"
              disabled={!newMessage.trim()}
              className={cn(
                "flex-shrink-0",
                newMessage.trim()
                  ? "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              )}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DoctorGPT;

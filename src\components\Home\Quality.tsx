import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Shield, Award, Thermometer, Zap, Users, Heart } from "lucide-react";
import processingFacility from "/hero2.avif";
import buffaloImage from "/hero.jpg";

const Quality = () => {
  const qualityFeatures = [
    {
      icon: Shield,
      title: "ISO 9001:2015 Certified",
      description: "International quality management standards ensure consistent excellence in every product.",
      color: "text-gray-800"
    },
    {
      icon: Thermometer,
      title: "Advanced Pasteurization",
      description: "State-of-the-art pasteurization technology eliminates harmful bacteria while preserving nutrients.",
      color: "text-primary"
    },
    {
      icon: Zap,
      title: "Cold Chain Management",
      description: "Unbroken cold chain from processing to delivery maintains freshness and quality.",
      color: "text-dairy-green"
    },
    {
      icon: Award,
      title: "96.8% Quality Score",
      description: "Consistently high quality scores from rigorous testing and AI-powered monitoring systems.",
      color: "text-dairy-orange"
    }
  ];

  const stats = [
    { value: "17K", label: "Litres Daily Capacity", trend: "****%" },
    { value: "96.8%", label: "Quality Score", trend: "+0.5%" },
    { value: "94.5%", label: "On-time Delivery", trend: "****%" },
    { value: "87.2%", label: "Energy Efficiency", trend: "Optimizing" }
  ];

  return (
    <section id="quality" className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <Badge variant="outline" className="mb-4">Quality Assurance</Badge>
          <h2 className="text-4xl lg:text-6xl font-bold text-primary mb-6">
            Uncompromising Quality Standards
          </h2>
          <p className="text-xl text-gray-900">
            Our commitment to quality is reflected in every drop of milk and every dairy product we create. Advanced technology meets traditional care.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center p-6 hover:shadow-lg transition-all duration-300 border-border">
              <CardContent className="space-y-2 p-0">
                <div className="text-3xl lg:text-4xl font-bold text-primary">{stat.value}</div>
                <div className="text-sm text-gray-900">{stat.label}</div>
                <div className="text-xs text-gray-800 font-medium">{stat.trend}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quality Features */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div className="space-y-8">
            <div>
              <h3 className="text-3xl font-bold text-primary mb-4">
                Advanced Quality Control Systems
              </h3>
              <p className="text-lg text-gray-900 mb-8">
                Our facility employs cutting-edge technology and rigorous testing protocols to ensure every product meets the highest standards of purity and nutrition.
              </p>
            </div>

            <div className="grid gap-6">
              {qualityFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="flex items-start space-x-4 p-4 rounded-xl hover:bg-background transition-colors duration-300">
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center`}>
                      <IconComponent className={`w-6 h-6 ${feature.color}`} />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-foreground mb-2">{feature.title}</h4>
                      <p className="text-gray-900 text-sm">{feature.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="relative">
            {/* Main facility image */}
            <div className="relative">
              <img 
                src={processingFacility} 
                alt="Processing Facility" 
                className="w-full h-96 object-cover rounded-2xl shadow-xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/30 to-transparent rounded-2xl"></div>
            </div>
            
            {/* Buffalo image floating overlay */}
            <div className="absolute -top-8 -right-8 w-40 h-28 rounded-xl overflow-hidden shadow-2xl border-4 border-white/80 transform rotate-6 hover:rotate-0 transition-transform duration-500">
              <img 
                src={buffaloImage} 
                alt="Buffalo in Pasture" 
                className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
              />
            </div>
            
            {/* Overlay Stats */}
            <div className="absolute bottom-6 left-6 right-6">
              <div className="bg-background/90 backdrop-blur-sm rounded-xl p-4 border border-border">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-900">Live Production</div>
                    <div className="text-2xl font-bold text-primary">16.2K Litres</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-secondary rounded-full animate-pulse"></div>
                    <span className="text-sm text-gray-800 font-medium">LIVE</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Certification Section */}
        <div className="bg-gradient-to-r from-secondary/10 to-primary/10 rounded-3xl p-8 lg:p-12">
          <div className="text-center space-y-6">
            <div className="flex justify-center items-center space-x-4 mb-6">
              <div className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center">
                <Award className="w-8 h-8 text-gray-800-foreground" />
              </div>
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                <Shield className="w-8 h-8 text-primary-foreground" />
              </div>
              <div className="w-16 h-16 bg-dairy-green rounded-full flex items-center justify-center">
                <Heart className="w-8 h-8 text-white" />
              </div>
            </div>
            
            <h3 className="text-3xl font-bold text-primary">
              Trusted by Thousands of Families
            </h3>
            <p className="text-lg text-gray-900 max-w-2xl mx-auto">
              Our ISO 9001:2015 certification and consistent quality scores reflect our unwavering commitment to delivering the purest, most nutritious dairy products to your family.
            </p>
            
            <div className="flex justify-center items-center space-x-8 pt-6">
              <div className="text-center">
                <Users className="w-8 h-8 text-primary mx-auto mb-2" />
                <div className="font-semibold text-foreground">5000+ Families</div>
                <div className="text-sm text-gray-900">Trust Our Quality</div>
              </div>
              <div className="w-px h-16 bg-border"></div>
              <div className="text-center">
                <Heart className="w-8 h-8 text-gray-800 mx-auto mb-2" />
                <div className="font-semibold text-foreground">100% Natural</div>
                <div className="text-sm text-gray-900">No Preservatives</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Quality;
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Brain, BarChart3, Activity, TrendingUp, Database, Cpu, Eye, Sparkles, Bot, Zap, Award, Shield, Heart, Users } from "lucide-react";
import processingFacility from "/hero2.avif";
import buffaloImage from "/hero.jpg";

const Quality = () => {
  const analyticsFeatures = [
    {
      icon: Brain,
      title: "AI-Powered Analytics",
      description: "Advanced machine learning algorithms analyze 234K+ animals with 97.8% prediction accuracy.",
      color: "text-blue-400",
      gradient: "from-blue-500 to-purple-600"
    },
    {
      icon: BarChart3,
      title: "Real-time Dashboards",
      description: "Interactive data visualization across 12 talukas with live monitoring and instant insights.",
      color: "text-green-400",
      gradient: "from-green-500 to-blue-500"
    },
    {
      icon: Activity,
      title: "Predictive Monitoring",
      description: "Proactive health monitoring and early warning systems prevent issues before they occur.",
      color: "text-purple-400",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      icon: Database,
      title: "Big Data Processing",
      description: "Process massive datasets from 6K+ sansthas to generate actionable business intelligence.",
      color: "text-yellow-400",
      gradient: "from-yellow-500 to-orange-500"
    }
  ];

  const aiStats = [
    { value: "234K+", label: "Animals Monitored", trend: "+12.5%", icon: Activity },
    { value: "97.8%", label: "AI Accuracy", trend: "****%", icon: Brain },
    { value: "6K+", label: "Sansthas Connected", trend: "****%", icon: Database },
    { value: "12", label: "Talukas Covered", trend: "100%", icon: BarChart3 }
  ];

  return (
    <section id="quality" className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-16 grid-rows-16 h-full w-full">
          {[...Array(256)].map((_, i) => (
            <div
              key={i}
              className="border border-white/5 animate-pulse"
              style={{ animationDelay: `${Math.random() * 4}s` }}
            />
          ))}
        </div>
      </div>

      {/* Floating AI Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${5 + Math.random() * 2}s`
            }}
          >
            <Sparkles className="h-5 w-5 text-blue-300/20" />
          </div>
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* AI Analytics Header */}
        <div className="text-center max-w-4xl mx-auto mb-16 animate-fade-in">
          <Badge className="mb-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 text-sm font-medium">
            <Brain className="w-4 h-4 mr-2 animate-pulse" />
            Advanced Analytics
          </Badge>
          <h2 className="text-4xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent">
            AI-Driven Insights & Analytics
          </h2>
          <p className="text-xl text-blue-100 leading-relaxed">
            Harness the power of artificial intelligence and big data analytics to optimize dairy operations
            across Kolhapur district with unprecedented precision and efficiency.
          </p>
        </div>

        {/* AI Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {aiStats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <Card key={index} className="text-center p-6 backdrop-blur-lg bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-500 hover:-translate-y-1 animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
                <CardContent className="space-y-4 p-0">
                  <div className="flex justify-center">
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  <div className="text-3xl lg:text-4xl font-bold text-white">{stat.value}</div>
                  <div className="text-sm text-blue-200 font-medium">{stat.label}</div>
                  <div className="text-xs text-green-400 font-semibold flex items-center justify-center gap-1">
                    <TrendingUp className="w-3 h-3" />
                    {stat.trend}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Quality Features */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge className="w-fit bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2">
                <Bot className="w-4 h-4 mr-2 animate-bounce" />
                AI-Powered Intelligence
              </Badge>
              <h3 className="text-3xl lg:text-4xl font-bold text-white">
                Next-Generation Analytics Platform
              </h3>
              <p className="text-lg text-blue-100 leading-relaxed">
                Our advanced AI platform combines machine learning, big data processing, and predictive analytics
                to revolutionize dairy farming operations across Kolhapur district.
              </p>
            </div>

            <div className="grid gap-6">
              {analyticsFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="flex items-start space-x-4 p-6 rounded-xl backdrop-blur-sm bg-white/5 border border-white/10 hover:bg-white/10 transition-all duration-300 animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
                    <div className={`p-3 rounded-full bg-gradient-to-r ${feature.gradient} flex-shrink-0 shadow-lg`}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div className="space-y-2">
                      <h4 className="text-xl font-semibold text-white flex items-center gap-2">
                        {feature.title}
                        <Zap className="w-4 h-4 text-yellow-400 animate-pulse" />
                      </h4>
                      <p className="text-blue-100 leading-relaxed">{feature.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="relative">
            {/* Main facility image */}
            <div className="relative">
              <img 
                src={processingFacility} 
                alt="Processing Facility" 
                className="w-full h-96 object-cover rounded-2xl shadow-xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/30 to-transparent rounded-2xl"></div>
            </div>
            
            {/* Buffalo image floating overlay */}
            <div className="absolute -top-8 -right-8 w-40 h-28 rounded-xl overflow-hidden shadow-2xl border-4 border-white/80 transform rotate-6 hover:rotate-0 transition-transform duration-500">
              <img 
                src={buffaloImage} 
                alt="Buffalo in Pasture" 
                className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
              />
            </div>
            
            {/* Overlay Stats */}
            <div className="absolute bottom-6 left-6 right-6">
              <div className="bg-background/90 backdrop-blur-sm rounded-xl p-4 border border-border">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-900">Live Production</div>
                    <div className="text-2xl font-bold text-primary">16.2K Litres</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-secondary rounded-full animate-pulse"></div>
                    <span className="text-sm text-gray-800 font-medium">LIVE</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Certification Section */}
        <div className="bg-gradient-to-r from-secondary/10 to-primary/10 rounded-3xl p-8 lg:p-12">
          <div className="text-center space-y-6">
            <div className="flex justify-center items-center space-x-4 mb-6">
              <div className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center">
                <Award className="w-8 h-8 text-gray-800-foreground" />
              </div>
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                <Shield className="w-8 h-8 text-primary-foreground" />
              </div>
              <div className="w-16 h-16 bg-dairy-green rounded-full flex items-center justify-center">
                <Heart className="w-8 h-8 text-white" />
              </div>
            </div>
            
            <h3 className="text-3xl font-bold text-primary">
              Trusted by Thousands of Families
            </h3>
            <p className="text-lg text-gray-900 max-w-2xl mx-auto">
              Our ISO 9001:2015 certification and consistent quality scores reflect our unwavering commitment to delivering the purest, most nutritious dairy products to your family.
            </p>
            
            <div className="flex justify-center items-center space-x-8 pt-6">
              <div className="text-center">
                <Users className="w-8 h-8 text-primary mx-auto mb-2" />
                <div className="font-semibold text-foreground">5000+ Families</div>
                <div className="text-sm text-gray-900">Trust Our Quality</div>
              </div>
              <div className="w-px h-16 bg-border"></div>
              <div className="text-center">
                <Heart className="w-8 h-8 text-gray-800 mx-auto mb-2" />
                <div className="font-semibold text-foreground">100% Natural</div>
                <div className="text-sm text-gray-900">No Preservatives</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Quality;
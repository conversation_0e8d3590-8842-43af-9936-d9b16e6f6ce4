
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  X, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Bell 
} from "lucide-react";

interface NotificationPanelProps {
  onClose: () => void;
}

const NotificationPanel = ({ onClose }: NotificationPanelProps) => {
  const notifications = [
    {
      id: 1,
      type: "critical",
      title: "Cold Storage Temperature Alert",
      message: "Cold Storage B: 6.2°C (Target: 4-5°C). Compressor efficiency down 15%.",
      time: "2 minutes ago",
      department: "Energy Management",
      location: "Building A - Cold Storage Unit B"
    },
    {
      id: 2,
      type: "warning",
      title: "Packaging Line Seal Defects",
      message: "Line 3 seal defect rate: 2.1% (Target: <1%). Temperature variance detected.",
      time: "5 minutes ago",
      department: "Quality Control",
      location: "Production Floor B - Line 3"
    },
    {
      id: 3,
      type: "info",
      title: "Production Target Achievement",
      message: "Daily milk processing: 16.2K litres completed successfully",
      time: "30 minutes ago",
      department: "Production",
      location: "Main Processing Unit"
    },
    {
      id: 4,
      type: "info",
      title: "AI Route Optimization Alert",
      message: "New route optimization available for Kolhapur district - 18% fuel savings possible",
      time: "1 hour ago",
      department: "Sales & Distribution",
      location: "Dispatch Center"
    },
    {
      id: 5,
      type: "info",
      title: "Festival Season Forecast",
      message: "AI predicts 15% increase in ghee demand for upcoming Diwali season",
      time: "2 hours ago",
      department: "Sales & Distribution",
      location: "Planning Office"
    }
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'info': return <CheckCircle className="h-4 w-4 text-blue-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'critical': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      case 'info': return 'bg-blue-50 border-blue-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const criticalCount = notifications.filter(n => n.type === 'critical').length;
  const warningCount = notifications.filter(n => n.type === 'warning').length;

  return (
    <Card className="h-full rounded-none border-l-2 border-sky-200">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
        <CardTitle className="flex items-center space-x-2">
          <Bell className="h-5 w-5 text-sky-600" />
          <span>Notifications</span>
          <div className="flex items-center space-x-1">
            {criticalCount > 0 && (
              <Badge className="bg-red-500 text-white text-xs">
                {criticalCount} Critical
              </Badge>
            )}
            {warningCount > 0 && (
              <Badge className="bg-yellow-500 text-white text-xs">
                {warningCount} Warning
              </Badge>
            )}
          </div>
        </CardTitle>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      
      <CardContent className="space-y-4 max-h-96 overflow-y-auto p-4">
        {notifications.map((notification) => (
          <div 
            key={notification.id}
            className={`p-4 rounded-lg border hover:shadow-sm transition-shadow ${getTypeColor(notification.type)}`}
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center space-x-2">
                {getTypeIcon(notification.type)}
                <span className="font-medium text-sm text-gray-900">
                  {notification.title}
                </span>
              </div>
              <Badge 
                className={`text-xs ${
                  notification.type === 'critical' ? 'bg-red-100 text-red-800' :
                  notification.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}
              >
                {notification.type.toUpperCase()}
              </Badge>
            </div>
            
            <p className="text-sm text-gray-700 mb-3 leading-relaxed">
              {notification.message}
            </p>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Badge variant="outline" className="text-xs">
                  {notification.department}
                </Badge>
                <span className="text-xs text-gray-500">
                  {notification.time}
                </span>
              </div>
              
              <div className="text-xs text-gray-600 bg-white/50 p-2 rounded">
                📍 {notification.location}
              </div>
            </div>
          </div>
        ))}
        
        {/* Summary Footer */}
        <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 mb-1">
              {notifications.length} Total Notifications
            </div>
            <div className="text-sm text-gray-600">
              Stay updated with real-time alerts across all departments
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationPanel;

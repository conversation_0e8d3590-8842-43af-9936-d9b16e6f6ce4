import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Building2, Database, Activity, Award } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-gray-800 text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Column */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                <Building2 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">गोकुळ दूध डेयरी</h3>
                <p className="text-sm text-gray-300">Professional Dairy Solutions</p>
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Professional dairy management solutions serving the region with modern technology
              and comprehensive support for all your dairy operation needs.
            </p>
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-blue-600 text-white border-none">
                <Activity className="w-3 h-3 mr-1" />
                Professional
              </Badge>
              <Badge className="bg-green-600 text-white border-none">
                <Database className="w-3 h-3 mr-1" />
                Reliable
              </Badge>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-white">
              Quick Links
            </h4>
            <nav className="space-y-3">
              <a href="#home" className="block text-gray-300 hover:text-white transition-colors">
                Home
              </a>
              <a href="#about" className="block text-gray-300 hover:text-white transition-colors">
                About
              </a>
              <a href="#products" className="block text-gray-300 hover:text-white transition-colors">
                Solutions
              </a>
              <a href="#quality" className="block text-gray-300 hover:text-white transition-colors">
                Quality
              </a>
              <a href="#contact" className="block text-gray-300 hover:text-white transition-colors">
                Contact
              </a>
            </nav>
          </div>

          {/* Services */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-white">
              Our Services
            </h4>
            <div className="space-y-3">
              <div className="text-gray-300">
                Dairy Management
              </div>
              <div className="text-gray-300">
                Quality Control
              </div>
              <div className="text-gray-300">
                Analytics & Reporting
              </div>
              <div className="text-gray-300">
                Professional Support
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-white">
              Contact Information
            </h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Phone className="w-5 h-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-gray-300">+91 98765 43210</p>
                  <p className="text-gray-300">+91 87654 32109</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-gray-300"><EMAIL></p>
                  <p className="text-gray-300"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-gray-300">Kolhapur, Maharashtra</p>
                  <p className="text-gray-300">India</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-gray-600">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <div className="flex items-center space-x-2">
                <Award className="w-5 h-5 text-yellow-400" />
                <span className="text-sm text-white">Professional Service</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm text-gray-300">Quality Assured</span>
              </div>
              <div className="text-sm text-gray-300">
                Serving Kolhapur District
              </div>
            </div>

            <div className="text-sm text-gray-300">
              © 2024 गोकुळ दूध डेयरी. All rights reserved.
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Brain, Bot, Database, Activity, Sparkles, Zap, Cpu, Award } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="grid grid-cols-20 grid-rows-20 h-full w-full">
          {[...Array(400)].map((_, i) => (
            <div
              key={i}
              className="border border-white/10 animate-pulse"
              style={{ animationDelay: `${Math.random() * 5}s` }}
            />
          ))}
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(10)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${6 + Math.random() * 2}s`
            }}
          >
            <Sparkles className="h-3 w-3 text-blue-300/20" />
          </div>
        ))}
      </div>

      <div className="container mx-auto px-4 py-16 relative z-10">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* AI Brand Column */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Brain className="w-6 h-6 text-white animate-pulse" />
              </div>
              <div>
                <h3 className="text-xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">गोकुळ AI Platform</h3>
                <p className="text-sm text-blue-200">Intelligent Dairy Solutions</p>
              </div>
            </div>
            <p className="text-blue-100 leading-relaxed">
              Revolutionizing dairy operations through advanced AI technology, predictive analytics, and intelligent automation
              across Kolhapur district's 12 talukas and 6,000 sansthas.
            </p>
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-none">
                <Bot className="w-3 h-3 mr-1 animate-bounce" />
                AI-Powered
              </Badge>
              <Badge className="bg-gradient-to-r from-green-500 to-blue-500 text-white border-none">
                <Database className="w-3 h-3 mr-1 animate-pulse" />
                Real-time Analytics
              </Badge>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-white flex items-center gap-2">
              <Cpu className="w-5 h-5 text-blue-400 animate-spin" />
              AI Solutions
            </h4>
            <nav className="space-y-3">
              <a href="#home" className="block text-blue-200 hover:text-white transition-colors flex items-center gap-2">
                <Activity className="w-4 h-4" />
                AI Dashboard
              </a>
              <a href="#about" className="block text-blue-200 hover:text-white transition-colors flex items-center gap-2">
                <Brain className="w-4 h-4" />
                Machine Learning
              </a>
              <a href="#products" className="block text-blue-200 hover:text-white transition-colors flex items-center gap-2">
                <Database className="w-4 h-4" />
                Analytics Platform
              </a>
              <a href="#quality" className="block text-blue-200 hover:text-white transition-colors flex items-center gap-2">
                <Bot className="w-4 h-4" />
                Predictive Insights
              </a>
              <a href="#contact" className="block text-blue-200 hover:text-white transition-colors flex items-center gap-2">
                <Zap className="w-4 h-4" />
                AI Support
              </a>
            </nav>
          </div>

          {/* Products */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold">Our Products</h4>
            <div className="space-y-3">
              <div className="text-primary-foreground/80">Fresh Milk</div>
              <div className="text-primary-foreground/80">Toned Milk</div>
              <div className="text-primary-foreground/80">Cow Milk</div>
              <div className="text-primary-foreground/80">Dairy Products</div>
              <div className="text-primary-foreground/80">Custom Orders</div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold">Contact Information</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Phone className="w-5 h-5 text-primary-foreground/80 mt-0.5" />
                <div>
                  <p className="text-primary-foreground/80">+91 98765 43210</p>
                  <p className="text-primary-foreground/80">+91 87654 32109</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-primary-foreground/80 mt-0.5" />
                <div>
                  <p className="text-primary-foreground/80"><EMAIL></p>
                  <p className="text-primary-foreground/80"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-primary-foreground/80 mt-0.5" />
                <div>
                  <p className="text-primary-foreground/80">Kolhapur, Maharashtra</p>
                  <p className="text-primary-foreground/80">India</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-primary-foreground/20">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Award className="w-5 h-5" />
                <span className="text-sm">ISO 9001:2015 Certified</span>
              </div>
              <div className="text-sm opacity-80">
                Daily Capacity: 17,000 Litres
              </div>
            </div>
            
            <div className="text-sm text-primary-foreground/80">
              © 2024 Gokul Dudh Dairy. All rights reserved.
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
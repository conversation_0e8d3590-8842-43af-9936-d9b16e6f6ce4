import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Award, Shield, Heart } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Column */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-primary-foreground/20 rounded-full flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-xl">गो</span>
              </div>
              <div>
                <h3 className="text-xl font-bold">गोकुल दूध डेयरी</h3>
                <p className="text-sm opacity-80">Gokul <PERSON></p>
              </div>
            </div>
            <p className="text-primary-foreground/80 leading-relaxed">
              Committed to delivering pure, nutritious dairy products with uncompromising quality standards. From our farm to your family.
            </p>
            <div className="flex space-x-2">
              <Badge variant="secondary" className="bg-primary-foreground/20 text-primary-foreground border-none">
                <Shield className="w-3 h-3 mr-1" />
                ISO Certified
              </Badge>
              <Badge variant="secondary" className="bg-primary-foreground/20 text-primary-foreground border-none">
                <Heart className="w-3 h-3 mr-1" />
                100% Natural
              </Badge>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold">Quick Links</h4>
            <nav className="space-y-3">
              <a href="#home" className="block text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Home
              </a>
              <a href="#about" className="block text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                About Us
              </a>
              <a href="#products" className="block text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Our Products
              </a>
              <a href="#quality" className="block text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Quality Assurance
              </a>
              <a href="#contact" className="block text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Contact
              </a>
            </nav>
          </div>

          {/* Products */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold">Our Products</h4>
            <div className="space-y-3">
              <div className="text-primary-foreground/80">Fresh Milk</div>
              <div className="text-primary-foreground/80">Toned Milk</div>
              <div className="text-primary-foreground/80">Cow Milk</div>
              <div className="text-primary-foreground/80">Dairy Products</div>
              <div className="text-primary-foreground/80">Custom Orders</div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold">Contact Information</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Phone className="w-5 h-5 text-primary-foreground/80 mt-0.5" />
                <div>
                  <p className="text-primary-foreground/80">+91 98765 43210</p>
                  <p className="text-primary-foreground/80">+91 87654 32109</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-primary-foreground/80 mt-0.5" />
                <div>
                  <p className="text-primary-foreground/80"><EMAIL></p>
                  <p className="text-primary-foreground/80"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-primary-foreground/80 mt-0.5" />
                <div>
                  <p className="text-primary-foreground/80">Kolhapur, Maharashtra</p>
                  <p className="text-primary-foreground/80">India</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-primary-foreground/20">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Award className="w-5 h-5" />
                <span className="text-sm">ISO 9001:2015 Certified</span>
              </div>
              <div className="text-sm opacity-80">
                Daily Capacity: 17,000 Litres
              </div>
            </div>
            
            <div className="text-sm text-primary-foreground/80">
              © 2024 Gokul Dudh Dairy. All rights reserved.
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Truck, 
  TrendingUp, 
  Calendar,
  MapPin,
  Package,
  Clock,
  Target,
  BarChart3,
  RefreshCw
} from "lucide-react";

const DispatchOptimization = () => {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [lastOptimized, setLastOptimized] = useState("2 hours ago");

  const demandForecast = [
    { product: "Milk (500ml)", predicted: 12500, actual: 12200, accuracy: 97.6, trend: "up" },
    { product: "Curd (200g)", predicted: 8900, actual: 9100, accuracy: 97.8, trend: "up" },
    { product: "Ghee (500g)", predicted: 3200, actual: 3050, accuracy: 95.3, trend: "down" },
    { product: "<PERSON><PERSON> (250g)", predicted: 2800, actual: 2900, accuracy: 96.4, trend: "up" },
    { product: "Butter (100g)", predicted: 1500, actual: 1450, accuracy: 96.7, trend: "stable" },
    { product: "Shrikhand (200g)", predicted: 1200, actual: 1180, accuracy: 98.3, trend: "stable" }
  ];

  const dispatchRoutes = [
    {
      route: "Route A - Central Zone",
      vehicles: 8,
      capacity: "85%",
      eta: "2:30 PM",
      status: "on-time",
      stops: 12,
      distance: "45 km"
    },
    {
      route: "Route B - North Zone", 
      vehicles: 6,
      capacity: "92%",
      eta: "3:15 PM",
      status: "delayed",
      stops: 9,
      distance: "38 km"
    },
    {
      route: "Route C - South Zone",
      vehicles: 7,
      capacity: "78%", 
      eta: "2:45 PM",
      status: "on-time",
      stops: 15,
      distance: "52 km"
    },
    {
      route: "Route D - East Zone",
      vehicles: 5,
      capacity: "88%",
      eta: "3:00 PM", 
      status: "early",
      stops: 8,
      distance: "35 km"
    }
  ];

  const inventoryOptimization = [
    { location: "Cold Storage A", utilization: 87, capacity: "2400L", recommended: "Increase stock" },
    { location: "Cold Storage B", utilization: 45, capacity: "1800L", recommended: "Redistribute" },
    { location: "Cold Storage C", utilization: 92, capacity: "3200L", recommended: "Near capacity" },
    { location: "Distribution Hub 1", utilization: 73, capacity: "1600L", recommended: "Optimal" },
    { location: "Distribution Hub 2", utilization: 59, capacity: "2000L", recommended: "Can accept more" }
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      default: return <Target className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-time': case 'early': return 'bg-green-100 text-green-800 border-green-200';
      case 'delayed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return 'text-red-600';
    if (utilization >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  const runOptimization = () => {
    setIsOptimizing(true);
    setTimeout(() => {
      setIsOptimizing(false);
      setLastOptimized("Just now");
    }, 3000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-sky-200 bg-gradient-to-r from-sky-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Truck className="h-6 w-6 text-sky-600" />
              <span>AI-Powered Dispatch & Stock Optimization</span>
            </div>
            <Button 
              onClick={runOptimization}
              disabled={isOptimizing}
              className="bg-sky-600 hover:bg-sky-700"
            >
              {isOptimizing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <Target className="h-4 w-4 mr-2" />
                  Run Optimization
                </>
              )}
            </Button>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Demand forecasting and intelligent dispatch planning | Last optimized: {lastOptimized}
          </p>
        </CardHeader>
      </Card>

      {/* Demand Forecast */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-sky-600" />
            <span>Today's Demand Forecast vs Actual</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {demandForecast.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <Package className="h-5 w-5 text-sky-600" />
                  <div>
                    <h4 className="font-medium text-gray-900">{item.product}</h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>Predicted: {item.predicted.toLocaleString()}</span>
                      <span>Actual: {item.actual.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Accuracy</p>
                    <p className="font-medium text-sky-700">{item.accuracy}%</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Trend</p>
                    {getTrendIcon(item.trend)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Dispatch Routes */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5 text-sky-600" />
            <span>Optimized Dispatch Routes</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {dispatchRoutes.map((route, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">{route.route}</h4>
                  <Badge className={getStatusColor(route.status)}>
                    {route.status}
                  </Badge>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Vehicles</p>
                    <p className="font-medium">{route.vehicles}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Capacity</p>
                    <p className="font-medium">{route.capacity}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">ETA</p>
                    <p className="font-medium flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {route.eta}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500">Distance</p>
                    <p className="font-medium">{route.distance}</p>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{route.stops} stops</span>
                  <Button size="sm" variant="outline">
                    <MapPin className="h-4 w-4 mr-1" />
                    View Route
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Inventory Optimization */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-sky-600" />
            <span>Cold Storage Optimization</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {inventoryOptimization.map((storage, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{storage.location}</h4>
                    <span className={`font-medium ${getUtilizationColor(storage.utilization)}`}>
                      {storage.utilization}% utilized
                    </span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <Progress value={storage.utilization} className="flex-1 max-w-48" />
                    <span className="text-sm text-gray-600">{storage.capacity}</span>
                  </div>
                </div>
                <div className="ml-4">
                  <Badge variant="outline" className="text-xs">
                    {storage.recommended}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Benefits Summary */}
      <Card className="border-sky-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-6 w-6 text-green-600" />
            <span>Dispatch Optimization Benefits</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">20%</div>
              <p className="text-sm text-gray-700">Reduction in shelf expiry</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">15%</div>
              <p className="text-sm text-gray-700">Better demand-supply match</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">25%</div>
              <p className="text-sm text-gray-700">Optimized cold storage use</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DispatchOptimization;

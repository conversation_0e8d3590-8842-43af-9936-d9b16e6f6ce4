import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { 
  Activity, 
  Heart, 
  Wheat, 
  Milk, 
  AlertTriangle, 
  TrendingUp,
  TrendingDown,
  Filter,
  Search,
  Calendar
} from "lucide-react";

// Mock data for animal health analytics
const mockAnimals = [
  {
    id: "COW-001",
    type: "cow",
    region: "Kolhapur",
    sanstha: "Kolhapur DCA Central",
    healthScore: 92,
    feedRequirement: 25.5,
    milkProduction: 18.2,
    healthStatus: "excellent",
    riskLevel: "low",
    lastCheckup: "2024-01-30",
    predictions: {
      healthTrend: "improving",
      milkTrend: "stable",
      feedOptimization: 15
    }
  },
  {
    id: "BUF-002", 
    type: "buffalo",
    region: "Sangli",
    sanstha: "Sangli DCA East",
    healthScore: 78,
    feedRequirement: 32.0,
    milkProduction: 22.5,
    healthStatus: "good",
    riskLevel: "medium",
    lastCheckup: "2024-01-29",
    predictions: {
      healthTrend: "stable",
      milkTrend: "increasing",
      feedOptimization: 8
    }
  },
  {
    id: "COW-003",
    type: "cow", 
    region: "Kolhapur",
    sanstha: "Kolhapur DCA South",
    healthScore: 65,
    feedRequirement: 28.0,
    milkProduction: 14.8,
    healthStatus: "fair",
    riskLevel: "high",
    lastCheckup: "2024-01-28",
    predictions: {
      healthTrend: "declining",
      milkTrend: "decreasing",
      feedOptimization: 22
    }
  },
  {
    id: "GOT-004",
    type: "goat",
    region: "Satara", 
    sanstha: "Satara DCA North",
    healthScore: 88,
    feedRequirement: 8.5,
    milkProduction: 3.2,
    healthStatus: "excellent",
    riskLevel: "low",
    lastCheckup: "2024-01-30",
    predictions: {
      healthTrend: "stable",
      milkTrend: "stable",
      feedOptimization: 5
    }
  }
];

const HealthAnalytics = () => {
  const [selectedRegion, setSelectedRegion] = useState<string>("all");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [selectedRisk, setSelectedRisk] = useState<string>("all");

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600 bg-green-50";
    if (score >= 75) return "text-blue-600 bg-blue-50";
    if (score >= 60) return "text-yellow-600 bg-yellow-50";
    return "text-red-600 bg-red-50";
  };

  const getRiskBadgeColor = (risk: string) => {
    switch (risk) {
      case "low": return "bg-green-100 text-green-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "high": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "improving":
      case "increasing":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "declining":
      case "decreasing":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-blue-600" />;
    }
  };

  const filteredAnimals = mockAnimals.filter(animal => {
    return (selectedRegion === "all" || animal.region === selectedRegion) &&
           (selectedType === "all" || animal.type === selectedType) &&
           (selectedRisk === "all" || animal.riskLevel === selectedRisk);
  });

  const calculateAverages = () => {
    const total = filteredAnimals.length;
    if (total === 0) return { avgHealth: 0, avgFeed: 0, avgMilk: 0 };

    const avgHealth = filteredAnimals.reduce((sum, animal) => sum + animal.healthScore, 0) / total;
    const avgFeed = filteredAnimals.reduce((sum, animal) => sum + animal.feedRequirement, 0) / total;
    const avgMilk = filteredAnimals.reduce((sum, animal) => sum + animal.milkProduction, 0) / total;

    return { avgHealth, avgFeed, avgMilk };
  };

  const averages = calculateAverages();

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Health Analytics per Animal</h2>
          <p className="text-gray-600">Daily health predictions, feed requirements, and milk production analysis</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={selectedRegion} onValueChange={setSelectedRegion}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Regions</SelectItem>
              <SelectItem value="Kolhapur">Kolhapur</SelectItem>
              <SelectItem value="Sangli">Sangli</SelectItem>
              <SelectItem value="Satara">Satara</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="cow">Cows</SelectItem>
              <SelectItem value="buffalo">Buffalos</SelectItem>
              <SelectItem value="goat">Goats</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedRisk} onValueChange={setSelectedRisk}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Risk" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Risk</SelectItem>
              <SelectItem value="low">Low Risk</SelectItem>
              <SelectItem value="medium">Medium Risk</SelectItem>
              <SelectItem value="high">High Risk</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Average Health Score</p>
                <p className="text-3xl font-bold text-green-900">{averages.avgHealth.toFixed(1)}%</p>
              </div>
              <div className="bg-green-200 p-3 rounded-full">
                <Heart className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Average Feed (kg/day)</p>
                <p className="text-3xl font-bold text-blue-900">{averages.avgFeed.toFixed(1)}</p>
              </div>
              <div className="bg-blue-200 p-3 rounded-full">
                <Wheat className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Average Milk (L/day)</p>
                <p className="text-3xl font-bold text-purple-900">{averages.avgMilk.toFixed(1)}</p>
              </div>
              <div className="bg-purple-200 p-3 rounded-full">
                <Milk className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Animal Health Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredAnimals.map(animal => (
          <Card key={animal.id} className="shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <div className="text-2xl">
                    {animal.type === "cow" ? "🐄" : animal.type === "buffalo" ? "🐃" : "🐐"}
                  </div>
                  <div>
                    <div className="font-bold">{animal.id}</div>
                    <div className="text-sm text-gray-500 font-normal">{animal.sanstha}</div>
                  </div>
                </CardTitle>
                <Badge className={getRiskBadgeColor(animal.riskLevel)}>
                  {animal.riskLevel} risk
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Health Score */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Health Score</span>
                  <span className={`text-sm font-bold px-2 py-1 rounded ${getHealthScoreColor(animal.healthScore)}`}>
                    {animal.healthScore}%
                  </span>
                </div>
                <Progress value={animal.healthScore} className="h-2" />
              </div>

              {/* Metrics Grid */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <Wheat className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Feed Required</span>
                  </div>
                  <div className="text-lg font-bold text-blue-900">{animal.feedRequirement} kg/day</div>
                </div>

                <div className="bg-purple-50 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <Milk className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium text-purple-900">Milk Production</span>
                  </div>
                  <div className="text-lg font-bold text-purple-900">{animal.milkProduction} L/day</div>
                </div>
              </div>

              {/* Predictions */}
              <div className="bg-gray-50 p-3 rounded-lg">
                <h4 className="text-sm font-semibold text-gray-900 mb-2">AI Predictions</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Health Trend</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(animal.predictions.healthTrend)}
                      <span className="font-medium">{animal.predictions.healthTrend}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Milk Trend</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(animal.predictions.milkTrend)}
                      <span className="font-medium">{animal.predictions.milkTrend}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Feed Optimization</span>
                    <span className="font-medium text-green-600">-{animal.predictions.feedOptimization}%</span>
                  </div>
                </div>
              </div>

              {/* Risk Alerts */}
              {animal.riskLevel === "high" && (
                <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <span className="text-sm font-medium text-red-900">Health Alert</span>
                  </div>
                  <p className="text-sm text-red-700 mt-1">
                    Requires immediate veterinary attention. Schedule checkup within 24 hours.
                  </p>
                </div>
              )}

              <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
                <span>Last checkup: {animal.lastCheckup}</span>
                <span>{animal.region}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAnimals.length === 0 && (
        <Card className="p-8 text-center">
          <div className="text-gray-500">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No animals found matching the selected filters.</p>
          </div>
        </Card>
      )}
    </div>
  );
};

export default HealthAnalytics;


import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Truck, 
  MapPin, 
  Clock,
  TrendingUp,
  Snowflake,
  Route,
  Package,
  Brain,
  Thermometer
} from "lucide-react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const DispatchDepartment = () => {
  const [selectedView, setSelectedView] = useState("dispatch");

  const dispatchData = [
    {
      vehicle: "MH-09-AB-1234",
      route: "Kini - Wathar - Peth Vadgaon",
      status: "in-transit",
      driver: "<PERSON><PERSON>",
      products: "Milk: 500L, Curd: 200kg",
      departure: "06:30 AM",
      expectedReturn: "11:30 AM",
      fuelEfficiency: 12.5,
      aiOptimization: "Route optimized - 18% fuel saving"
    },
    {
      vehicle: "MH-09-CD-5678", 
      route: "Shiroli - Hamidwada - Gajra",
      status: "loading",
      driver: "Suresh Jadhav",
      products: "Ghee: 100kg, Butter: 150kg",
      departure: "07:00 AM",
      expectedReturn: "12:00 PM",
      fuelEfficiency: 11.8,
      aiOptimization: "Loading sequence optimized"
    },
    {
      vehicle: "MH-09-EF-9012",
      route: "Gaganbawada - Radhanagari",
      status: "completed",
      driver: "Mahesh Koli",
      products: "Mixed products: 800kg",
      departure: "05:00 AM",
      expectedReturn: "10:00 AM",
      actualReturn: "09:45 AM",
      fuelEfficiency: 13.2,
      aiOptimization: "15 min early arrival"
    }
  ];

  const coldStorageData = [
    {
      unit: "Cold Storage A",
      temperature: 4.2,
      targetTemp: 4.0,
      humidity: 85,
      targetHumidity: 80,
      capacity: 5000,
      current: 4200,
      products: "Milk, Curd, Paneer",
      efficiency: 94.5,
      energySaving: "12% saved via AI scheduling"
    },
    {
      unit: "Cold Storage B",
      temperature: 6.1,
      targetTemp: 4.0,
      humidity: 88,
      targetHumidity: 80,
      capacity: 3000,  
      current: 2800,
      products: "Butter, Ghee",
      efficiency: 87.2,
      energySaving: "Alert: Temperature high"
    },
    {
      unit: "Freezer Unit",
      temperature: -18.5,
      targetTemp: -18.0,
      humidity: 65,
      targetHumidity: 65,
      capacity: 2000,
      current: 1500,
      products: "Frozen products",
      efficiency: 96.8,
      energySaving: "Optimal performance"
    }
  ];

  const demandForecast = [
    {
      product: "Milk",
      current: 15800,
      forecasted: 18200,
      change: "+15.2%",
      reason: "Festival season approaching",
      recommendation: "Increase production by 20%"
    },
    {
      product: "Ghee", 
      current: 850,
      forecasted: 1200,
      change: "+41.2%",
      reason: "Diwali demand spike",
      recommendation: "Prepare additional packaging"
    },
    {
      product: "Curd",
      current: 3200,
      forecasted: 3400,
      change: "+6.3%",
      reason: "Seasonal increase",
      recommendation: "Maintain current levels"
    },
    {
      product: "Paneer",
      current: 1200,
      forecasted: 1100,
      change: "-8.3%",
      reason: "Market saturation",
      recommendation: "Optimize inventory"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in-transit': return 'bg-blue-100 text-blue-800';
      case 'loading': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'delayed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTempColor = (temp: number, target: number) => {
    const diff = Math.abs(temp - target);
    if (diff <= 0.5) return 'text-green-600';
    if (diff <= 1.0) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Chart Data
  const deliveryPerformanceData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'On-Time Deliveries (%)',
        data: [94, 96, 92, 95, 97, 89, 91],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Target (%)',
        data: [95, 95, 95, 95, 95, 95, 95],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderDash: [5, 5],
        tension: 0.4,
      }
    ],
  };

  const routeEfficiencyData = {
    labels: ['Route A', 'Route B', 'Route C', 'Route D', 'Route E'],
    datasets: [
      {
        label: 'Fuel Efficiency (km/L)',
        data: [12.5, 11.8, 13.2, 10.9, 12.1],
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
      },
      {
        label: 'AI Optimized (km/L)',
        data: [14.8, 13.9, 15.1, 12.8, 14.3],
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 2,
      },
    ],
  };

  const coldChainTempData = {
    labels: ['6 AM', '9 AM', '12 PM', '3 PM', '6 PM', '9 PM', '12 AM', '3 AM'],
    datasets: [
      {
        label: 'Cold Storage 1 (°C)',
        data: [4.2, 4.1, 4.3, 4.0, 4.2, 4.1, 4.0, 4.2],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Cold Storage 2 (°C)',
        data: [3.8, 3.9, 4.0, 3.7, 3.9, 3.8, 3.9, 4.0],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Target Range (°C)',
        data: [4, 4, 4, 4, 4, 4, 4, 4],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderDash: [5, 5],
        tension: 0.4,
      }
    ],
  };

  const vehicleStatusData = {
    labels: ['In Transit', 'Loading', 'Completed', 'Delayed', 'Maintenance'],
    datasets: [
      {
        data: [35, 20, 30, 10, 5],
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(156, 163, 175, 0.8)',
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(245, 158, 11)',
          'rgb(34, 197, 94)',
          'rgb(239, 68, 68)',
          'rgb(156, 163, 175)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: false,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header with View Selection */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Dispatch & Cold Chain</h2>
          <p className="text-sm text-gray-600">AI-optimized logistics and cold storage management</p>
        </div>
        <div className="flex items-center space-x-4">
          <select 
            value={selectedView}
            onChange={(e) => setSelectedView(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="dispatch">Dispatch Operations</option>
            <option value="cold-storage">Cold Storage</option>
            <option value="forecast">Demand Forecast</option>
          </select>
        </div>
      </div>

      {selectedView === "dispatch" && (
        <>
          {/* AI Optimization Insights */}
          <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-purple-600" />
                <span>AI Dispatch & Cold Chain Insights</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-lg border border-purple-100">
                  <div className="flex items-start space-x-3">
                    <Route className="h-5 w-5 text-green-500 mt-1" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2">Route Optimization Success</h4>
                      <p className="text-sm text-gray-700 mb-3">AI reduced fuel consumption by 18% through dynamic route planning. Average delivery time improved by 15 minutes.</p>
                      <Badge className="bg-green-100 text-green-700">High Impact</Badge>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-4 rounded-lg border border-purple-100">
                  <div className="flex items-start space-x-3">
                    <Thermometer className="h-5 w-5 text-blue-500 mt-1" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2">Cold Chain Prediction</h4>
                      <p className="text-sm text-gray-700 mb-3">Predictive maintenance identified potential compressor issues 48 hours in advance, preventing quality loss.</p>
                      <Badge className="bg-blue-100 text-blue-700">Preventive</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Dispatch Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600 mb-1">8</div>
                <p className="text-sm text-gray-600">Active Vehicles</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600 mb-1">12.3</div>
                <p className="text-sm text-gray-600">Avg Fuel Efficiency</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600 mb-1">18%</div>
                <p className="text-sm text-gray-600">Fuel Savings (AI)</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600 mb-1">96.5%</div>
                <p className="text-sm text-gray-600">On-time Delivery</p>
              </CardContent>
            </Card>
          </div>

          {/* Vehicle Tracking */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Truck className="h-5 w-5 text-blue-600" />
                <span>Real-time Vehicle Tracking</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dispatchData.map((vehicle, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="bg-blue-100 p-3 rounded-lg">
                        <Truck className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{vehicle.vehicle}</h4>
                        <p className="text-sm text-gray-600">Driver: {vehicle.driver}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <MapPin className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-700">{vehicle.route}</span>
                        </div>
                      </div>
                    </div>

                    <div className="text-center">
                      <Badge className={getStatusColor(vehicle.status)}>
                        {vehicle.status.replace('-', ' ').toUpperCase()}
                      </Badge>
                      <p className="text-sm text-gray-600 mt-1">{vehicle.products}</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                        <span>Departure: {vehicle.departure}</span>
                        <span>Return: {vehicle.expectedReturn}</span>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-sm text-gray-600">Fuel Efficiency</div>
                      <div className="font-semibold text-green-600">{vehicle.fuelEfficiency} km/L</div>
                      <div className="text-xs text-blue-600 mt-1">{vehicle.aiOptimization}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {selectedView === "cold-storage" && (
        <>
          {/* Cold Storage Monitoring */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Snowflake className="h-5 w-5 text-cyan-600" />
                <span>Cold Storage Monitoring</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {coldStorageData.map((storage, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="bg-cyan-100 p-2 rounded-lg">
                          <Snowflake className="h-5 w-5 text-cyan-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{storage.unit}</h3>
                          <p className="text-sm text-gray-600">{storage.products}</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3 mb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Thermometer className="h-4 w-4 text-blue-500" />
                          <span className="text-sm text-gray-600">Temperature</span>
                        </div>
                        <span className={`font-medium ${getTempColor(storage.temperature, storage.targetTemp)}`}>
                          {storage.temperature}°C / {storage.targetTemp}°C
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Humidity</span>
                        <span className="font-medium text-gray-900">
                          {storage.humidity}% / {storage.targetHumidity}%
                        </span>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-600">Capacity</span>
                          <span>{storage.current}kg / {storage.capacity}kg</span>
                        </div>
                        <Progress value={(storage.current / storage.capacity) * 100} className="h-2" />
                      </div>
                    </div>

                    <div className="bg-purple-50 p-3 rounded-lg">
                      <div className="flex items-center space-x-2 mb-1">
                        <Brain className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-purple-900">AI Optimization</span>
                      </div>
                      <p className="text-sm text-purple-800">{storage.energySaving}</p>
                      <p className="text-xs text-purple-600 mt-1">Efficiency: {storage.efficiency}%</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {selectedView === "forecast" && (
        <>
          {/* SKU-wise Demand Forecasting */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5 text-green-600" />
                <span>SKU-wise Demand Forecasting</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {demandForecast.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="bg-green-100 p-3 rounded-lg">
                        <Package className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{item.product}</h4>
                        <p className="text-sm text-gray-600">{item.reason}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-6 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Current</p>
                        <p className="font-semibold text-gray-900">{item.current.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Forecasted</p>
                        <p className="font-semibold text-blue-600">{item.forecasted.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Change</p>
                        <p className={`font-semibold ${
                          item.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {item.change}
                        </p>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="bg-blue-50 p-2 rounded">
                        <p className="text-sm font-medium text-blue-900">AI Recommendation</p>
                        <p className="text-xs text-blue-800">{item.recommendation}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Dispatch & Cold Chain Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Delivery Performance Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <span>Weekly Delivery Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={deliveryPerformanceData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Route Efficiency Comparison */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Route className="h-5 w-5 text-blue-600" />
              <span>Route Efficiency: Before vs AI Optimized</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Bar data={routeEfficiencyData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cold Chain & Vehicle Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cold Chain Temperature Monitoring */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Thermometer className="h-5 w-5 text-blue-600" />
              <span>Cold Chain Temperature Monitoring</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={coldChainTempData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Truck className="h-5 w-5 text-purple-600" />
              <span>Vehicle Status Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Doughnut data={vehicleStatusData} options={pieChartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DispatchDepartment;

/* Leaflet CSS - Required for proper map rendering */
@import 'leaflet/dist/leaflet.css';

/* Custom marker styles */
.custom-marker {
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
}

.custom-marker:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.custom-marker.excellent {
  background: linear-gradient(135deg, #10b981, #059669);
}

.custom-marker.good {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.custom-marker.needs_attention {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.custom-marker.critical {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.custom-marker.gokul-unit {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  width: 32px !important;
  height: 32px !important;
  border: 4px solid white;
}

/* Custom popup styles */
.leaflet-popup-content-wrapper {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-content {
  margin: 16px;
  font-family: system-ui, -apple-system, sans-serif;
}

.leaflet-popup-tip {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Map container styles */
.leaflet-container {
  border-radius: 8px;
  font-family: system-ui, -apple-system, sans-serif;
}

/* Control styles */
.leaflet-control-zoom {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.leaflet-control-zoom a {
  border-radius: 6px;
  font-size: 18px;
  font-weight: bold;
}

/* Attribution styles */
.leaflet-control-attribution {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  font-size: 11px;
}

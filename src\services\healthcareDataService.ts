// Healthcare Data Service - Mock data and API integration points
export interface Animal {
  id: string;
  type: 'cow' | 'buffalo' | 'goat';
  region: string;
  sanstha: string;
  healthScore: number;
  feedRequirement: number;
  milkProduction: number;
  healthStatus: 'excellent' | 'good' | 'fair' | 'poor';
  riskLevel: 'low' | 'medium' | 'high';
  lastCheckup: string;
  predictions: {
    healthTrend: 'improving' | 'stable' | 'declining';
    milkTrend: 'increasing' | 'stable' | 'decreasing';
    feedOptimization: number;
  };
}

export interface Region {
  id: string;
  name: string;
  sansthas: Sanstha[];
}

export interface Sanstha {
  id: string;
  name: string;
  animals: {
    cows: number;
    buffalos: number;
    goats: number;
  };
  status: 'active' | 'maintenance' | 'inactive';
}

export interface VeterinaryDoctor {
  id: string;
  name: string;
  phone: string;
  region: string;
  specialization: string;
  experience: number;
  availability: {
    status: 'available' | 'busy' | 'unavailable';
    days: string[];
    hours: string;
  };
  consultationCapacity: number;
  todayConsultations: number;
  rating: number;
  lastActive: string;
  outageReports: Array<{
    date: string;
    reason: string;
    duration: string;
  }>;
}

export interface MilkProductionData {
  regions: Array<{
    id: string;
    name: string;
    dailyVolume: number;
    targetVolume: number;
    contributingAnimals: number;
    totalAnimals: number;
    avgHealthScore: number;
    feedEfficiency: number;
    qualityGrade: string;
    trend: 'increasing' | 'stable' | 'decreasing';
  }>;
  animalTypes: Array<{
    type: string;
    emoji: string;
    totalAnimals: number;
    contributingAnimals: number;
    avgDailyMilk: number;
    avgFeedConsumption: number;
    efficiency: number;
  }>;
}

export interface AIInsights {
  predictions: Array<{
    id: string;
    title: string;
    category: string;
    confidence: number;
    timeframe: string;
    insights: Array<{
      region: string;
      prediction: string;
      confidence: number;
      factors: string[];
    }>;
  }>;
  optimizations: Array<{
    id: string;
    title: string;
    category: string;
    potentialSavings: string;
    recommendations: Array<{
      region: string;
      suggestion: string;
      impact: string;
      implementation: string;
    }>;
  }>;
  riskMonitoring: Array<{
    id: string;
    title: string;
    level: string;
    confidence: number;
    regions: Array<{
      name: string;
      risk: string;
      score: number;
    }>;
  }>;
}

class HealthcareDataService {
  private baseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api';

  // Mock data - replace with actual API calls
  async getRegionsAndSansthas(): Promise<Region[]> {
    // Simulate API call
    await this.delay(500);
    
    return [
      {
        id: "kolhapur",
        name: "Kolhapur",
        sansthas: [
          {
            id: "dca-kol-1",
            name: "Kolhapur DCA Central",
            animals: { cows: 450, buffalos: 320, goats: 180 },
            status: "active"
          },
          {
            id: "dca-kol-2", 
            name: "Kolhapur DCA South",
            animals: { cows: 380, buffalos: 290, goats: 150 },
            status: "active"
          }
        ]
      },
      {
        id: "sangli",
        name: "Sangli",
        sansthas: [
          {
            id: "dca-san-1",
            name: "Sangli DCA East",
            animals: { cows: 520, buffalos: 410, goats: 220 },
            status: "active"
          },
          {
            id: "dca-san-2",
            name: "Sangli DCA West", 
            animals: { cows: 340, buffalos: 280, goats: 160 },
            status: "maintenance"
          }
        ]
      },
      {
        id: "satara",
        name: "Satara",
        sansthas: [
          {
            id: "dca-sat-1",
            name: "Satara DCA North",
            animals: { cows: 420, buffalos: 350, goats: 190 },
            status: "active"
          }
        ]
      }
    ];
  }

  async getAnimalsHealthData(): Promise<Animal[]> {
    await this.delay(300);
    
    return [
      {
        id: "COW-001",
        type: "cow",
        region: "Kolhapur",
        sanstha: "Kolhapur DCA Central",
        healthScore: 92,
        feedRequirement: 25.5,
        milkProduction: 18.2,
        healthStatus: "excellent",
        riskLevel: "low",
        lastCheckup: "2024-01-30",
        predictions: {
          healthTrend: "improving",
          milkTrend: "stable",
          feedOptimization: 15
        }
      },
      {
        id: "BUF-002", 
        type: "buffalo",
        region: "Sangli",
        sanstha: "Sangli DCA East",
        healthScore: 78,
        feedRequirement: 32.0,
        milkProduction: 22.5,
        healthStatus: "good",
        riskLevel: "medium",
        lastCheckup: "2024-01-29",
        predictions: {
          healthTrend: "stable",
          milkTrend: "increasing",
          feedOptimization: 8
        }
      },
      {
        id: "COW-003",
        type: "cow", 
        region: "Kolhapur",
        sanstha: "Kolhapur DCA South",
        healthScore: 65,
        feedRequirement: 28.0,
        milkProduction: 14.8,
        healthStatus: "fair",
        riskLevel: "high",
        lastCheckup: "2024-01-28",
        predictions: {
          healthTrend: "declining",
          milkTrend: "decreasing",
          feedOptimization: 22
        }
      },
      {
        id: "GOT-004",
        type: "goat",
        region: "Satara", 
        sanstha: "Satara DCA North",
        healthScore: 88,
        feedRequirement: 8.5,
        milkProduction: 3.2,
        healthStatus: "excellent",
        riskLevel: "low",
        lastCheckup: "2024-01-30",
        predictions: {
          healthTrend: "stable",
          milkTrend: "stable",
          feedOptimization: 5
        }
      }
    ];
  }

  async getVeterinaryDoctors(): Promise<VeterinaryDoctor[]> {
    await this.delay(400);
    
    return [
      {
        id: "VET-001",
        name: "Dr. Rajesh Patil",
        phone: "+91 98765 43210",
        region: "Kolhapur",
        specialization: "Dairy Cattle Medicine",
        experience: 12,
        availability: {
          status: "available",
          days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
          hours: "9:00 AM - 6:00 PM"
        },
        consultationCapacity: 25,
        todayConsultations: 18,
        rating: 4.8,
        lastActive: "2024-01-30 14:30",
        outageReports: []
      },
      {
        id: "VET-002", 
        name: "Dr. Priya Sharma",
        phone: "+91 87654 32109",
        region: "Sangli",
        specialization: "Buffalo Health & Reproduction",
        experience: 8,
        availability: {
          status: "busy",
          days: ["Monday", "Wednesday", "Friday", "Saturday"],
          hours: "8:00 AM - 5:00 PM"
        },
        consultationCapacity: 20,
        todayConsultations: 20,
        rating: 4.9,
        lastActive: "2024-01-30 15:45",
        outageReports: []
      }
    ];
  }

  async getMilkProductionData(): Promise<MilkProductionData> {
    await this.delay(350);
    
    return {
      regions: [
        {
          id: "kolhapur",
          name: "Kolhapur",
          dailyVolume: 6800,
          targetVolume: 7000,
          contributingAnimals: 830,
          totalAnimals: 950,
          avgHealthScore: 87,
          feedEfficiency: 0.68,
          qualityGrade: "A+",
          trend: "increasing"
        },
        {
          id: "sangli", 
          name: "Sangli",
          dailyVolume: 5200,
          targetVolume: 5500,
          contributingAnimals: 640,
          totalAnimals: 750,
          avgHealthScore: 82,
          feedEfficiency: 0.65,
          qualityGrade: "A",
          trend: "stable"
        }
      ],
      animalTypes: [
        {
          type: "cow",
          emoji: "🐄",
          totalAnimals: 1280,
          contributingAnimals: 1150,
          avgDailyMilk: 16.5,
          avgFeedConsumption: 24.2,
          efficiency: 0.68
        },
        {
          type: "buffalo",
          emoji: "🐃", 
          totalAnimals: 890,
          contributingAnimals: 820,
          avgDailyMilk: 22.8,
          avgFeedConsumption: 32.5,
          efficiency: 0.70
        }
      ]
    };
  }

  async getAIInsights(): Promise<AIInsights> {
    await this.delay(600);
    
    return {
      predictions: [
        {
          id: "growth-potential",
          title: "Animal Growth Potential",
          category: "Growth Analysis",
          confidence: 94,
          timeframe: "Next 3 months",
          insights: [
            {
              region: "Kolhapur",
              prediction: "15% increase in milk production expected",
              confidence: 92,
              factors: ["Improved feed quality", "Better health management", "Seasonal advantages"]
            }
          ]
        }
      ],
      optimizations: [
        {
          id: "feed-optimization",
          title: "Feed Optimization Recommendations",
          category: "Cost Reduction",
          potentialSavings: "₹2.3L per month",
          recommendations: [
            {
              region: "All Regions",
              suggestion: "Switch to high-protein concentrate mix",
              impact: "12% reduction in feed costs, 8% increase in milk yield",
              implementation: "Gradual transition over 2 weeks"
            }
          ]
        }
      ],
      riskMonitoring: [
        {
          id: "contamination-risk",
          title: "Milk Contamination Risk",
          level: "Low",
          confidence: 92,
          regions: [
            { name: "Kolhapur", risk: "Very Low", score: 95 },
            { name: "Sangli", risk: "Low", score: 88 }
          ]
        }
      ]
    };
  }

  // Utility method to simulate API delay
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Future API integration methods
  async createAnimal(animal: Partial<Animal>): Promise<Animal> {
    // TODO: Implement actual API call
    throw new Error("Not implemented");
  }

  async updateAnimal(id: string, updates: Partial<Animal>): Promise<Animal> {
    // TODO: Implement actual API call
    throw new Error("Not implemented");
  }

  async scheduleVeterinaryVisit(doctorId: string, animalId: string, date: string): Promise<void> {
    // TODO: Implement actual API call
    throw new Error("Not implemented");
  }
}

export const healthcareDataService = new HealthcareDataService();


import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface DateRangeFilterProps {
  value: string;
  onChange: (value: string) => void;
}

const DateRangeFilter = ({ value, onChange }: DateRangeFilterProps) => {
  const [currentData, setCurrentData] = useState<any>({});

  const filterOptions = [
    { value: "today", label: "Today", active: true },
    { value: "yesterday", label: "Yesterday", active: true },
    { value: "week", label: "This Week", active: true },
    { value: "month", label: "This Month", active: true },
    { value: "quarter", label: "This Quarter", active: false },
    { value: "year", label: "This Year", active: true }
  ];

  // Simulate real-time data changes based on filter
  useEffect(() => {
    const updateData = () => {
      const baseData = {
        today: { production: 16.2, quality: 96.8, energy: 87.2, dispatch: 94.5 },
        yesterday: { production: 15.8, quality: 96.3, energy: 88.1, dispatch: 93.2 },
        week: { production: 112.4, quality: 96.5, energy: 87.8, dispatch: 94.1 },
        month: { production: 486.2, quality: 96.7, energy: 87.5, dispatch: 94.3 },
        quarter: { production: 1458.6, quality: 96.4, energy: 87.9, dispatch: 93.8 },
        year: { production: 5834.4, quality: 96.6, energy: 87.6, dispatch: 94.0 }
      };
      
      setCurrentData(baseData[value as keyof typeof baseData] || baseData.today);
    };

    updateData();
  }, [value]);

  const getFilterLabel = (filterValue: string) => {
    const option = filterOptions.find(opt => opt.value === filterValue);
    return option?.label || "Today";
  };

  const handleFilterChange = (newValue: string) => {
    onChange(newValue);
    
    // Trigger data refresh for components
    const event = new CustomEvent('dateFilterChange', { 
      detail: { filter: newValue, data: currentData } 
    });
    window.dispatchEvent(event);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center space-x-2 bg-white border-blue-200 hover:bg-blue-50">
          <Calendar className="h-4 w-4 text-blue-600" />
          <span className="text-blue-700 font-medium">{getFilterLabel(value)}</span>
          <ChevronDown className="h-4 w-4 text-blue-600" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {filterOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleFilterChange(option.value)}
            className={`flex items-center justify-between cursor-pointer ${
              value === option.value ? 'bg-blue-50 text-blue-700' : ''
            }`}
          >
            <span>{option.label}</span>
            {option.active && (
              <Badge className="bg-green-100 text-green-700 text-xs">Live</Badge>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DateRangeFilter;

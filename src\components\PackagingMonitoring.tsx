import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Package, 
  Camera, 
  AlertTriangle, 
  CheckCircle,
  X,
  Eye,
  Zap,
  BarChart3,
  Settings
} from "lucide-react";

const PackagingMonitoring = () => {
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [totalPackages, setTotalPackages] = useState(12847);
  const [defectsDetected, setDefectsDetected] = useState(23);

  // Simulate real-time monitoring
  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(() => {
        setTotalPackages(prev => prev + Math.floor(Math.random() * 3) + 1);
        if (Math.random() > 0.95) {
          setDefectsDetected(prev => prev + 1);
        }
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [isMonitoring]);

  const packagingLines = [
    {
      line: "Line A - Milk Packets",
      status: "running",
      speed: "450 ppm",
      defectRate: 0.12,
      lastDefect: "2 min ago",
      camera: "active"
    },
    {
      line: "Line B - Curd Containers",
      status: "running", 
      speed: "320 ppm",
      defectRate: 0.08,
      lastDefect: "15 min ago",
      camera: "active"
    },
    {
      line: "Line C - Ghee Containers",
      status: "warning",
      speed: "180 ppm",
      defectRate: 0.25,
      lastDefect: "1 min ago",
      camera: "active"
    },
    {
      line: "Line D - Butter Packets",
      status: "stopped",
      speed: "0 ppm",
      defectRate: 0.00,
      lastDefect: "30 min ago",
      camera: "offline"
    }
  ];

  const recentDetections = [
    {
      time: "10:45:23",
      line: "Line A",
      issue: "Missing date code",
      confidence: 96,
      action: "Alert sent to operator",
      image: "defect_001.jpg"
    },
    {
      time: "10:42:17",
      line: "Line C", 
      issue: "Improper seal",
      confidence: 89,
      action: "Quality team notified",
      image: "defect_002.jpg"
    },
    {
      time: "10:38:45",
      line: "Line A",
      issue: "Label misalignment", 
      confidence: 93,
      action: "Operator alerted",
      image: "defect_003.jpg"
    },
    {
      time: "10:35:12",
      line: "Line B",
      issue: "Package damage",
      confidence: 98,
      action: "Line inspection scheduled",
      image: "defect_004.jpg"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'stopped': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <CheckCircle className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'stopped': return <X className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const defectRate = ((defectsDetected / totalPackages) * 100).toFixed(3);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-sky-200 bg-gradient-to-r from-sky-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-6 w-6 text-sky-600" />
            <span>AI-Powered Packaging Line Monitoring</span>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Real-time defect detection using YOLOv8 computer vision
          </p>
        </CardHeader>
      </Card>

      {/* Live Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-sky-200">
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-sky-700 mb-1">
              {totalPackages.toLocaleString()}
            </div>
            <p className="text-sm text-gray-600">Packages Processed Today</p>
          </CardContent>
        </Card>
        
        <Card className="border-sky-200">
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-red-600 mb-1">
              {defectsDetected}
            </div>
            <p className="text-sm text-gray-600">Defects Detected</p>
          </CardContent>
        </Card>

        <Card className="border-sky-200">
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">
              {defectRate}%
            </div>
            <p className="text-sm text-gray-600">Defect Rate</p>
          </CardContent>
        </Card>

        <Card className="border-sky-200">
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-1">
              {((1 - defectsDetected / totalPackages) * 100).toFixed(2)}%
            </div>
            <p className="text-sm text-gray-600">Quality Rate</p>
          </CardContent>
        </Card>
      </div>

      {/* Packaging Lines Status */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Eye className="h-5 w-5 text-sky-600" />
              <span>Packaging Lines Status</span>
            </div>
            <Button 
              onClick={() => setIsMonitoring(!isMonitoring)}
              variant={isMonitoring ? "destructive" : "default"}
              size="sm"
            >
              {isMonitoring ? (
                <>
                  <X className="h-4 w-4 mr-2" />
                  Stop Monitoring
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Start Monitoring
                </>
              )}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {packagingLines.map((line, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <Badge className={getStatusColor(line.status)}>
                    {getStatusIcon(line.status)}
                  </Badge>
                  <div>
                    <h4 className="font-medium text-gray-900">{line.line}</h4>
                    <p className="text-sm text-gray-600">Speed: {line.speed}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Defect Rate</p>
                    <p className="font-medium text-gray-900">{line.defectRate}%</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Last Defect</p>
                    <p className="font-medium text-gray-900">{line.lastDefect}</p>
                  </div>
                  <div className="text-center">
                    <Camera className={`h-6 w-6 ${line.camera === 'active' ? 'text-green-600' : 'text-gray-400'}`} />
                    <p className="text-xs text-gray-500">{line.camera}</p>
                  </div>
                  <div className="text-center">
                    <Badge className={line.status === 'running' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                      {line.status}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Detections */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            <span>Recent Defection Detections</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentDetections.map((detection, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="bg-red-100 p-2 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{detection.issue}</span>
                      <Badge variant="outline" className="text-xs">
                        {detection.line}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">{detection.time} - {detection.action}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Confidence</p>
                    <p className="font-medium text-sky-700">{detection.confidence}%</p>
                  </div>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Benefits Summary */}
      <Card className="border-sky-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-6 w-6 text-green-600" />
            <span>Packaging AI Benefits</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">90%</div>
              <p className="text-sm text-gray-700">Reduction in defective packages</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">75%</div>
              <p className="text-sm text-gray-700">Fewer product recalls</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">60%</div>
              <p className="text-sm text-gray-700">Improved quality confidence</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PackagingMonitoring;

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Building2, 
  MapPin, 
  TrendingUp, 
  TrendingDown,
  Users, 
  Milk, 
  Truck,
  BarChart3,
  Activity,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  Droplets,
  Heart,
  Stethoscope,
  Calendar,
  Target,
  Zap,
  Lightbulb
} from "lucide-react";
import { useState } from "react";
import TrendsAnalytics from "./TrendsAnalytics";

// Comprehensive data structure for internal operations
const internalOperationsData = {
  district: {
    name: "Kolhapur District",
    gokulUnit: {
      totalProduction: 240000,
      totalWastage: 2400,
      efficiency: 99.0,
      resourceUtilization: 94.5,
      healthIndex: 92.3,
      doctorVisits: 156,
      predictedOutput: 245000
    }
  },
  talukas: [
    {
      id: 1,
      name: "Kolhapur",
      performance: "excellent",
      villages: 156,
      dcsCount: 89,
      totalProduction: 28500,
      wastage: 285,
      efficiency: 99.0,
      healthIndex: 94.2,
      resourceUtilization: 96.1,
      doctorVisits: 24,
      predictedOutput: 29200,
      villages_data: [
        { 
          name: "Rankala", 
          dcs: 3, 
          production: 1200, 
          wastage: 12,
          farmers: 45, 
          efficiency: 99.0,
          healthIndex: 95.5,
          resourceUtilization: 97.2,
          doctorVisits: 3,
          predictedOutput: 1250,
          performance: "excellent",
          aiFlags: []
        },
        { 
          name: "Mahalaxmi", 
          dcs: 2, 
          production: 890, 
          wastage: 18,
          farmers: 32, 
          efficiency: 98.0,
          healthIndex: 91.2,
          resourceUtilization: 94.8,
          doctorVisits: 2,
          predictedOutput: 920,
          performance: "good",
          aiFlags: ["feed_optimization"]
        },
        { 
          name: "Shivaji Nagar", 
          dcs: 4, 
          production: 1560, 
          wastage: 31,
          farmers: 67, 
          efficiency: 98.0,
          healthIndex: 93.8,
          resourceUtilization: 95.5,
          doctorVisits: 4,
          predictedOutput: 1600,
          performance: "good",
          aiFlags: []
        }
      ]
    },
    {
      id: 2,
      name: "Panhala",
      performance: "good",
      villages: 98,
      dcsCount: 67,
      totalProduction: 22300,
      wastage: 334,
      efficiency: 98.5,
      healthIndex: 89.7,
      resourceUtilization: 92.3,
      doctorVisits: 18,
      predictedOutput: 23100,
      villages_data: [
        { 
          name: "Panhala Fort", 
          dcs: 2, 
          production: 980, 
          wastage: 29,
          farmers: 28, 
          efficiency: 97.0,
          healthIndex: 87.5,
          resourceUtilization: 89.2,
          doctorVisits: 2,
          predictedOutput: 1020,
          performance: "needs_attention",
          aiFlags: ["health_monitoring", "resource_optimization"]
        },
        { 
          name: "Someshwar", 
          dcs: 3, 
          production: 1340, 
          wastage: 20,
          farmers: 41, 
          efficiency: 98.5,
          healthIndex: 91.2,
          resourceUtilization: 94.1,
          doctorVisits: 3,
          predictedOutput: 1380,
          performance: "good",
          aiFlags: []
        }
      ]
    },
    {
      id: 3,
      name: "Shirol",
      performance: "needs_attention",
      villages: 87,
      dcsCount: 72,
      totalProduction: 19800,
      wastage: 495,
      efficiency: 97.5,
      healthIndex: 85.2,
      resourceUtilization: 88.7,
      doctorVisits: 15,
      predictedOutput: 20500,
      villages_data: [
        { 
          name: "Shirol", 
          dcs: 4, 
          production: 1680, 
          wastage: 67,
          farmers: 58, 
          efficiency: 96.0,
          healthIndex: 82.1,
          resourceUtilization: 85.3,
          doctorVisits: 3,
          predictedOutput: 1750,
          performance: "critical",
          aiFlags: ["urgent_health_intervention", "feed_quality", "doctor_visit_required"]
        }
      ]
    }
  ]
};

const InternalOperationsDashboard = () => {
  const [selectedLevel, setSelectedLevel] = useState<'gokul' | 'taluka' | 'village' | 'dcs'>('gokul');
  const [selectedTaluka, setSelectedTaluka] = useState<number | null>(null);
  const [selectedVillage, setSelectedVillage] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'daily' | 'monthly'>('daily');

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'needs_attention': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent': return <CheckCircle className="w-4 h-4" />;
      case 'good': return <TrendingUp className="w-4 h-4" />;
      case 'needs_attention': return <Clock className="w-4 h-4" />;
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const selectedTalukaData = selectedTaluka 
    ? internalOperationsData.talukas.find(t => t.id === selectedTaluka)
    : null;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Internal Operations Dashboard</h1>
            <p className="text-gray-600">Dairy Cooperative System - Hierarchical Operations View</p>
          </div>
          <div className="flex items-center gap-4">
            <Select value={timeRange} onValueChange={(value: 'daily' | 'monthly') => setTimeRange(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
            <Badge className="bg-green-100 text-green-800">
              <Activity className="w-3 h-3 mr-1" />
              Live Data
            </Badge>
          </div>
        </div>
      </div>

      <div className="p-6">
        <Tabs value={selectedLevel} onValueChange={(value: 'gokul' | 'taluka' | 'village' | 'dcs') => setSelectedLevel(value)} className="space-y-6">
          {/* Level Navigation */}
          <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:grid-cols-5">
            <TabsTrigger value="gokul" className="flex items-center gap-2">
              <Building2 className="w-4 h-4" />
              Gokul Unit
            </TabsTrigger>
            <TabsTrigger value="taluka" className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Taluka Level
            </TabsTrigger>
            <TabsTrigger value="village" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Village Level
            </TabsTrigger>
            <TabsTrigger value="dcs" className="flex items-center gap-2">
              <Milk className="w-4 h-4" />
              DCS Level
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Analytics
            </TabsTrigger>
          </TabsList>

          {/* Gokul Unit Level */}
          <TabsContent value="gokul" className="space-y-6">
            {/* Hierarchical Flow Visualization */}
            <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-green-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <Building2 className="h-6 w-6 text-blue-600" />
                  Gokul Unit - District Operations Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  {/* Production Metrics */}
                  <div className="text-center p-4 bg-white rounded-lg border border-blue-200">
                    <div className="flex items-center justify-center mb-2">
                      <Milk className="w-8 h-8 text-blue-600" />
                    </div>
                    <div className="text-2xl font-bold text-blue-600">
                      {internalOperationsData.district.gokulUnit.totalProduction.toLocaleString()}L
                    </div>
                    <div className="text-sm text-gray-600">Total Production</div>
                    <div className="text-xs text-green-600 mt-1">
                      ↗ {internalOperationsData.district.gokulUnit.predictedOutput.toLocaleString()}L Predicted
                    </div>
                  </div>

                  {/* Efficiency */}
                  <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                    <div className="flex items-center justify-center mb-2">
                      <Target className="w-8 h-8 text-green-600" />
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      {internalOperationsData.district.gokulUnit.efficiency}%
                    </div>
                    <div className="text-sm text-gray-600">Efficiency</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Wastage: {internalOperationsData.district.gokulUnit.totalWastage}L
                    </div>
                  </div>

                  {/* Health Index */}
                  <div className="text-center p-4 bg-white rounded-lg border border-purple-200">
                    <div className="flex items-center justify-center mb-2">
                      <Heart className="w-8 h-8 text-purple-600" />
                    </div>
                    <div className="text-2xl font-bold text-purple-600">
                      {internalOperationsData.district.gokulUnit.healthIndex}%
                    </div>
                    <div className="text-sm text-gray-600">Health Index</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Doctor Visits: {internalOperationsData.district.gokulUnit.doctorVisits}
                    </div>
                  </div>

                  {/* Resource Utilization */}
                  <div className="text-center p-4 bg-white rounded-lg border border-orange-200">
                    <div className="flex items-center justify-center mb-2">
                      <BarChart3 className="w-8 h-8 text-orange-600" />
                    </div>
                    <div className="text-2xl font-bold text-orange-600">
                      {internalOperationsData.district.gokulUnit.resourceUtilization}%
                    </div>
                    <div className="text-sm text-gray-600">Resource Utilization</div>
                    <div className="text-xs text-green-600 mt-1">Optimal Range</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Hierarchical Tree View */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <MapPin className="h-6 w-6 text-blue-600" />
                  Hierarchical Data Flow: Village → Taluka → Gokul Unit
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Flow Diagram */}
                  <div className="flex items-center justify-between p-6 bg-gradient-to-r from-blue-50 via-green-50 to-purple-50 rounded-lg border">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-2">
                        <Users className="h-8 w-8 text-white" />
                      </div>
                      <div className="text-sm font-medium">Villages</div>
                      <div className="text-xs text-gray-600">1,204 Active</div>
                    </div>

                    <div className="flex-1 h-1 bg-gradient-to-r from-blue-600 to-green-600 mx-4 relative">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-2 py-1 rounded text-xs font-medium border">
                        DCS: 847
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-2">
                        <MapPin className="h-8 w-8 text-white" />
                      </div>
                      <div className="text-sm font-medium">Talukas</div>
                      <div className="text-xs text-gray-600">12 Regions</div>
                    </div>

                    <div className="flex-1 h-1 bg-gradient-to-r from-green-600 to-purple-600 mx-4 relative">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-2 py-1 rounded text-xs font-medium border">
                        Processing
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mb-2">
                        <Building2 className="h-8 w-8 text-white" />
                      </div>
                      <div className="text-sm font-medium">Gokul Unit</div>
                      <div className="text-xs text-gray-600">Central Hub</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Taluka Level */}
          <TabsContent value="taluka" className="space-y-6">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {internalOperationsData.talukas.map((taluka) => (
                <Card
                  key={taluka.id}
                  className={`cursor-pointer transition-all duration-300 hover:shadow-lg border-2 ${
                    selectedTaluka === taluka.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                  onClick={() => setSelectedTaluka(selectedTaluka === taluka.id ? null : taluka.id)}
                >
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <MapPin className="h-5 w-5 text-blue-600" />
                        {taluka.name} Taluka
                      </span>
                      <Badge className={`${getPerformanceColor(taluka.performance)} border`}>
                        {getPerformanceIcon(taluka.performance)}
                        <span className="ml-1 capitalize">{taluka.performance.replace('_', ' ')}</span>
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Villages:</span>
                          <span className="font-medium">{taluka.villages}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">DCS:</span>
                          <span className="font-medium">{taluka.dcsCount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Production:</span>
                          <span className="font-medium text-green-600">{taluka.totalProduction.toLocaleString()}L</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Efficiency:</span>
                          <span className="font-medium">{taluka.efficiency}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Health:</span>
                          <span className="font-medium">{taluka.healthIndex}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Wastage:</span>
                          <span className="font-medium text-red-600">{taluka.wastage}L</span>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4">
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>Performance</span>
                        <span>{taluka.efficiency}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            taluka.efficiency >= 98 ? 'bg-green-600' :
                            taluka.efficiency >= 95 ? 'bg-blue-600' :
                            taluka.efficiency >= 90 ? 'bg-yellow-600' : 'bg-red-600'
                          }`}
                          style={{ width: `${taluka.efficiency}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="mt-3 flex items-center justify-between text-xs">
                      <span className="text-gray-500">Predicted: {taluka.predictedOutput.toLocaleString()}L</span>
                      <div className="flex items-center gap-1">
                        <Stethoscope className="w-3 h-3 text-purple-500" />
                        <span className="text-purple-600">{taluka.doctorVisits} visits</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Village Level */}
          <TabsContent value="village" className="space-y-6">
            {selectedTalukaData ? (
              <Card className="border-2 border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Users className="h-6 w-6 text-green-600" />
                    {selectedTalukaData.name} Taluka - Village Operations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {selectedTalukaData.villages_data.map((village, index) => (
                      <Card
                        key={index}
                        className={`cursor-pointer transition-all duration-300 hover:shadow-md border-2 ${
                          selectedVillage === village.name ? 'border-green-500 bg-green-50' : 'border-gray-200'
                        }`}
                        onClick={() => setSelectedVillage(selectedVillage === village.name ? null : village.name)}
                      >
                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <h4 className="font-semibold text-gray-800">{village.name}</h4>
                              <Badge className={`${getPerformanceColor(village.performance)} border text-xs`}>
                                {getPerformanceIcon(village.performance)}
                                <span className="ml-1 capitalize">{village.performance.replace('_', ' ')}</span>
                              </Badge>
                            </div>

                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="space-y-1">
                                <div className="flex justify-between">
                                  <span className="text-gray-600">DCS:</span>
                                  <span className="font-medium">{village.dcs}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Farmers:</span>
                                  <span className="font-medium">{village.farmers}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Production:</span>
                                  <span className="font-medium text-green-600">{village.production}L</span>
                                </div>
                              </div>
                              <div className="space-y-1">
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Efficiency:</span>
                                  <span className="font-medium">{village.efficiency}%</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Health:</span>
                                  <span className="font-medium">{village.healthIndex}%</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Wastage:</span>
                                  <span className="font-medium text-red-600">{village.wastage}L</span>
                                </div>
                              </div>
                            </div>

                            {/* AI Flags */}
                            {village.aiFlags.length > 0 && (
                              <div className="space-y-1">
                                <div className="text-xs font-medium text-gray-700">AI Recommendations:</div>
                                <div className="flex flex-wrap gap-1">
                                  {village.aiFlags.map((flag, flagIndex) => (
                                    <Badge key={flagIndex} variant="outline" className="text-xs bg-yellow-50 text-yellow-800 border-yellow-200">
                                      <Zap className="w-2 h-2 mr-1" />
                                      {flag.replace('_', ' ')}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}

                            <div className="flex items-center justify-between text-xs">
                              <span className="text-gray-500">Predicted: {village.predictedOutput}L</span>
                              <div className="flex items-center gap-1">
                                <Stethoscope className="w-3 h-3 text-purple-500" />
                                <span className="text-purple-600">{village.doctorVisits} visits</span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <MapPin className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">Select a Taluka</h3>
                  <p className="text-gray-500">Choose a taluka from the Taluka Level tab to view village operations</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* DCS Level */}
          <TabsContent value="dcs" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <Milk className="h-6 w-6 text-blue-600" />
                  Dairy Cooperative Society Operations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="text-2xl font-bold text-blue-600">847</div>
                    <div className="text-sm text-gray-600">Active DCS</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="text-2xl font-bold text-green-600">98.2%</div>
                    <div className="text-sm text-gray-600">Avg Efficiency</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <div className="text-2xl font-bold text-purple-600">91.5%</div>
                    <div className="text-sm text-gray-600">Avg Health Index</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="text-2xl font-bold text-orange-600">93.8%</div>
                    <div className="text-sm text-gray-600">Resource Utilization</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics">
            <TrendsAnalytics />
          </TabsContent>
        </Tabs>

        {/* Sidebar with Disruptive Ads */}
        <div className="fixed right-4 top-1/2 transform -translate-y-1/2 w-80 space-y-4 z-10">
          {/* Innovation Ad */}
          <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-purple-50">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Lightbulb className="w-4 h-4 text-yellow-500" />
                Innovation Spotlight
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <div className="text-xs text-gray-700 mb-2">
                <strong>Smart Feed Optimizer</strong>
              </div>
              <div className="text-xs text-gray-600 mb-3">
                AI-powered feed composition analysis increases milk production by 15%.
                Reduce costs while improving animal health.
              </div>
              <Button size="sm" className="w-full text-xs bg-gradient-to-r from-blue-500 to-purple-600">
                Learn More
              </Button>
            </CardContent>
          </Card>

          {/* Medicine Ad */}
          <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-blue-50">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Heart className="w-4 h-4 text-red-500" />
                Health Innovation
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <div className="text-xs text-gray-700 mb-2">
                <strong>Preventive Care System</strong>
              </div>
              <div className="text-xs text-gray-600 mb-3">
                Early disease detection using IoT sensors. Reduce veterinary costs by 40%
                with predictive health monitoring.
              </div>
              <Button size="sm" variant="outline" className="w-full text-xs">
                Request Demo
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Bottom Ribbon Ad */}
        <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-3 z-10">
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Zap className="w-5 h-5 text-yellow-300" />
              <div>
                <div className="text-sm font-medium">Dairy Tech Summit 2024</div>
                <div className="text-xs opacity-90">Latest innovations in dairy technology - Register now for early bird pricing</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button size="sm" variant="secondary" className="text-xs">
                Register
              </Button>
              <Button size="sm" variant="ghost" className="text-xs text-white hover:text-gray-200">
                ✕
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InternalOperationsDashboard;

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { 
  Milk, 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  PieChart,
  Activity,
  Wheat,
  Target,
  Calendar,
  MapPin
} from "lucide-react";

// Mock data for milk contribution insights
const mockMilkData = {
  regions: [
    {
      id: "kolhapur",
      name: "Kolhapur",
      dailyVolume: 6800,
      targetVolume: 7000,
      contributingAnimals: 830,
      totalAnimals: 950,
      avgHealthScore: 87,
      feedEfficiency: 0.68, // liters per kg feed
      qualityGrade: "A+",
      trend: "increasing"
    },
    {
      id: "sangli", 
      name: "<PERSON><PERSON>",
      dailyVolume: 5200,
      targetVolume: 5500,
      contributingAnimals: 640,
      totalAnimals: 750,
      avgHealthScore: 82,
      feedEfficiency: 0.65,
      qualityGrade: "A",
      trend: "stable"
    },
    {
      id: "satara",
      name: "Satara", 
      dailyVolume: 4100,
      targetVolume: 4200,
      contributingAnimals: 420,
      totalAnimals: 480,
      avgHealthScore: 85,
      feedEfficiency: 0.71,
      qualityGrade: "A+",
      trend: "increasing"
    }
  ],
  animalTypes: [
    {
      type: "cow",
      emoji: "🐄",
      totalAnimals: 1280,
      contributingAnimals: 1150,
      avgDailyMilk: 16.5,
      avgFeedConsumption: 24.2,
      efficiency: 0.68
    },
    {
      type: "buffalo",
      emoji: "🐃", 
      totalAnimals: 890,
      contributingAnimals: 820,
      avgDailyMilk: 22.8,
      avgFeedConsumption: 32.5,
      efficiency: 0.70
    },
    {
      type: "goat",
      emoji: "🐐",
      totalAnimals: 510,
      contributingAnimals: 420,
      avgDailyMilk: 3.2,
      avgFeedConsumption: 4.8,
      efficiency: 0.67
    }
  ]
};

const MilkContributionInsights = () => {
  const [selectedRegion, setSelectedRegion] = useState<string>("all");
  const [selectedPeriod, setSelectedPeriod] = useState<string>("today");

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "increasing":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "decreasing":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-blue-600" />;
    }
  };

  const getQualityColor = (grade: string) => {
    switch (grade) {
      case "A+": return "bg-green-100 text-green-800";
      case "A": return "bg-blue-100 text-blue-800";
      case "B+": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const filteredRegions = selectedRegion === "all" 
    ? mockMilkData.regions 
    : mockMilkData.regions.filter(region => region.id === selectedRegion);

  const calculateTotals = () => {
    const totalVolume = filteredRegions.reduce((sum, region) => sum + region.dailyVolume, 0);
    const totalTarget = filteredRegions.reduce((sum, region) => sum + region.targetVolume, 0);
    const totalContributing = filteredRegions.reduce((sum, region) => sum + region.contributingAnimals, 0);
    const totalAnimals = filteredRegions.reduce((sum, region) => sum + region.totalAnimals, 0);
    const avgEfficiency = filteredRegions.reduce((sum, region) => sum + region.feedEfficiency, 0) / filteredRegions.length;

    return { totalVolume, totalTarget, totalContributing, totalAnimals, avgEfficiency };
  };

  const totals = calculateTotals();

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gokul Milk Contribution Insights</h2>
          <p className="text-gray-600">Track milk production, quality, and efficiency across regions</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={selectedRegion} onValueChange={setSelectedRegion}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Regions</SelectItem>
              <SelectItem value="kolhapur">Kolhapur</SelectItem>
              <SelectItem value="sangli">Sangli</SelectItem>
              <SelectItem value="satara">Satara</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Daily Volume</p>
                <p className="text-3xl font-bold text-blue-900">{totals.totalVolume.toLocaleString()}L</p>
                <p className="text-xs text-blue-600 mt-1">
                  Target: {totals.totalTarget.toLocaleString()}L
                </p>
              </div>
              <div className="bg-blue-200 p-3 rounded-full">
                <Milk className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Contributing Animals</p>
                <p className="text-3xl font-bold text-green-900">{totals.totalContributing}</p>
                <p className="text-xs text-green-600 mt-1">
                  of {totals.totalAnimals} total
                </p>
              </div>
              <div className="bg-green-200 p-3 rounded-full">
                <Target className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Feed Efficiency</p>
                <p className="text-3xl font-bold text-purple-900">{totals.avgEfficiency.toFixed(2)}</p>
                <p className="text-xs text-purple-600 mt-1">L per kg feed</p>
              </div>
              <div className="bg-purple-200 p-3 rounded-full">
                <Wheat className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">Target Achievement</p>
                <p className="text-3xl font-bold text-orange-900">
                  {((totals.totalVolume / totals.totalTarget) * 100).toFixed(1)}%
                </p>
              </div>
              <div className="bg-orange-200 p-3 rounded-full">
                <BarChart3 className="h-6 w-6 text-orange-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Regional Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredRegions.map(region => (
          <Card key={region.id} className="shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  {region.name}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge className={getQualityColor(region.qualityGrade)}>
                    {region.qualityGrade}
                  </Badge>
                  {getTrendIcon(region.trend)}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Volume Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Daily Volume</span>
                  <span className="text-blue-600 font-bold">
                    {region.dailyVolume.toLocaleString()}L / {region.targetVolume.toLocaleString()}L
                  </span>
                </div>
                <Progress 
                  value={(region.dailyVolume / region.targetVolume) * 100} 
                  className="h-2"
                />
              </div>

              {/* Animal Contribution */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Animal Participation</span>
                  <span className="text-green-600 font-bold">
                    {region.contributingAnimals} / {region.totalAnimals}
                  </span>
                </div>
                <Progress 
                  value={(region.contributingAnimals / region.totalAnimals) * 100} 
                  className="h-2"
                />
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-green-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-green-900">{region.avgHealthScore}%</div>
                  <div className="text-xs text-green-600">Avg Health Score</div>
                </div>
                <div className="bg-blue-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-blue-900">{region.feedEfficiency}</div>
                  <div className="text-xs text-blue-600">Feed Efficiency</div>
                </div>
              </div>

              {/* Health-Output Correlation */}
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-sm font-semibold text-gray-900 mb-2">Health-Output Correlation</div>
                <div className="text-sm text-gray-700">
                  High health scores correlate with {region.avgHealthScore > 85 ? "excellent" : "good"} milk production
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Correlation coefficient: {(region.avgHealthScore / 100 * 0.85 + 0.15).toFixed(2)}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Animal Type Analysis */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5 text-purple-600" />
            Animal Type Performance Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {mockMilkData.animalTypes.map(animal => (
              <div key={animal.type} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-3 mb-3">
                  <div className="text-3xl">{animal.emoji}</div>
                  <div>
                    <div className="font-semibold text-gray-900 capitalize">{animal.type}s</div>
                    <div className="text-sm text-gray-500">
                      {animal.contributingAnimals} of {animal.totalAnimals} contributing
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Avg Daily Milk:</span>
                    <span className="font-medium">{animal.avgDailyMilk}L</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Avg Feed Consumption:</span>
                    <span className="font-medium">{animal.avgFeedConsumption}kg</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Efficiency Ratio:</span>
                    <span className="font-medium text-green-600">{animal.efficiency}</span>
                  </div>
                  
                  <div className="pt-2 border-t border-gray-200">
                    <div className="text-xs text-gray-500">Participation Rate</div>
                    <Progress 
                      value={(animal.contributingAnimals / animal.totalAnimals) * 100} 
                      className="h-2 mt-1"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MilkContributionInsights;

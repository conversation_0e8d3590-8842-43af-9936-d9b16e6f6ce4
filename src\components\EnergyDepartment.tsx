
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Zap, 
  Sun, 
  Thermometer,
  TrendingUp,
  TrendingDown,
  Battery,
  Settings,
  AlertTriangle
} from "lucide-react";
import DateRangeFilter from "@/components/DateRangeFilter";

const EnergyDepartment = () => {
  const [dateRange, setDateRange] = useState("today");

  const energyOverview = {
    totalConsumption: 8500, // kWh
    solarGeneration: 3200, // kWh
    gridConsumption: 5300, // kWh
    savings: 1850, // ₹
    efficiency: 87.5, // %
    carbonFootprint: 2.4 // tons CO2
  };

  const solarData = {
    currentGeneration: 450, // kW
    peakGeneration: 520, // kW
    todayTotal: 3200, // kWh
    monthlyTotal: 85000, // kWh
    efficiency: 89.2, // %
    weatherImpact: "Clear skies, optimal performance"
  };

  const coldStorageData = [
    {
      room: "Cold Storage A",
      temperature: 4.2,
      targetTemp: 4.0,
      humidity: 85,
      energyUsage: 125,
      status: "optimal",
      compressorCycles: 12
    },
    {
      room: "Cold Storage B", 
      temperature: 6.1,
      targetTemp: 5.0,
      humidity: 78,
      energyUsage: 142,
      status: "warning",
      compressorCycles: 18
    },
    {
      room: "Freezer Unit",
      temperature: -18.5,
      targetTemp: -18.0,
      humidity: 65,
      energyUsage: 98,
      status: "optimal",
      compressorCycles: 8
    }
  ];

  const equipmentData = [
    {
      equipment: "Milk Processing Unit 1",
      power: 85,
      efficiency: 92.5,
      runTime: 18.5,
      status: "running",
      lastMaintenance: "2 weeks ago"
    },
    {
      equipment: "Packaging Line 1",
      power: 45,
      efficiency: 88.2,
      runTime: 16.2,
      status: "running",
      lastMaintenance: "1 week ago"
    },
    {
      equipment: "Compressor System",
      power: 220,
      efficiency: 85.8,
      runTime: 22.1,
      status: "warning",
      lastMaintenance: "3 weeks ago"
    },
    {
      equipment: "Lighting System",
      power: 25,
      efficiency: 95.2,
      runTime: 24,
      status: "optimal",
      lastMaintenance: "1 month ago"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'optimal': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTemperatureStatus = (current: number, target: number) => {
    const diff = Math.abs(current - target);
    if (diff <= 0.5) return 'optimal';
    if (diff <= 1.0) return 'warning';
    return 'critical';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Energy Management Dashboard</h2>
          <p className="text-sm text-gray-600">Monitor energy consumption, solar generation, and optimization</p>
        </div>
        <DateRangeFilter value={dateRange} onChange={setDateRange} />
      </div>

      {/* Energy Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-4 text-center">
            <Zap className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Total Consumption</p>
            <p className="text-xl font-bold text-gray-900">{energyOverview.totalConsumption}</p>
            <p className="text-xs text-gray-500">kWh today</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-4 text-center">
            <Sun className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Solar Generation</p>
            <p className="text-xl font-bold text-gray-900">{energyOverview.solarGeneration}</p>
            <p className="text-xs text-gray-500">kWh today</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-4 text-center">
            <Battery className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Grid Usage</p>
            <p className="text-xl font-bold text-gray-900">{energyOverview.gridConsumption}</p>
            <p className="text-xs text-gray-500">kWh today</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Cost Savings</p>
            <p className="text-xl font-bold text-gray-900">{formatCurrency(energyOverview.savings)}</p>
            <p className="text-xs text-gray-500">today</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-4 text-center">
            <div className="bg-sky-100 p-2 rounded-full w-8 h-8 mx-auto mb-2 flex items-center justify-center">
              <span className="text-xs font-bold text-sky-600">%</span>
            </div>
            <p className="text-sm text-gray-600">Efficiency</p>
            <p className="text-xl font-bold text-gray-900">{energyOverview.efficiency}%</p>
            <p className="text-xs text-gray-500">overall</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-4 text-center">
            <div className="bg-green-100 p-2 rounded-full w-8 h-8 mx-auto mb-2 flex items-center justify-center">
              <span className="text-xs font-bold text-green-600">CO₂</span>
            </div>
            <p className="text-sm text-gray-600">Carbon Saved</p>
            <p className="text-xl font-bold text-gray-900">{energyOverview.carbonFootprint}</p>
            <p className="text-xs text-gray-500">tons today</p>
          </CardContent>
        </Card>
      </div>

      {/* Solar Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sun className="h-5 w-5 text-orange-600" />
            <span>Solar Power Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-orange-50 rounded-lg">
                  <p className="text-sm text-gray-600">Current Generation</p>
                  <p className="text-2xl font-bold text-orange-600">{solarData.currentGeneration} kW</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600">Peak: {solarData.peakGeneration} kW</span>
                  </div>
                </div>
                <div className="p-4 bg-orange-50 rounded-lg">
                  <p className="text-sm text-gray-600">Today's Total</p>
                  <p className="text-2xl font-bold text-orange-600">{solarData.todayTotal} kWh</p>
                  <p className="text-sm text-gray-500">Efficiency: {solarData.efficiency}%</p>
                </div>
              </div>
              
              <div className="p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-gray-600 mb-2">Weather Impact</p>
                <p className="text-sm text-gray-800">{solarData.weatherImpact}</p>
                <div className="mt-2">
                  <Progress value={solarData.efficiency} className="h-2" />
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold mb-3">Monthly Performance</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Generation</span>
                    <span className="text-sm font-medium">{solarData.monthlyTotal.toLocaleString()} kWh</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Cost Savings</span>
                    <span className="text-sm font-medium text-green-600">{formatCurrency(45000)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">CO₂ Reduction</span>
                    <span className="text-sm font-medium text-green-600">65 tons</span>
                  </div>
                </div>
              </div>

              <Button className="w-full bg-orange-600 hover:bg-orange-700">
                <Sun className="h-4 w-4 mr-2" />
                View Detailed Solar Analytics
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cold Storage Monitoring */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Thermometer className="h-5 w-5 text-blue-600" />
            <span>Cold Storage Monitoring</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {coldStorageData.map((storage, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-sky-300 transition-colors">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      <Thermometer className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{storage.room}</h3>
                      <p className="text-sm text-gray-600">Target: {storage.targetTemp}°C</p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(getTemperatureStatus(storage.temperature, storage.targetTemp))}>
                    {getTemperatureStatus(storage.temperature, storage.targetTemp)}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Temperature</p>
                    <p className={`text-xl font-bold ${
                      Math.abs(storage.temperature - storage.targetTemp) > 1 ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {storage.temperature}°C
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Humidity</p>
                    <p className="text-xl font-bold text-gray-900">{storage.humidity}%</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Energy Usage</p>
                    <p className="text-xl font-bold text-gray-900">{storage.energyUsage}</p>
                    <p className="text-xs text-gray-500">kW/h</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Compressor Cycles</p>
                    <p className="text-xl font-bold text-gray-900">{storage.compressorCycles}</p>
                    <p className="text-xs text-gray-500">today</p>
                  </div>
                </div>

                {storage.status === 'warning' && (
                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm text-yellow-800">
                        Temperature deviation detected. Consider adjusting compressor settings.
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Equipment Energy Usage */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-sky-600" />
            <span>Equipment Energy Monitoring</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {equipmentData.map((equipment, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="bg-sky-100 p-3 rounded-lg">
                    <Zap className="h-6 w-6 text-sky-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{equipment.equipment}</h4>
                    <p className="text-sm text-gray-600">Last maintenance: {equipment.lastMaintenance}</p>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-6 text-center">
                  <div>
                    <p className="text-sm text-gray-600">Power Usage</p>
                    <p className="text-lg font-semibold text-gray-900">{equipment.power} kW</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Efficiency</p>
                    <p className="text-lg font-semibold text-gray-900">{equipment.efficiency}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Runtime</p>
                    <p className="text-lg font-semibold text-gray-900">{equipment.runTime}h</p>
                  </div>
                </div>

                <div className="text-right">
                  <Badge className={getStatusColor(equipment.status)}>
                    {equipment.status}
                  </Badge>
                  {equipment.status === 'warning' && (
                    <Button size="sm" variant="outline" className="mt-2">
                      <Settings className="h-4 w-4 mr-1" />
                      Optimize
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnergyDepartment;

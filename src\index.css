
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.



*/

.notranslate {
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -ms-transform: none !important;
  -o-transform: none !important;
  transform: none !important;
}

/* Prevent Google Translate from duplicating select values */
[data-radix-select-trigger] .notranslate,
[data-radix-select-value] .notranslate,
[data-radix-select-item] .notranslate {
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -ms-transform: none !important;
  -o-transform: none !important;
  transform: none !important;
  font-family: inherit !important;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  .VIpgJd-ZVi9od-ORHb-OEVmcd.skiptranslate,
  .goog-te-banner-frame,
  .goog-te-balloon-frame,
  .skiptranslate iframe {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
  }

  body {
    top: 0px !important;
    margin-top: 0px !important;
  }

  #google_translate_element {
    display: block !important;
  }

  .goog-te-gadget {
    font-family: 'Inter', sans-serif !important;
    color: transparent !important;
    background: transparent !important;
    border: none !important;
    font-size: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .goog-te-gadget-simple {
    background-color: white !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
    padding: 8px 12px !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    height: 44px !important;
    min-width: 150px;
    cursor: pointer;
  }

  .goog-te-menu-value span {
    font-size: 15px !important;
    color: #333 !important;
    font-weight: 500 !important;
  }

  .goog-te-menu-value span:first-child {
    margin-right: auto;
  }

  .goog-te-menu-value span:last-child {
    font-size: 13px !important;
    color: #888 !important;
  }

  .goog-te-gadget-icon {
    background-image: url("https://translate.googleapis.com/translate_static/img/te_ctrl3.gif") !important;
    background-position: -65px 0px !important;
    background-size: auto !important;
    width: 20px !important;
    height: 20px !important;
    display: inline-block !important;
  }

  .goog-te-menu-value .goog-te-menu-value-arrow {
    margin-left: 4px !important;
  }

  .VIpgJd-ZVi9od-xl07Ob-lTBxed {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    color: #333 !important;
    text-decoration: none !important;
    width: 100%;
  }

  .VIpgJd-ZVi9od-xl07Ob-lTBxed span {
    margin-right: 6px;
  }
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }
  
  .font-inter {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}
._ReactTransliterate_1b0d4b{
  cursor: pointer;
  padding: 15px;
  min-width: 100px;
  margin-top: -290px;
}
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

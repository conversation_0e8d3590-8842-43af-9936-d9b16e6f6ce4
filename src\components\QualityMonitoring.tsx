
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Beaker, 
  Camera, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  Target,
  Activity
} from "lucide-react";

const QualityMonitoring = () => {
  const [snfValue, setSnfValue] = useState(8.5);
  const [fatValue, setFatValue] = useState(3.2);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSnfValue(prev => Math.max(7.5, Math.min(9.5, prev + (Math.random() - 0.5) * 0.2)));
      setFatValue(prev => Math.max(2.8, Math.min(4.0, prev + (Math.random() - 0.5) * 0.1)));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const qualityMetrics = [
    {
      parameter: "SNF (Solids Not Fat)",
      current: snfValue.toFixed(1),
      target: "8.5",
      unit: "%",
      status: snfValue >= 8.0 ? "good" : snfValue >= 7.5 ? "warning" : "critical",
      trend: "+0.2% from yesterday"
    },
    {
      parameter: "Fat Content",
      current: fatValue.toFixed(1),
      target: "3.2",
      unit: "%",
      status: fatValue >= 3.0 ? "good" : fatValue >= 2.5 ? "warning" : "critical",
      trend: "+0.1% from yesterday"
    },
    {
      parameter: "pH Level",
      current: "6.8",
      target: "6.7",
      unit: "",
      status: "good",
      trend: "Stable"
    },
    {
      parameter: "Temperature",
      current: "4.2",
      target: "4.0",
      unit: "°C",
      status: "warning",
      trend: "+0.2°C from target"
    }
  ];

  const recentTests = [
    { time: "09:45 AM", batch: "B001", snf: 8.6, fat: 3.3, status: "passed", confidence: 98 },
    { time: "09:30 AM", batch: "B002", snf: 8.2, fat: 3.1, status: "passed", confidence: 95 },
    { time: "09:15 AM", batch: "B003", snf: 7.8, fat: 2.9, status: "warning", confidence: 92 },
    { time: "09:00 AM", batch: "B004", snf: 8.7, fat: 3.4, status: "passed", confidence: 97 },
    { time: "08:45 AM", batch: "B005", snf: 8.1, fat: 3.0, status: "passed", confidence: 94 }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': case 'passed': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': case 'passed': return <CheckCircle className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'critical': case 'failed': return <AlertTriangle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const startAnalysis = () => {
    setIsAnalyzing(true);
    setTimeout(() => setIsAnalyzing(false), 3000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-sky-200 bg-gradient-to-r from-sky-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Beaker className="h-6 w-6 text-sky-600" />
            <span>AI-Powered Milk Quality & Lab Monitoring</span>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Real-time quality analysis using computer vision and machine learning
          </p>
        </CardHeader>
      </Card>

      {/* Real-time Quality Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {qualityMetrics.map((metric, index) => (
          <Card key={index} className="border-sky-200">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-700">{metric.parameter}</h4>
                  <Badge className={getStatusColor(metric.status)}>
                    {getStatusIcon(metric.status)}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <div className="flex items-end space-x-2">
                    <span className="text-2xl font-bold text-sky-700">
                      {metric.current}
                    </span>
                    <span className="text-sm text-gray-500">{metric.unit}</span>
                  </div>
                  <p className="text-xs text-gray-500">Target: {metric.target}{metric.unit}</p>
                </div>
                <p className="text-xs text-gray-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {metric.trend}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* AI Analysis Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-sky-200">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Camera className="h-5 w-5 text-sky-600" />
              <span>Visual Anomaly Detection</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-100 rounded-lg p-6 flex items-center justify-center min-h-48">
              <div className="text-center space-y-3">
                <Camera className="h-12 w-12 text-gray-400 mx-auto" />
                <p className="text-gray-600">Live camera feed from lab equipment</p>
                <Button 
                  onClick={startAnalysis}
                  disabled={isAnalyzing}
                  className="bg-sky-600 hover:bg-sky-700"
                >
                  {isAnalyzing ? (
                    <>
                      <Activity className="h-4 w-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Target className="h-4 w-4 mr-2" />
                      Start Analysis
                    </>
                  )}
                </Button>
              </div>
            </div>
            {isAnalyzing && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>AI Processing...</span>
                  <span>87%</span>
                </div>
                <Progress value={87} className="h-2" />
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="border-sky-200">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-sky-600" />
              <span>Recent Test Results</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentTests.map((test, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge className={getStatusColor(test.status)}>
                      {getStatusIcon(test.status)}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium">{test.batch} - {test.time}</p>
                      <p className="text-xs text-gray-600">
                        SNF: {test.snf}% | Fat: {test.fat}%
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">Confidence</p>
                    <p className="text-sm font-medium text-sky-700">{test.confidence}%</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Benefits Summary */}
      <Card className="border-sky-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            <span>AI Quality Monitoring Benefits</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">95%</div>
              <p className="text-sm text-gray-700">Faster lab turnaround time</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">85%</div>
              <p className="text-sm text-gray-700">Reduction in rejected batches</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">90%</div>
              <p className="text-sm text-gray-700">Fewer calibration errors</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QualityMonitoring;

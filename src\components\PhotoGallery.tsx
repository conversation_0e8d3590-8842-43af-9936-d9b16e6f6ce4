
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Camera, 
  Upload, 
  Eye, 
  Download,
  Calendar,
  MapPin,
  Factory,
  Package,
  Truck,
  Zap
} from "lucide-react";

const PhotoGallery = () => {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedPhoto, setSelectedPhoto] = useState<any>(null);

  const categories = [
    { id: "all", name: "All Photos", icon: <Camera className="h-4 w-4" /> },
    { id: "facility", name: "Facility & Infrastructure", icon: <Factory className="h-4 w-4" /> },
    { id: "production", name: "Production Lines", icon: <Package className="h-4 w-4" /> },
    { id: "quality", name: "Quality Testing", icon: <Eye className="h-4 w-4" /> },
    { id: "distribution", name: "Distribution & Logistics", icon: <Truck className="h-4 w-4" /> },
    { id: "solar", name: "Solar Plant", icon: <Zap className="h-4 w-4" /> }
  ];

  const photoData = [
    {
      id: 1,
      title: "Main Processing Unit",
      category: "facility",
      date: "2024-01-15",
      location: "Kolhapur Plant",
      description: "Primary milk processing facility with automated systems",
      thumbnail: "/api/placeholder/300/200",
      fullImage: "/api/placeholder/800/600",
      tags: ["processing", "automated", "facility"]
    },
    {
      id: 2,
      title: "Quality Testing Lab",
      category: "quality",
      date: "2024-01-14",
      location: "Quality Control Lab",
      description: "State-of-the-art testing equipment for milk quality analysis",
      thumbnail: "/api/placeholder/300/200",
      fullImage: "/api/placeholder/800/600",
      tags: ["testing", "quality", "lab"]
    },
    {
      id: 3,
      title: "Packaging Line 1",
      category: "production",
      date: "2024-01-13",
      location: "Production Floor A",
      description: "Automated packaging line with AI-powered quality inspection",
      thumbnail: "/api/placeholder/300/200",
      fullImage: "/api/placeholder/800/600",
      tags: ["packaging", "automation", "production"]
    },
    {
      id: 4,
      title: "Solar Panel Installation",
      category: "solar",
      date: "2024-01-12",
      location: "Rooftop - Building A",
      description: "6.5 MWp solar plant installation progress",
      thumbnail: "/api/placeholder/300/200",
      fullImage: "/api/placeholder/800/600",
      tags: ["solar", "renewable", "energy"]
    },
    {
      id: 5,
      title: "Distribution Fleet",
      category: "distribution",
      date: "2024-01-11",
      location: "Loading Bay",
      description: "Temperature-controlled vehicles for product distribution",
      thumbnail: "/api/placeholder/300/200",
      fullImage: "/api/placeholder/800/600",
      tags: ["distribution", "logistics", "vehicles"]
    },
    {
      id: 6,
      title: "Cold Storage Unit",
      category: "facility",
      date: "2024-01-10",
      location: "Cold Storage B",
      description: "Advanced cold storage facility maintaining optimal temperatures",
      thumbnail: "/api/placeholder/300/200",
      fullImage: "/api/placeholder/800/600",
      tags: ["storage", "temperature", "facility"]
    }
  ];

  const filteredPhotos = selectedCategory === "all" 
    ? photoData 
    : photoData.filter(photo => photo.category === selectedCategory);

  const openPhotoModal = (photo: any) => {
    setSelectedPhoto(photo);
  };

  const closePhotoModal = () => {
    setSelectedPhoto(null);
  };

  return (
    <div className="space-y-6">
      {/* Header & Upload */}
      <Card className="border-blue-200 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 text-blue-800">
              <Camera className="h-6 w-6" />
              <span>Photo Gallery</span>
            </CardTitle>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Upload className="h-4 w-4 mr-2" />
              Upload Photos
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Category Filter */}
      <Card className="shadow-lg">
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-3">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                onClick={() => setSelectedCategory(category.id)}
                className={`${
                  selectedCategory === category.id 
                    ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                    : 'hover:bg-blue-50 border-blue-200'
                }`}
              >
                {category.icon}
                <span className="ml-2">{category.name}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Photo Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPhotos.map((photo) => (
          <Card key={photo.id} className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow cursor-pointer">
            <div 
              className="relative aspect-video bg-gray-200"
              onClick={() => openPhotoModal(photo)}
            >
              <img 
                src={photo.thumbnail} 
                alt={photo.title}
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute top-3 right-3">
                <Badge className="bg-white/90 text-gray-800">
                  {categories.find(cat => cat.id === photo.category)?.name}
                </Badge>
              </div>
            </div>
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 mb-2">{photo.title}</h3>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{photo.description}</p>
              
              <div className="space-y-2">
                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span>{photo.date}</span>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>{photo.location}</span>
                </div>
              </div>

              <div className="flex flex-wrap gap-1 mt-3">
                {photo.tags.map((tag: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>

              <div className="flex justify-between items-center mt-4">
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => openPhotoModal(photo)}
                  className="hover:bg-blue-50"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log(`Downloading ${photo.title}`);
                  }}
                  className="hover:bg-green-50"
                >
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Photo Modal */}
      {selectedPhoto && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto">
            <div className="relative">
              <img 
                src={selectedPhoto.fullImage} 
                alt={selectedPhoto.title}
                className="w-full h-auto max-h-[60vh] object-contain"
              />
              <Button
                className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white"
                onClick={closePhotoModal}
                size="sm"
              >
                ×
              </Button>
            </div>
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedPhoto.title}</h2>
              <p className="text-gray-600 mb-4">{selectedPhoto.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Date:</strong> {selectedPhoto.date}
                </div>
                <div>
                  <strong>Location:</strong> {selectedPhoto.location}
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mt-4">
                {selectedPhoto.tags.map((tag: string, index: number) => (
                  <Badge key={index} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <Button onClick={closePhotoModal} variant="outline">
                  Close
                </Button>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Download className="h-4 w-4 mr-2" />
                  Download Full Size
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Statistics */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>Gallery Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{photoData.length}</div>
              <div className="text-sm text-gray-600">Total Photos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{categories.length - 1}</div>
              <div className="text-sm text-gray-600">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">2.3GB</div>
              <div className="text-sm text-gray-600">Total Size</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">15</div>
              <div className="text-sm text-gray-600">This Month</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PhotoGallery;

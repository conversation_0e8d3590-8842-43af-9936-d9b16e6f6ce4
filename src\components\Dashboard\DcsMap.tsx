import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  MapPin,
  Building2,
  Users,
  Milk,
  Heart,
  Stethoscope,
  AlertTriangle,
  CheckCircle,
  Activity,
  Droplets,
  Map,
  List
} from "lucide-react";

// Enhanced Interactive Map Component with realistic positioning
const InteractiveMapView = ({ dcsData, selectedDcs, onDcsSelect }: any) => {
  // Realistic positioning based on actual Kolhapur district geography
  const getRealisticPosition = (dcs: any) => {
    const positions: { [key: string]: { x: number; y: number } } = {
      'Shivane DCS': { x: 52, y: 48 }, // Near Kolhapur city
      'Vadgaon DCS': { x: 75, y: 25 }, // Hatkanangle area
      'Ichalkaranji DCS': { x: 85, y: 55 }, // Shirol area
      'Rankala DCS': { x: 48, y: 52 }, // Kolhapur central
      'Panhala DCS': { x: 35, y: 20 }, // Panhala hills
    };
    return positions[dcs.name] || { x: 50 + Math.random() * 30, y: 30 + Math.random() * 40 };
  };

  return (
    <div className="relative h-96 bg-gradient-to-br from-slate-100 via-blue-50 to-green-50 rounded-lg border-2 border-blue-200 overflow-hidden">
      {/* Stylized Map Background with District Outline */}
      <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
        {/* District boundary (stylized Kolhapur shape) */}
        <path
          d="M15,25 Q20,15 35,20 Q50,10 70,25 Q85,20 90,35 Q95,50 85,65 Q80,80 65,85 Q45,90 25,80 Q10,70 15,55 Q12,40 15,25 Z"
          fill="rgba(59, 130, 246, 0.1)"
          stroke="rgba(59, 130, 246, 0.3)"
          strokeWidth="0.5"
          strokeDasharray="2,1"
        />

        {/* Taluka boundaries (simplified) */}
        <path d="M15,25 Q35,30 50,25 Q50,45 35,50 Q20,45 15,25" fill="rgba(34, 197, 94, 0.05)" stroke="rgba(34, 197, 94, 0.2)" strokeWidth="0.3" />
        <path d="M50,25 Q70,20 85,35 Q70,40 50,45 Q50,25" fill="rgba(168, 85, 247, 0.05)" stroke="rgba(168, 85, 247, 0.2)" strokeWidth="0.3" />
        <path d="M35,50 Q50,45 70,55 Q65,70 45,75 Q35,65 35,50" fill="rgba(249, 115, 22, 0.05)" stroke="rgba(249, 115, 22, 0.2)" strokeWidth="0.3" />

        {/* Rivers/waterways */}
        <path d="M20,30 Q40,35 60,40 Q80,45 90,50" stroke="rgba(59, 130, 246, 0.4)" strokeWidth="0.8" fill="none" strokeLinecap="round" />
        <path d="M30,60 Q50,65 70,70" stroke="rgba(59, 130, 246, 0.3)" strokeWidth="0.6" fill="none" strokeLinecap="round" />
      </svg>

      {/* Gokul Unit - Central Processing Hub */}
      <div
        className="absolute w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full border-4 border-white shadow-xl cursor-pointer transform -translate-x-1/2 -translate-y-1/2 hover:scale-110 transition-all duration-300 z-20"
        style={{ left: '50%', top: '45%' }}
        title="Gokul Dairy Unit - Central Processing Hub"
      >
        <Building2 className="w-4 h-4 text-white absolute top-1 left-1" />
        <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
          Gokul Unit
        </div>
      </div>

      {/* DCS Markers with realistic positioning */}
      {dcsData.map((dcs: any) => {
        const position = getRealisticPosition(dcs);
        const color = dcs.performance === 'excellent' ? 'from-green-500 to-green-600' :
                     dcs.performance === 'good' ? 'from-blue-500 to-blue-600' :
                     dcs.performance === 'needs_attention' ? 'from-yellow-500 to-yellow-600' : 'from-red-500 to-red-600';

        const borderColor = dcs.performance === 'excellent' ? 'border-green-300' :
                           dcs.performance === 'good' ? 'border-blue-300' :
                           dcs.performance === 'needs_attention' ? 'border-yellow-300' : 'border-red-300';

        return (
          <div key={dcs.id} className="absolute z-10">
            <div
              className={`w-5 h-5 bg-gradient-to-br ${color} rounded-full border-3 border-white shadow-lg cursor-pointer transform -translate-x-1/2 -translate-y-1/2 hover:scale-125 transition-all duration-200 ${
                selectedDcs?.id === dcs.id ? `ring-4 ${borderColor} scale-125` : ''
              }`}
              style={{ left: `${position.x}%`, top: `${position.y}%` }}
              onClick={() => onDcsSelect(dcs)}
              title={`${dcs.name} - ${dcs.village}, ${dcs.taluka}`}
            >
              <Droplets className="w-2.5 h-2.5 text-white absolute top-0.5 left-0.5" />
            </div>

            {/* DCS Label */}
            <div
              className="absolute text-xs bg-white px-2 py-1 rounded shadow-md border transform -translate-x-1/2 pointer-events-none"
              style={{ left: `${position.x}%`, top: `${position.y + 8}%` }}
            >
              <div className="font-medium text-gray-800">{dcs.village}</div>
              <div className="text-gray-500">{dcs.milkOutput}L</div>
            </div>
          </div>
        );
      })}

      {/* Connection Lines to Gokul Unit */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none z-5">
        {dcsData.map((dcs: any) => {
          const position = getRealisticPosition(dcs);
          const opacity = selectedDcs?.id === dcs.id ? 0.8 : 0.3;
          const strokeWidth = selectedDcs?.id === dcs.id ? 2 : 1;

          return (
            <line
              key={dcs.id}
              x1={`${position.x}%`}
              y1={`${position.y}%`}
              x2="50%"
              y2="45%"
              stroke="#3B82F6"
              strokeWidth={strokeWidth}
              strokeOpacity={opacity}
              strokeDasharray="3,2"
            />
          );
        })}
      </svg>

      {/* Enhanced Legend */}
      <div className="absolute bottom-4 left-4 bg-white p-4 rounded-lg shadow-xl border-2 border-gray-200">
        <div className="text-sm font-bold text-gray-800 mb-3 flex items-center gap-2">
          <Map className="w-4 h-4" />
          Performance Legend
        </div>
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 bg-gradient-to-br from-green-500 to-green-600 rounded-full border border-white shadow"></div>
            <span className="text-gray-700">Excellent (90%+)</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full border border-white shadow"></div>
            <span className="text-gray-700">Good (80-89%)</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full border border-white shadow"></div>
            <span className="text-gray-700">Needs Attention (70-79%)</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 bg-gradient-to-br from-red-500 to-red-600 rounded-full border border-white shadow"></div>
            <span className="text-gray-700">Critical (<70%)</span>
          </div>
        </div>
      </div>

      {/* District Information */}
      <div className="absolute top-4 right-4 bg-white p-3 rounded-lg shadow-xl border-2 border-gray-200">
        <div className="text-sm font-bold text-gray-800 flex items-center gap-2">
          <MapPin className="w-4 h-4 text-blue-600" />
          Kolhapur District
        </div>
        <div className="text-xs text-gray-600 mt-1">16.705°N, 74.243°E</div>
        <div className="text-xs text-gray-500 mt-1">{dcsData.length} DCS Centers</div>
      </div>

      {/* Scale indicator */}
      <div className="absolute bottom-4 right-4 bg-white p-2 rounded-lg shadow-lg border">
        <div className="text-xs text-gray-600 mb-1">Scale</div>
        <div className="flex items-center gap-2">
          <div className="w-8 h-0.5 bg-gray-400"></div>
          <span className="text-xs text-gray-500">~50km</span>
        </div>
      </div>
    </div>
  );
};

// Enhanced dummy data with hierarchical structure
const dcsMapData = [
  {
    id: 1,
    name: "Shivane DCS",
    village: "Shivane",
    taluka: "Karveer",
    lat: 16.7201,
    lng: 74.2439,
    milkOutput: 420,
    healthStatus: "Good",
    healthIndex: 94.5,
    doctorAssigned: true,
    doctorName: "Dr. Patil",
    feedStatus: "Sufficient",
    farmers: 45,
    animals: 180,
    efficiency: 98.5,
    lastVisit: "2024-01-15",
    performance: "excellent"
  },
  {
    id: 2,
    name: "Vadgaon DCS",
    village: "Vadgaon",
    taluka: "Hatkanangle",
    lat: 16.8465,
    lng: 74.4398,
    milkOutput: 280,
    healthStatus: "Needs Attention",
    healthIndex: 78.2,
    doctorAssigned: false,
    doctorName: null,
    feedStatus: "Low",
    farmers: 32,
    animals: 128,
    efficiency: 85.3,
    lastVisit: "2024-01-10",
    performance: "needs_attention"
  },
  {
    id: 3,
    name: "Ichalkaranji DCS",
    village: "Ichalkaranji",
    taluka: "Shirol",
    lat: 16.7000,
    lng: 74.4700,
    milkOutput: 630,
    healthStatus: "Good",
    healthIndex: 96.8,
    doctorAssigned: true,
    doctorName: "Dr. Sharma",
    feedStatus: "Sufficient",
    farmers: 67,
    animals: 245,
    efficiency: 99.2,
    lastVisit: "2024-01-16",
    performance: "excellent"
  },
  {
    id: 4,
    name: "Rankala DCS",
    village: "Rankala",
    taluka: "Kolhapur",
    lat: 16.7050,
    lng: 74.2400,
    milkOutput: 520,
    healthStatus: "Good",
    healthIndex: 92.1,
    doctorAssigned: true,
    doctorName: "Dr. Kulkarni",
    feedStatus: "Sufficient",
    farmers: 58,
    animals: 210,
    efficiency: 97.8,
    lastVisit: "2024-01-14",
    performance: "good"
  },
  {
    id: 5,
    name: "Panhala DCS",
    village: "Panhala Fort",
    taluka: "Panhala",
    lat: 16.8100,
    lng: 74.1100,
    milkOutput: 340,
    healthStatus: "Critical",
    healthIndex: 68.5,
    doctorAssigned: false,
    doctorName: null,
    feedStatus: "Critical",
    farmers: 28,
    animals: 95,
    efficiency: 76.2,
    lastVisit: "2024-01-08",
    performance: "critical"
  }
];

const gokulUnit = {
  name: "Gokul Dairy Unit",
  lat: 16.6956,
  lng: 74.2316,
  totalProduction: 2190, // Sum of all DCS
  totalDCS: dcsMapData.length,
  totalFarmers: dcsMapData.reduce((sum, dcs) => sum + dcs.farmers, 0),
  totalAnimals: dcsMapData.reduce((sum, dcs) => sum + dcs.animals, 0),
  avgHealthIndex: (dcsMapData.reduce((sum, dcs) => sum + dcs.healthIndex, 0) / dcsMapData.length).toFixed(1)
};

interface DcsMapProps {
  selectedTaluka?: string | null;
  onDcsSelect?: (dcs: any) => void;
}

const DcsMap: React.FC<DcsMapProps> = ({ selectedTaluka, onDcsSelect }) => {
  const [selectedDcs, setSelectedDcs] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'map' | 'list'>('list');

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'needs_attention': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent': return <CheckCircle className="w-4 h-4" />;
      case 'good': return <Activity className="w-4 h-4" />;
      case 'needs_attention': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const filteredDcsData = selectedTaluka 
    ? dcsMapData.filter(dcs => dcs.taluka === selectedTaluka)
    : dcsMapData;

  const handleDcsClick = (dcs: any) => {
    setSelectedDcs(dcs);
    if (onDcsSelect) {
      onDcsSelect(dcs);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold text-gray-800">DCS Map Visualization</h3>
          <p className="text-gray-600">Interactive map showing dairy cooperative societies across Kolhapur district</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            onClick={() => setViewMode('list')}
            size="sm"
          >
            <Building2 className="w-4 h-4 mr-2" />
            List View
          </Button>
          <Button
            variant={viewMode === 'map' ? 'default' : 'outline'}
            onClick={() => setViewMode('map')}
            size="sm"
          >
            <MapPin className="w-4 h-4 mr-2" />
            Map View
          </Button>
        </div>
      </div>

      {/* Gokul Unit Summary */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-blue-600" />
            {gokulUnit.name} - Central Processing Hub
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-blue-600">{gokulUnit.totalDCS}</div>
              <div className="text-sm text-gray-600">DCS Centers</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-green-600">{gokulUnit.totalProduction}L</div>
              <div className="text-sm text-gray-600">Daily Production</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-purple-600">{gokulUnit.totalFarmers}</div>
              <div className="text-sm text-gray-600">Total Farmers</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-orange-600">{gokulUnit.totalAnimals}</div>
              <div className="text-sm text-gray-600">Total Animals</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-red-600">{gokulUnit.avgHealthIndex}%</div>
              <div className="text-sm text-gray-600">Avg Health Index</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {viewMode === 'map' ? (
        /* Interactive Map View */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Map className="w-5 h-5 text-blue-600" />
              Interactive DCS Location Map
            </CardTitle>
          </CardHeader>
          <CardContent>
            <InteractiveMapView
              dcsData={filteredDcsData}
              selectedDcs={selectedDcs}
              onDcsSelect={handleDcsClick}
            />
          </CardContent>
        </Card>
      ) : (
        /* List View */
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredDcsData.map((dcs) => (
            <Card 
              key={dcs.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg border-2 ${
                selectedDcs?.id === dcs.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}
              onClick={() => handleDcsClick(dcs)}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Droplets className="h-5 w-5 text-blue-600" />
                    {dcs.name}
                  </span>
                  <Badge className={`${getPerformanceColor(dcs.performance)} border text-xs`}>
                    {getPerformanceIcon(dcs.performance)}
                    <span className="ml-1 capitalize">{dcs.performance.replace('_', ' ')}</span>
                  </Badge>
                </CardTitle>
                <div className="text-sm text-gray-600">
                  {dcs.village}, {dcs.taluka} Taluka
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Production:</span>
                      <span className="font-medium text-green-600">{dcs.milkOutput}L</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Efficiency:</span>
                      <span className="font-medium">{dcs.efficiency}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Farmers:</span>
                      <span className="font-medium">{dcs.farmers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Animals:</span>
                      <span className="font-medium">{dcs.animals}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Health Index:</span>
                      <span className={`font-medium ${
                        dcs.healthIndex >= 90 ? 'text-green-600' : 
                        dcs.healthIndex >= 80 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {dcs.healthIndex}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          dcs.healthIndex >= 90 ? 'bg-green-600' : 
                          dcs.healthIndex >= 80 ? 'bg-yellow-600' : 'bg-red-600'
                        }`}
                        style={{ width: `${dcs.healthIndex}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-1">
                      <Stethoscope className="w-3 h-3 text-purple-500" />
                      <span className={dcs.doctorAssigned ? 'text-green-600' : 'text-red-600'}>
                        {dcs.doctorAssigned ? dcs.doctorName : 'No Doctor'}
                      </span>
                    </div>
                    <div className="text-gray-500">
                      Last Visit: {dcs.lastVisit}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <Badge variant="outline" className={`text-xs ${
                      dcs.feedStatus === 'Sufficient' ? 'border-green-200 text-green-700' :
                      dcs.feedStatus === 'Low' ? 'border-yellow-200 text-yellow-700' :
                      'border-red-200 text-red-700'
                    }`}>
                      Feed: {dcs.feedStatus}
                    </Badge>
                    <div className="text-gray-500">
                      📍 {dcs.lat.toFixed(4)}, {dcs.lng.toFixed(4)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Selected DCS Details */}
      {selectedDcs && (
        <Card className="border-2 border-purple-200 bg-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <MapPin className="h-6 w-6 text-purple-600" />
              {selectedDcs.name} - Detailed Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-800">Location Details</h4>
                <div className="text-sm space-y-1">
                  <div>Village: {selectedDcs.village}</div>
                  <div>Taluka: {selectedDcs.taluka}</div>
                  <div>Coordinates: {selectedDcs.lat}, {selectedDcs.lng}</div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-800">Production Metrics</h4>
                <div className="text-sm space-y-1">
                  <div>Daily Output: {selectedDcs.milkOutput}L</div>
                  <div>Efficiency: {selectedDcs.efficiency}%</div>
                  <div>Performance: {selectedDcs.performance}</div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-800">Health & Care</h4>
                <div className="text-sm space-y-1">
                  <div>Health Status: {selectedDcs.healthStatus}</div>
                  <div>Health Index: {selectedDcs.healthIndex}%</div>
                  <div>Doctor: {selectedDcs.doctorAssigned ? selectedDcs.doctorName : 'Not Assigned'}</div>
                  <div>Feed Status: {selectedDcs.feedStatus}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DcsMap;

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet';
import { Icon, DivIcon } from 'leaflet';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  MapPin,
  Building2,
  Users,
  Milk,
  Heart,
  Stethoscope,
  AlertTriangle,
  CheckCircle,
  Activity,
  Droplets,
  Map,
  List
} from "lucide-react";
import '../../styles/leaflet.css';

// Create custom markers for different performance levels
const createCustomIcon = (performance: string, isGokul = false) => {
  const size = isGokul ? 32 : 24;
  const colorClass = isGokul ? 'gokul-unit' : performance;

  return new DivIcon({
    html: `<div class="custom-marker ${colorClass}" style="width: ${size}px; height: ${size}px;">
             ${isGokul ? '<div style="color: white; font-size: 16px; text-align: center; line-height: 24px;">🏭</div>' :
                        '<div style="color: white; font-size: 12px; text-align: center; line-height: 18px;">🥛</div>'}
           </div>`,
    className: 'custom-div-icon',
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
  });
};

// Real Leaflet Map Component
const LeafletMapView = ({ dcsData, selectedDcs, onDcsSelect }: any) => {
  const [map, setMap] = useState<any>(null);

  // Center of Kolhapur district
  const center: [number, number] = [16.705, 74.243];
  const gokulUnit = { lat: 16.6956, lng: 74.2316, name: "Gokul Dairy Unit" };

  useEffect(() => {
    if (map && selectedDcs) {
      map.setView([selectedDcs.lat, selectedDcs.lng], 12);
    }
  }, [map, selectedDcs]);

  return (
    <div className="h-96 w-full rounded-lg overflow-hidden border-2 border-blue-200">
      <MapContainer
        center={center}
        zoom={10}
        style={{ height: '100%', width: '100%' }}
        ref={setMap}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />

        {/* Gokul Unit Marker */}
        <Marker
          position={[gokulUnit.lat, gokulUnit.lng]}
          icon={createCustomIcon('excellent', true)}
        >
          <Popup>
            <div className="p-2">
              <h3 className="font-bold text-lg text-blue-600 mb-2">🏭 {gokulUnit.name}</h3>
              <div className="space-y-1 text-sm">
                <div><strong>Central Processing Hub</strong></div>
                <div>📍 {gokulUnit.lat.toFixed(4)}, {gokulUnit.lng.toFixed(4)}</div>
                <div>🥛 Total Daily Collection: {dcsData.reduce((sum: number, dcs: any) => sum + dcs.milkSupply, 0)}L</div>
                <div>🏢 Connected DCS: {dcsData.length}</div>
              </div>
            </div>
          </Popup>
        </Marker>

        {/* DCS Markers */}
        {dcsData.map((dcs: any) => (
          <Marker
            key={dcs.id}
            position={[dcs.lat, dcs.lng]}
            icon={createCustomIcon(dcs.performance)}
            eventHandlers={{
              click: () => onDcsSelect(dcs),
            }}
          >
            <Popup>
              <div className="p-3 min-w-64">
                <h3 className="font-bold text-lg text-blue-600 mb-2">🥛 {dcs.name}</h3>

                <div className="grid grid-cols-2 gap-3 text-sm mb-3">
                  <div>
                    <div className="text-gray-600">Village:</div>
                    <div className="font-medium">{dcs.village}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Taluka:</div>
                    <div className="font-medium">{dcs.taluka}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Milk Supply:</div>
                    <div className="font-medium text-green-600">{dcs.milkSupply}L</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Efficiency:</div>
                    <div className="font-medium">{dcs.efficiency}%</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Farmers:</div>
                    <div className="font-medium">{dcs.farmers}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Animal Count:</div>
                    <div className="font-medium">{dcs.animalCount}</div>
                  </div>
                </div>

                <div className="border-t pt-2 space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Health Index:</span>
                    <span className={`font-medium ${
                      dcs.healthIndex >= 90 ? 'text-green-600' :
                      dcs.healthIndex >= 80 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {dcs.healthIndex}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Doctor:</span>
                    <span className={dcs.doctorAssigned ? 'text-green-600' : 'text-red-600'}>
                      {dcs.doctorAssigned ? dcs.doctorName : 'Not Assigned'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Feed Status:</span>
                    <span className={`font-medium ${
                      dcs.feedStatus === 'Sufficient' ? 'text-green-600' :
                      dcs.feedStatus === 'Low' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {dcs.feedStatus}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Visit:</span>
                    <span className="text-gray-600">{dcs.lastVisit}</span>
                  </div>
                </div>

                <div className="mt-2 pt-2 border-t text-xs text-gray-500">
                  📍 {dcs.lat.toFixed(4)}, {dcs.lng.toFixed(4)}
                </div>
              </div>
            </Popup>
          </Marker>
        ))}

        {/* Connection lines from DCS to Gokul Unit */}
        {dcsData.map((dcs: any) => (
          <Polyline
            key={`line-${dcs.id}`}
            positions={[
              [dcs.lat, dcs.lng],
              [gokulUnit.lat, gokulUnit.lng]
            ]}
            color={selectedDcs?.id === dcs.id ? '#3B82F6' : '#94A3B8'}
            weight={selectedDcs?.id === dcs.id ? 3 : 1}
            opacity={selectedDcs?.id === dcs.id ? 0.8 : 0.4}
            dashArray={selectedDcs?.id === dcs.id ? undefined : '5, 10'}
          />
        ))}
      </MapContainer>
    </div>
  );
};

// Comprehensive DCS data with 25 centers across Kolhapur district
const dcsMapData = [
  {
    id: 1,
    name: "Shivane DCS",
    village: "Shivane",
    taluka: "Karveer",
    lat: 16.7201,
    lng: 74.2439,
    milkSupply: 420, // Liters per day
    animalCount: 180,
    healthStatus: "Good",
    healthIndex: 94.5,
    doctorAssigned: true,
    doctorName: "Dr. Patil",
    feedStatus: "Sufficient",
    farmers: 45,
    efficiency: 98.5,
    lastVisit: "2024-01-15",
    performance: "excellent",
    fatContent: 4.2,
    proteinContent: 3.8,
    establishedYear: 2018,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Borewell",
    transportFacility: "Available"
  },
  {
    id: 2,
    name: "Vadgaon DCS",
    village: "Vadgaon",
    taluka: "Hatkanangle",
    lat: 16.8465,
    lng: 74.4398,
    milkSupply: 280,
    animalCount: 128,
    healthStatus: "Needs Attention",
    healthIndex: 78.2,
    doctorAssigned: false,
    doctorName: null,
    feedStatus: "Low",
    farmers: 32,
    efficiency: 85.3,
    lastVisit: "2024-01-10",
    performance: "needs_attention",
    fatContent: 3.9,
    proteinContent: 3.5,
    establishedYear: 2015,
    infrastructure: "Basic",
    coldStorage: false,
    powerBackup: true,
    waterSource: "Municipal",
    transportFacility: "Limited"
  },
  {
    id: 3,
    name: "Ichalkaranji DCS",
    village: "Ichalkaranji",
    taluka: "Shirol",
    lat: 16.7000,
    lng: 74.4700,
    milkSupply: 630,
    animalCount: 245,
    healthStatus: "Excellent",
    healthIndex: 96.8,
    doctorAssigned: true,
    doctorName: "Dr. Sharma",
    feedStatus: "Sufficient",
    farmers: 67,
    efficiency: 99.2,
    lastVisit: "2024-01-16",
    performance: "excellent",
    fatContent: 4.5,
    proteinContent: 4.1,
    establishedYear: 2020,
    infrastructure: "Advanced",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Borewell",
    transportFacility: "Available"
  },
  {
    id: 4,
    name: "Rankala DCS",
    village: "Rankala",
    taluka: "Kolhapur",
    lat: 16.7050,
    lng: 74.2400,
    milkSupply: 520,
    animalCount: 210,
    healthStatus: "Good",
    healthIndex: 92.1,
    doctorAssigned: true,
    doctorName: "Dr. Kulkarni",
    feedStatus: "Sufficient",
    farmers: 58,
    efficiency: 97.8,
    lastVisit: "2024-01-14",
    performance: "good",
    fatContent: 4.1,
    proteinContent: 3.7,
    establishedYear: 2017,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Municipal",
    transportFacility: "Available"
  },
  {
    id: 5,
    name: "Panhala DCS",
    village: "Panhala Fort",
    taluka: "Panhala",
    lat: 16.8100,
    lng: 74.1100,
    milkSupply: 340,
    animalCount: 95,
    healthStatus: "Critical",
    healthIndex: 68.5,
    doctorAssigned: false,
    doctorName: null,
    feedStatus: "Critical",
    farmers: 28,
    efficiency: 76.2,
    lastVisit: "2024-01-08",
    performance: "critical",
    fatContent: 3.6,
    proteinContent: 3.2,
    establishedYear: 2012,
    infrastructure: "Basic",
    coldStorage: false,
    powerBackup: false,
    waterSource: "Well",
    transportFacility: "Limited"
  },
  {
    id: 6,
    name: "Kagal DCS",
    village: "Kagal",
    taluka: "Kagal",
    lat: 16.5800,
    lng: 74.3200,
    milkSupply: 380,
    animalCount: 165,
    healthStatus: "Good",
    healthIndex: 89.3,
    doctorAssigned: true,
    doctorName: "Dr. Desai",
    feedStatus: "Sufficient",
    farmers: 42,
    efficiency: 94.7,
    lastVisit: "2024-01-13",
    performance: "good",
    fatContent: 4.0,
    proteinContent: 3.6,
    establishedYear: 2016,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Borewell",
    transportFacility: "Available"
  },
  {
    id: 7,
    name: "Ajra DCS",
    village: "Ajra",
    taluka: "Ajra",
    lat: 16.1200,
    lng: 74.2100,
    milkSupply: 290,
    animalCount: 140,
    healthStatus: "Needs Attention",
    healthIndex: 82.1,
    doctorAssigned: true,
    doctorName: "Dr. Joshi",
    feedStatus: "Low",
    farmers: 35,
    efficiency: 88.4,
    lastVisit: "2024-01-12",
    performance: "needs_attention",
    fatContent: 3.8,
    proteinContent: 3.4,
    establishedYear: 2014,
    infrastructure: "Basic",
    coldStorage: false,
    powerBackup: true,
    waterSource: "Municipal",
    transportFacility: "Limited"
  },
  {
    id: 8,
    name: "Radhanagari DCS",
    village: "Radhanagari",
    taluka: "Radhanagari",
    lat: 16.4100,
    lng: 74.0200,
    milkSupply: 450,
    animalCount: 195,
    healthStatus: "Good",
    healthIndex: 91.7,
    doctorAssigned: true,
    doctorName: "Dr. Pawar",
    feedStatus: "Sufficient",
    farmers: 52,
    efficiency: 96.3,
    lastVisit: "2024-01-15",
    performance: "excellent",
    fatContent: 4.3,
    proteinContent: 3.9,
    establishedYear: 2019,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "River",
    transportFacility: "Available"
  },
  {
    id: 9,
    name: "Bhudargad DCS",
    village: "Bhudargad",
    taluka: "Bhudargad",
    lat: 16.0500,
    lng: 74.1800,
    milkSupply: 320,
    animalCount: 155,
    healthStatus: "Good",
    healthIndex: 87.4,
    doctorAssigned: true,
    doctorName: "Dr. Kale",
    feedStatus: "Sufficient",
    farmers: 38,
    efficiency: 92.1,
    lastVisit: "2024-01-11",
    performance: "good",
    fatContent: 3.9,
    proteinContent: 3.5,
    establishedYear: 2016,
    infrastructure: "Basic",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Borewell",
    transportFacility: "Available"
  },
  {
    id: 10,
    name: "Chandgad DCS",
    village: "Chandgad",
    taluka: "Chandgad",
    lat: 16.2300,
    lng: 74.3800,
    milkSupply: 365,
    animalCount: 172,
    healthStatus: "Good",
    healthIndex: 88.9,
    doctorAssigned: true,
    doctorName: "Dr. Mane",
    feedStatus: "Sufficient",
    farmers: 44,
    efficiency: 93.7,
    lastVisit: "2024-01-14",
    performance: "good",
    fatContent: 4.1,
    proteinContent: 3.7,
    establishedYear: 2017,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Municipal",
    transportFacility: "Available"
  },
  {
    id: 11,
    name: "Gadhinglaj DCS",
    village: "Gadhinglaj",
    taluka: "Gadhinglaj",
    lat: 16.2200,
    lng: 74.3400,
    milkSupply: 480,
    animalCount: 220,
    healthStatus: "Excellent",
    healthIndex: 95.2,
    doctorAssigned: true,
    doctorName: "Dr. Sawant",
    feedStatus: "Excellent",
    farmers: 62,
    efficiency: 98.1,
    lastVisit: "2024-01-16",
    performance: "excellent",
    fatContent: 4.4,
    proteinContent: 4.0,
    establishedYear: 2020,
    infrastructure: "Advanced",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Borewell",
    transportFacility: "Available"
  },
  {
    id: 12,
    name: "Shirol DCS",
    village: "Shirol",
    taluka: "Shirol",
    lat: 16.7200,
    lng: 74.4200,
    milkSupply: 410,
    animalCount: 185,
    healthStatus: "Good",
    healthIndex: 90.6,
    doctorAssigned: true,
    doctorName: "Dr. Jadhav",
    feedStatus: "Sufficient",
    farmers: 48,
    efficiency: 95.4,
    lastVisit: "2024-01-13",
    performance: "good",
    fatContent: 4.0,
    proteinContent: 3.6,
    establishedYear: 2018,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Municipal",
    transportFacility: "Available"
  },
  {
    id: 13,
    name: "Karveer DCS",
    village: "Karveer",
    taluka: "Karveer",
    lat: 16.6800,
    lng: 74.2100,
    milkSupply: 395,
    animalCount: 178,
    healthStatus: "Good",
    healthIndex: 89.8,
    doctorAssigned: true,
    doctorName: "Dr. Bhosale",
    feedStatus: "Sufficient",
    farmers: 46,
    efficiency: 94.2,
    lastVisit: "2024-01-12",
    performance: "good",
    fatContent: 3.9,
    proteinContent: 3.5,
    establishedYear: 2015,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Borewell",
    transportFacility: "Available"
  },
  {
    id: 14,
    name: "Hatkanangle DCS",
    village: "Hatkanangle",
    taluka: "Hatkanangle",
    lat: 16.8200,
    lng: 74.4500,
    milkSupply: 355,
    animalCount: 162,
    healthStatus: "Needs Attention",
    healthIndex: 81.3,
    doctorAssigned: true,
    doctorName: "Dr. Gaikwad",
    feedStatus: "Low",
    farmers: 39,
    efficiency: 87.9,
    lastVisit: "2024-01-09",
    performance: "needs_attention",
    fatContent: 3.7,
    proteinContent: 3.3,
    establishedYear: 2013,
    infrastructure: "Basic",
    coldStorage: false,
    powerBackup: true,
    waterSource: "Municipal",
    transportFacility: "Limited"
  },
  {
    id: 15,
    name: "Bavda DCS",
    village: "Bavda",
    taluka: "Shirol",
    lat: 16.6500,
    lng: 74.4800,
    milkSupply: 275,
    animalCount: 125,
    healthStatus: "Good",
    healthIndex: 86.7,
    doctorAssigned: true,
    doctorName: "Dr. Shinde",
    feedStatus: "Sufficient",
    farmers: 33,
    efficiency: 91.5,
    lastVisit: "2024-01-11",
    performance: "good",
    fatContent: 3.8,
    proteinContent: 3.4,
    establishedYear: 2016,
    infrastructure: "Basic",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Well",
    transportFacility: "Available"
  },
  {
    id: 16,
    name: "Kurundwad DCS",
    village: "Kurundwad",
    taluka: "Shirol",
    lat: 16.6800,
    lng: 74.5900,
    milkSupply: 425,
    animalCount: 192,
    healthStatus: "Excellent",
    healthIndex: 93.4,
    doctorAssigned: true,
    doctorName: "Dr. Patil",
    feedStatus: "Excellent",
    farmers: 51,
    efficiency: 96.8,
    lastVisit: "2024-01-15",
    performance: "excellent",
    fatContent: 4.2,
    proteinContent: 3.8,
    establishedYear: 2019,
    infrastructure: "Advanced",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Borewell",
    transportFacility: "Available"
  },
  {
    id: 17,
    name: "Malkapur DCS",
    village: "Malkapur",
    taluka: "Karveer",
    lat: 16.7500,
    lng: 74.1800,
    milkSupply: 310,
    animalCount: 148,
    healthStatus: "Good",
    healthIndex: 88.2,
    doctorAssigned: true,
    doctorName: "Dr. Kamble",
    feedStatus: "Sufficient",
    farmers: 37,
    efficiency: 92.7,
    lastVisit: "2024-01-13",
    performance: "good",
    fatContent: 3.9,
    proteinContent: 3.5,
    establishedYear: 2017,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Municipal",
    transportFacility: "Available"
  },
  {
    id: 18,
    name: "Nesari DCS",
    village: "Nesari",
    taluka: "Ajra",
    lat: 16.1800,
    lng: 74.2800,
    milkSupply: 265,
    animalCount: 118,
    healthStatus: "Needs Attention",
    healthIndex: 79.6,
    doctorAssigned: false,
    doctorName: null,
    feedStatus: "Low",
    farmers: 29,
    efficiency: 84.3,
    lastVisit: "2024-01-07",
    performance: "needs_attention",
    fatContent: 3.6,
    proteinContent: 3.2,
    establishedYear: 2014,
    infrastructure: "Basic",
    coldStorage: false,
    powerBackup: false,
    waterSource: "Well",
    transportFacility: "Limited"
  },
  {
    id: 19,
    name: "Shahuwadi DCS",
    village: "Shahuwadi",
    taluka: "Shahuwadi",
    lat: 16.3500,
    lng: 73.9500,
    milkSupply: 385,
    animalCount: 175,
    healthStatus: "Good",
    healthIndex: 90.1,
    doctorAssigned: true,
    doctorName: "Dr. More",
    feedStatus: "Sufficient",
    farmers: 45,
    efficiency: 94.6,
    lastVisit: "2024-01-14",
    performance: "good",
    fatContent: 4.0,
    proteinContent: 3.6,
    establishedYear: 2018,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "River",
    transportFacility: "Available"
  },
  {
    id: 20,
    name: "Peth Vadgaon DCS",
    village: "Peth Vadgaon",
    taluka: "Hatkanangle",
    lat: 16.8800,
    lng: 74.4100,
    milkSupply: 340,
    animalCount: 158,
    healthStatus: "Good",
    healthIndex: 87.9,
    doctorAssigned: true,
    doctorName: "Dr. Kulkarni",
    feedStatus: "Sufficient",
    farmers: 40,
    efficiency: 91.8,
    lastVisit: "2024-01-12",
    performance: "good",
    fatContent: 3.8,
    proteinContent: 3.4,
    establishedYear: 2016,
    infrastructure: "Basic",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Borewell",
    transportFacility: "Available"
  },
  {
    id: 21,
    name: "Gaganbawda DCS",
    village: "Gaganbawda",
    taluka: "Gaganbawda",
    lat: 16.5500,
    lng: 73.8500,
    milkSupply: 295,
    animalCount: 135,
    healthStatus: "Needs Attention",
    healthIndex: 83.4,
    doctorAssigned: true,
    doctorName: "Dr. Deshmukh",
    feedStatus: "Low",
    farmers: 34,
    efficiency: 89.2,
    lastVisit: "2024-01-10",
    performance: "needs_attention",
    fatContent: 3.7,
    proteinContent: 3.3,
    establishedYear: 2015,
    infrastructure: "Basic",
    coldStorage: false,
    powerBackup: true,
    waterSource: "Municipal",
    transportFacility: "Limited"
  },
  {
    id: 22,
    name: "Uchgaon DCS",
    village: "Uchgaon",
    taluka: "Kolhapur",
    lat: 16.7300,
    lng: 74.2800,
    milkSupply: 465,
    animalCount: 205,
    healthStatus: "Excellent",
    healthIndex: 94.7,
    doctorAssigned: true,
    doctorName: "Dr. Thorat",
    feedStatus: "Excellent",
    farmers: 56,
    efficiency: 97.3,
    lastVisit: "2024-01-16",
    performance: "excellent",
    fatContent: 4.3,
    proteinContent: 3.9,
    establishedYear: 2020,
    infrastructure: "Advanced",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Municipal",
    transportFacility: "Available"
  },
  {
    id: 23,
    name: "Jaysingpur DCS",
    village: "Jaysingpur",
    taluka: "Shirol",
    lat: 16.7800,
    lng: 74.5600,
    milkSupply: 510,
    animalCount: 230,
    healthStatus: "Excellent",
    healthIndex: 96.1,
    doctorAssigned: true,
    doctorName: "Dr. Pawar",
    feedStatus: "Excellent",
    farmers: 64,
    efficiency: 98.7,
    lastVisit: "2024-01-16",
    performance: "excellent",
    fatContent: 4.4,
    proteinContent: 4.0,
    establishedYear: 2021,
    infrastructure: "Advanced",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Borewell",
    transportFacility: "Available"
  },
  {
    id: 24,
    name: "Kini DCS",
    village: "Kini",
    taluka: "Panhala",
    lat: 16.8500,
    lng: 74.0800,
    milkSupply: 325,
    animalCount: 152,
    healthStatus: "Good",
    healthIndex: 89.5,
    doctorAssigned: true,
    doctorName: "Dr. Salunkhe",
    feedStatus: "Sufficient",
    farmers: 38,
    efficiency: 93.1,
    lastVisit: "2024-01-13",
    performance: "good",
    fatContent: 3.9,
    proteinContent: 3.5,
    establishedYear: 2017,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "Well",
    transportFacility: "Available"
  },
  {
    id: 25,
    name: "Vishalgad DCS",
    village: "Vishalgad",
    taluka: "Shahuwadi",
    lat: 16.3200,
    lng: 74.0200,
    milkSupply: 375,
    animalCount: 168,
    healthStatus: "Good",
    healthIndex: 91.3,
    doctorAssigned: true,
    doctorName: "Dr. Chavan",
    feedStatus: "Sufficient",
    farmers: 43,
    efficiency: 95.2,
    lastVisit: "2024-01-15",
    performance: "excellent",
    fatContent: 4.1,
    proteinContent: 3.7,
    establishedYear: 2019,
    infrastructure: "Modern",
    coldStorage: true,
    powerBackup: true,
    waterSource: "River",
    transportFacility: "Available"
  }
];

const gokulUnit = {
  name: "Gokul Dairy Unit",
  lat: 16.6956,
  lng: 74.2316,
  totalProduction: dcsMapData.reduce((sum, dcs) => sum + dcs.milkSupply, 0), // Sum of all DCS milk supply
  totalDCS: dcsMapData.length,
  totalFarmers: dcsMapData.reduce((sum, dcs) => sum + dcs.farmers, 0),
  totalAnimals: dcsMapData.reduce((sum, dcs) => sum + dcs.animalCount, 0),
  avgHealthIndex: (dcsMapData.reduce((sum, dcs) => sum + dcs.healthIndex, 0) / dcsMapData.length).toFixed(1)
};

interface DcsMapProps {
  selectedTaluka?: string | null;
  onDcsSelect?: (dcs: any) => void;
}

const DcsMap: React.FC<DcsMapProps> = ({ selectedTaluka, onDcsSelect }) => {
  const [selectedDcs, setSelectedDcs] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'map' | 'list'>('list');

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'needs_attention': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent': return <CheckCircle className="w-4 h-4" />;
      case 'good': return <Activity className="w-4 h-4" />;
      case 'needs_attention': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const filteredDcsData = selectedTaluka 
    ? dcsMapData.filter(dcs => dcs.taluka === selectedTaluka)
    : dcsMapData;

  const handleDcsClick = (dcs: any) => {
    setSelectedDcs(dcs);
    if (onDcsSelect) {
      onDcsSelect(dcs);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold text-gray-800">DCS Map Visualization</h3>
          <p className="text-gray-600">Interactive map showing dairy cooperative societies across Kolhapur district</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            onClick={() => setViewMode('list')}
            size="sm"
          >
            <Building2 className="w-4 h-4 mr-2" />
            List View
          </Button>
          <Button
            variant={viewMode === 'map' ? 'default' : 'outline'}
            onClick={() => setViewMode('map')}
            size="sm"
          >
            <MapPin className="w-4 h-4 mr-2" />
            Map View
          </Button>
        </div>
      </div>

      {/* Gokul Unit Summary */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-blue-600" />
            {gokulUnit.name} - Central Processing Hub
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-blue-600">{gokulUnit.totalDCS}</div>
              <div className="text-sm text-gray-600">DCS Centers</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-green-600">{gokulUnit.totalProduction}L</div>
              <div className="text-sm text-gray-600">Daily Production</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-purple-600">{gokulUnit.totalFarmers}</div>
              <div className="text-sm text-gray-600">Total Farmers</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-orange-600">{gokulUnit.totalAnimals}</div>
              <div className="text-sm text-gray-600">Total Animals</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-red-600">{gokulUnit.avgHealthIndex}%</div>
              <div className="text-sm text-gray-600">Avg Health Index</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {viewMode === 'map' ? (
        /* Interactive Map View */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Map className="w-5 h-5 text-blue-600" />
              Interactive DCS Location Map
            </CardTitle>
          </CardHeader>
          <CardContent>
            <LeafletMapView
              dcsData={filteredDcsData}
              selectedDcs={selectedDcs}
              onDcsSelect={handleDcsClick}
            />
          </CardContent>
        </Card>
      ) : (
        /* List View */
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredDcsData.map((dcs) => (
            <Card 
              key={dcs.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg border-2 ${
                selectedDcs?.id === dcs.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}
              onClick={() => handleDcsClick(dcs)}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Droplets className="h-5 w-5 text-blue-600" />
                    {dcs.name}
                  </span>
                  <Badge className={`${getPerformanceColor(dcs.performance)} border text-xs`}>
                    {getPerformanceIcon(dcs.performance)}
                    <span className="ml-1 capitalize">{dcs.performance.replace('_', ' ')}</span>
                  </Badge>
                </CardTitle>
                <div className="text-sm text-gray-600">
                  {dcs.village}, {dcs.taluka} Taluka
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Milk Supply:</span>
                      <span className="font-medium text-green-600">{dcs.milkSupply}L</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Efficiency:</span>
                      <span className="font-medium">{dcs.efficiency}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Farmers:</span>
                      <span className="font-medium">{dcs.farmers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Animals:</span>
                      <span className="font-medium">{dcs.animalCount}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Health Index:</span>
                      <span className={`font-medium ${
                        dcs.healthIndex >= 90 ? 'text-green-600' : 
                        dcs.healthIndex >= 80 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {dcs.healthIndex}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          dcs.healthIndex >= 90 ? 'bg-green-600' : 
                          dcs.healthIndex >= 80 ? 'bg-yellow-600' : 'bg-red-600'
                        }`}
                        style={{ width: `${dcs.healthIndex}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-1">
                      <Stethoscope className="w-3 h-3 text-purple-500" />
                      <span className={dcs.doctorAssigned ? 'text-green-600' : 'text-red-600'}>
                        {dcs.doctorAssigned ? dcs.doctorName : 'No Doctor'}
                      </span>
                    </div>
                    <div className="text-gray-500">
                      Last Visit: {dcs.lastVisit}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <Badge variant="outline" className={`text-xs ${
                      dcs.feedStatus === 'Sufficient' ? 'border-green-200 text-green-700' :
                      dcs.feedStatus === 'Low' ? 'border-yellow-200 text-yellow-700' :
                      'border-red-200 text-red-700'
                    }`}>
                      Feed: {dcs.feedStatus}
                    </Badge>
                    <div className="text-gray-500">
                      📍 {dcs.lat.toFixed(4)}, {dcs.lng.toFixed(4)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Selected DCS Details */}
      {selectedDcs && (
        <Card className="border-2 border-purple-200 bg-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <MapPin className="h-6 w-6 text-purple-600" />
              {selectedDcs.name} - Detailed Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-800">Location Details</h4>
                <div className="text-sm space-y-1">
                  <div>Village: {selectedDcs.village}</div>
                  <div>Taluka: {selectedDcs.taluka}</div>
                  <div>Coordinates: {selectedDcs.lat}, {selectedDcs.lng}</div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-800">Production Metrics</h4>
                <div className="text-sm space-y-1">
                  <div>Daily Output: {selectedDcs.milkOutput}L</div>
                  <div>Efficiency: {selectedDcs.efficiency}%</div>
                  <div>Performance: {selectedDcs.performance}</div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-800">Health & Care</h4>
                <div className="text-sm space-y-1">
                  <div>Health Status: {selectedDcs.healthStatus}</div>
                  <div>Health Index: {selectedDcs.healthIndex}%</div>
                  <div>Doctor: {selectedDcs.doctorAssigned ? selectedDcs.doctorName : 'Not Assigned'}</div>
                  <div>Feed Status: {selectedDcs.feedStatus}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DcsMap;

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  MapPin, 
  Building2, 
  Users, 
  Milk, 
  Heart, 
  Stethoscope,
  AlertTriangle,
  CheckCircle,
  Activity,
  Droplets
} from "lucide-react";

// Enhanced dummy data with hierarchical structure
const dcsMapData = [
  {
    id: 1,
    name: "Shivane DCS",
    village: "Shivane",
    taluka: "Karveer",
    lat: 16.7201,
    lng: 74.2439,
    milkOutput: 420,
    healthStatus: "Good",
    healthIndex: 94.5,
    doctorAssigned: true,
    doctorName: "Dr. Patil",
    feedStatus: "Sufficient",
    farmers: 45,
    animals: 180,
    efficiency: 98.5,
    lastVisit: "2024-01-15",
    performance: "excellent"
  },
  {
    id: 2,
    name: "Vadgaon DCS",
    village: "Vadgaon",
    taluka: "Hatkanangle",
    lat: 16.8465,
    lng: 74.4398,
    milkOutput: 280,
    healthStatus: "Needs Attention",
    healthIndex: 78.2,
    doctorAssigned: false,
    doctorName: null,
    feedStatus: "Low",
    farmers: 32,
    animals: 128,
    efficiency: 85.3,
    lastVisit: "2024-01-10",
    performance: "needs_attention"
  },
  {
    id: 3,
    name: "Ichalkaranji DCS",
    village: "Ichalkaranji",
    taluka: "Shirol",
    lat: 16.7000,
    lng: 74.4700,
    milkOutput: 630,
    healthStatus: "Good",
    healthIndex: 96.8,
    doctorAssigned: true,
    doctorName: "Dr. Sharma",
    feedStatus: "Sufficient",
    farmers: 67,
    animals: 245,
    efficiency: 99.2,
    lastVisit: "2024-01-16",
    performance: "excellent"
  },
  {
    id: 4,
    name: "Rankala DCS",
    village: "Rankala",
    taluka: "Kolhapur",
    lat: 16.7050,
    lng: 74.2400,
    milkOutput: 520,
    healthStatus: "Good",
    healthIndex: 92.1,
    doctorAssigned: true,
    doctorName: "Dr. Kulkarni",
    feedStatus: "Sufficient",
    farmers: 58,
    animals: 210,
    efficiency: 97.8,
    lastVisit: "2024-01-14",
    performance: "good"
  },
  {
    id: 5,
    name: "Panhala DCS",
    village: "Panhala Fort",
    taluka: "Panhala",
    lat: 16.8100,
    lng: 74.1100,
    milkOutput: 340,
    healthStatus: "Critical",
    healthIndex: 68.5,
    doctorAssigned: false,
    doctorName: null,
    feedStatus: "Critical",
    farmers: 28,
    animals: 95,
    efficiency: 76.2,
    lastVisit: "2024-01-08",
    performance: "critical"
  }
];

const gokulUnit = {
  name: "Gokul Dairy Unit",
  lat: 16.6956,
  lng: 74.2316,
  totalProduction: 2190, // Sum of all DCS
  totalDCS: dcsMapData.length,
  totalFarmers: dcsMapData.reduce((sum, dcs) => sum + dcs.farmers, 0),
  totalAnimals: dcsMapData.reduce((sum, dcs) => sum + dcs.animals, 0),
  avgHealthIndex: (dcsMapData.reduce((sum, dcs) => sum + dcs.healthIndex, 0) / dcsMapData.length).toFixed(1)
};

interface DcsMapProps {
  selectedTaluka?: string | null;
  onDcsSelect?: (dcs: any) => void;
}

const DcsMap: React.FC<DcsMapProps> = ({ selectedTaluka, onDcsSelect }) => {
  const [selectedDcs, setSelectedDcs] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'map' | 'list'>('list');

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'needs_attention': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent': return <CheckCircle className="w-4 h-4" />;
      case 'good': return <Activity className="w-4 h-4" />;
      case 'needs_attention': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const filteredDcsData = selectedTaluka 
    ? dcsMapData.filter(dcs => dcs.taluka === selectedTaluka)
    : dcsMapData;

  const handleDcsClick = (dcs: any) => {
    setSelectedDcs(dcs);
    if (onDcsSelect) {
      onDcsSelect(dcs);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold text-gray-800">DCS Map Visualization</h3>
          <p className="text-gray-600">Interactive map showing dairy cooperative societies across Kolhapur district</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            onClick={() => setViewMode('list')}
            size="sm"
          >
            <Building2 className="w-4 h-4 mr-2" />
            List View
          </Button>
          <Button
            variant={viewMode === 'map' ? 'default' : 'outline'}
            onClick={() => setViewMode('map')}
            size="sm"
          >
            <MapPin className="w-4 h-4 mr-2" />
            Map View
          </Button>
        </div>
      </div>

      {/* Gokul Unit Summary */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-blue-600" />
            {gokulUnit.name} - Central Processing Hub
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-blue-600">{gokulUnit.totalDCS}</div>
              <div className="text-sm text-gray-600">DCS Centers</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-green-600">{gokulUnit.totalProduction}L</div>
              <div className="text-sm text-gray-600">Daily Production</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-purple-600">{gokulUnit.totalFarmers}</div>
              <div className="text-sm text-gray-600">Total Farmers</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-orange-600">{gokulUnit.totalAnimals}</div>
              <div className="text-sm text-gray-600">Total Animals</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-red-600">{gokulUnit.avgHealthIndex}%</div>
              <div className="text-sm text-gray-600">Avg Health Index</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {viewMode === 'map' ? (
        /* Map View Placeholder */
        <Card className="h-96">
          <CardContent className="h-full flex items-center justify-center">
            <div className="text-center">
              <MapPin className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">Google Maps Integration</h3>
              <p className="text-gray-500 mb-4">
                Interactive map showing DCS locations across Kolhapur district
              </p>
              <p className="text-sm text-gray-400">
                Note: Google Maps API integration required for full functionality
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* List View */
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredDcsData.map((dcs) => (
            <Card 
              key={dcs.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg border-2 ${
                selectedDcs?.id === dcs.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}
              onClick={() => handleDcsClick(dcs)}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Droplets className="h-5 w-5 text-blue-600" />
                    {dcs.name}
                  </span>
                  <Badge className={`${getPerformanceColor(dcs.performance)} border text-xs`}>
                    {getPerformanceIcon(dcs.performance)}
                    <span className="ml-1 capitalize">{dcs.performance.replace('_', ' ')}</span>
                  </Badge>
                </CardTitle>
                <div className="text-sm text-gray-600">
                  {dcs.village}, {dcs.taluka} Taluka
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Production:</span>
                      <span className="font-medium text-green-600">{dcs.milkOutput}L</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Efficiency:</span>
                      <span className="font-medium">{dcs.efficiency}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Farmers:</span>
                      <span className="font-medium">{dcs.farmers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Animals:</span>
                      <span className="font-medium">{dcs.animals}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Health Index:</span>
                      <span className={`font-medium ${
                        dcs.healthIndex >= 90 ? 'text-green-600' : 
                        dcs.healthIndex >= 80 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {dcs.healthIndex}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          dcs.healthIndex >= 90 ? 'bg-green-600' : 
                          dcs.healthIndex >= 80 ? 'bg-yellow-600' : 'bg-red-600'
                        }`}
                        style={{ width: `${dcs.healthIndex}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-1">
                      <Stethoscope className="w-3 h-3 text-purple-500" />
                      <span className={dcs.doctorAssigned ? 'text-green-600' : 'text-red-600'}>
                        {dcs.doctorAssigned ? dcs.doctorName : 'No Doctor'}
                      </span>
                    </div>
                    <div className="text-gray-500">
                      Last Visit: {dcs.lastVisit}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <Badge variant="outline" className={`text-xs ${
                      dcs.feedStatus === 'Sufficient' ? 'border-green-200 text-green-700' :
                      dcs.feedStatus === 'Low' ? 'border-yellow-200 text-yellow-700' :
                      'border-red-200 text-red-700'
                    }`}>
                      Feed: {dcs.feedStatus}
                    </Badge>
                    <div className="text-gray-500">
                      📍 {dcs.lat.toFixed(4)}, {dcs.lng.toFixed(4)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Selected DCS Details */}
      {selectedDcs && (
        <Card className="border-2 border-purple-200 bg-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <MapPin className="h-6 w-6 text-purple-600" />
              {selectedDcs.name} - Detailed Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-800">Location Details</h4>
                <div className="text-sm space-y-1">
                  <div>Village: {selectedDcs.village}</div>
                  <div>Taluka: {selectedDcs.taluka}</div>
                  <div>Coordinates: {selectedDcs.lat}, {selectedDcs.lng}</div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-800">Production Metrics</h4>
                <div className="text-sm space-y-1">
                  <div>Daily Output: {selectedDcs.milkOutput}L</div>
                  <div>Efficiency: {selectedDcs.efficiency}%</div>
                  <div>Performance: {selectedDcs.performance}</div>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-800">Health & Care</h4>
                <div className="text-sm space-y-1">
                  <div>Health Status: {selectedDcs.healthStatus}</div>
                  <div>Health Index: {selectedDcs.healthIndex}%</div>
                  <div>Doctor: {selectedDcs.doctorAssigned ? selectedDcs.doctorName : 'Not Assigned'}</div>
                  <div>Feed Status: {selectedDcs.feedStatus}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DcsMap;

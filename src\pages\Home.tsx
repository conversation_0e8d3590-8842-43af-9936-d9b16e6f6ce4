import { useEffect } from "react";
import About from "@/components/Home/About";
import Contact from "@/components/Home/Contact";
import Footer from "@/components/Home/Footer";
import Header from "@/components/Home/Header";
import Hero from "@/components/Home/Hero";
import Products from "@/components/Home/Products";
import Quality from "@/components/Home/Quality";

const Home = () => {
  // Smooth scroll behavior for navigation links
  useEffect(() => {
    const handleSmoothScroll = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.hash) {
        e.preventDefault();
        const element = document.querySelector(target.hash);
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }
    };

    // Add event listeners to navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
      link.addEventListener('click', handleSmoothScroll);
    });

    // Cleanup event listeners
    return () => {
      navLinks.forEach(link => {
        link.removeEventListener('click', handleSmoothScroll);
      });
    };
  }, []);

  return (
    <div className="min-h-screen">
      {/* Professional Header */}
      <Header />

      {/* Main Content Sections */}
      <main className="relative">
        {/* Hero Section - Full viewport height */}
        <section className="relative">
          <Hero />
        </section>

        {/* About Section */}
        <section className="relative bg-white">
          <About />
        </section>

        {/* Products/Solutions Section */}
        <section className="relative bg-gray-50">
          <Products />
        </section>

        {/* Quality & Analytics Section */}
        <section className="relative bg-white">
          <Quality />
        </section>

        {/* Contact Section */}
        <section className="relative bg-gray-50">
          <Contact />
        </section>
      </main>

      {/* Professional Footer */}
      <Footer />

      {/* Scroll to Top Button */}
      <button
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        className="fixed bottom-8 right-8 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110 z-50"
        aria-label="Scroll to top"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M5 10l7-7m0 0l7 7m-7-7v18"
          />
        </svg>
      </button>
    </div>
  );
};

export default Home;
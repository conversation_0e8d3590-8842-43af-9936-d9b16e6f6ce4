import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Stethoscope, 
  Phone, 
  MapPin, 
  Clock, 
  Users, 
  Calendar,
  AlertCircle,
  CheckCircle,
  XCircle,
  Star,
  Activity
} from "lucide-react";

// Mock data for veterinary doctors
const mockDoctors = [
  {
    id: "VET-001",
    name: "Dr. <PERSON><PERSON>",
    phone: "+91 98765 43210",
    region: "Kolhapur",
    specialization: "Dairy Cattle Medicine",
    experience: 12,
    availability: {
      status: "available",
      days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      hours: "9:00 AM - 6:00 PM"
    },
    consultationCapacity: 25,
    todayConsultations: 18,
    rating: 4.8,
    lastActive: "2024-01-30 14:30",
    outageReports: []
  },
  {
    id: "VET-002", 
    name: "<PERSON><PERSON> <PERSON><PERSON>",
    phone: "+91 87654 32109",
    region: "<PERSON><PERSON>",
    specialization: "Buffalo Health & Reproduction",
    experience: 8,
    availability: {
      status: "busy",
      days: ["Monday", "Wednesday", "Friday", "Saturday"],
      hours: "8:00 AM - 5:00 PM"
    },
    consultationCapacity: 20,
    todayConsultations: 20,
    rating: 4.9,
    lastActive: "2024-01-30 15:45",
    outageReports: []
  },
  {
    id: "VET-003",
    name: "Dr. Amit Desai",
    phone: "+91 76543 21098", 
    region: "Satara",
    specialization: "Small Ruminant Care",
    experience: 15,
    availability: {
      status: "unavailable",
      days: ["Tuesday", "Thursday", "Saturday"],
      hours: "10:00 AM - 4:00 PM"
    },
    consultationCapacity: 15,
    todayConsultations: 0,
    rating: 4.6,
    lastActive: "2024-01-29 18:00",
    outageReports: [
      {
        date: "2024-01-30",
        reason: "Medical Emergency",
        duration: "Full Day"
      }
    ]
  },
  {
    id: "VET-004",
    name: "Dr. Sunita Kulkarni",
    phone: "+91 65432 10987",
    region: "Kolhapur", 
    specialization: "Preventive Medicine & Vaccination",
    experience: 10,
    availability: {
      status: "available",
      days: ["Monday", "Tuesday", "Thursday", "Friday", "Saturday"],
      hours: "7:00 AM - 3:00 PM"
    },
    consultationCapacity: 30,
    todayConsultations: 12,
    rating: 4.7,
    lastActive: "2024-01-30 16:15",
    outageReports: []
  }
];

const VeterinaryNetwork = () => {
  const [selectedRegion, setSelectedRegion] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available": return "bg-green-100 text-green-800";
      case "busy": return "bg-yellow-100 text-yellow-800";
      case "unavailable": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "available": return <CheckCircle className="h-4 w-4" />;
      case "busy": return <Clock className="h-4 w-4" />;
      case "unavailable": return <XCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const filteredDoctors = mockDoctors.filter(doctor => {
    return (selectedRegion === "all" || doctor.region === selectedRegion) &&
           (selectedStatus === "all" || doctor.availability.status === selectedStatus);
  });

  const calculateStats = () => {
    const total = filteredDoctors.length;
    const available = filteredDoctors.filter(d => d.availability.status === "available").length;
    const busy = filteredDoctors.filter(d => d.availability.status === "busy").length;
    const unavailable = filteredDoctors.filter(d => d.availability.status === "unavailable").length;
    const totalCapacity = filteredDoctors.reduce((sum, d) => sum + d.consultationCapacity, 0);
    const totalConsultations = filteredDoctors.reduce((sum, d) => sum + d.todayConsultations, 0);

    return { total, available, busy, unavailable, totalCapacity, totalConsultations };
  };

  const stats = calculateStats();

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Veterinary Doctor Network</h2>
          <p className="text-gray-600">Manage Gokul-affiliated doctors and their availability</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={selectedRegion} onValueChange={setSelectedRegion}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Regions</SelectItem>
              <SelectItem value="Kolhapur">Kolhapur</SelectItem>
              <SelectItem value="Sangli">Sangli</SelectItem>
              <SelectItem value="Satara">Satara</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="available">Available</SelectItem>
              <SelectItem value="busy">Busy</SelectItem>
              <SelectItem value="unavailable">Unavailable</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Total Doctors</p>
                <p className="text-3xl font-bold text-blue-900">{stats.total}</p>
              </div>
              <div className="bg-blue-200 p-3 rounded-full">
                <Stethoscope className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Available Now</p>
                <p className="text-3xl font-bold text-green-900">{stats.available}</p>
              </div>
              <div className="bg-green-200 p-3 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Daily Capacity</p>
                <p className="text-3xl font-bold text-purple-900">{stats.totalCapacity}</p>
              </div>
              <div className="bg-purple-200 p-3 rounded-full">
                <Users className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">Today's Consultations</p>
                <p className="text-3xl font-bold text-orange-900">{stats.totalConsultations}</p>
              </div>
              <div className="bg-orange-200 p-3 rounded-full">
                <Activity className="h-6 w-6 text-orange-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Doctor Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredDoctors.map(doctor => (
          <Card key={doctor.id} className="shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-3">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Stethoscope className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-bold text-lg">{doctor.name}</div>
                    <div className="text-sm text-gray-500 font-normal">{doctor.id}</div>
                  </div>
                </CardTitle>
                <Badge className={getStatusColor(doctor.availability.status)}>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(doctor.availability.status)}
                    {doctor.availability.status}
                  </div>
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Contact & Location */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="flex items-center gap-2 text-sm">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">{doctor.phone}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span>{doctor.region}</span>
                </div>
              </div>

              {/* Specialization & Experience */}
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-sm font-semibold text-gray-900 mb-1">Specialization</div>
                <div className="text-sm text-gray-700">{doctor.specialization}</div>
                <div className="text-xs text-gray-500 mt-1">{doctor.experience} years experience</div>
              </div>

              {/* Availability */}
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-semibold text-blue-900">Availability</span>
                </div>
                <div className="text-sm text-blue-800 mb-1">{doctor.availability.hours}</div>
                <div className="flex flex-wrap gap-1">
                  {doctor.availability.days.map(day => (
                    <Badge key={day} variant="outline" className="text-xs">
                      {day.slice(0, 3)}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Consultation Stats */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-green-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-green-900">{doctor.todayConsultations}</div>
                  <div className="text-xs text-green-600">Today's Consultations</div>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-purple-900">{doctor.consultationCapacity}</div>
                  <div className="text-xs text-purple-600">Daily Capacity</div>
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span className="text-sm font-medium">{doctor.rating}/5.0</span>
                </div>
                <div className="text-xs text-gray-500">
                  Last active: {doctor.lastActive}
                </div>
              </div>

              {/* Outage Reports */}
              {doctor.outageReports.length > 0 && (
                <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span className="text-sm font-medium text-red-900">Outage Report</span>
                  </div>
                  {doctor.outageReports.map((report, index) => (
                    <div key={index} className="text-sm text-red-700">
                      <div className="font-medium">{report.reason}</div>
                      <div className="text-xs">{report.date} - {report.duration}</div>
                    </div>
                  ))}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Phone className="h-4 w-4 mr-2" />
                  Call
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredDoctors.length === 0 && (
        <Card className="p-8 text-center">
          <div className="text-gray-500">
            <Stethoscope className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No doctors found matching the selected filters.</p>
          </div>
        </Card>
      )}
    </div>
  );
};

export default VeterinaryNetwork;

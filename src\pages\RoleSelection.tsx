import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Building2, Heart, ArrowRight, CheckCircle, Users, Activity, Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";

const RoleSelection = () => {
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleRoleSelection = async (role: string) => {
    setSelectedRole(role);
    setIsLoading(true);

    // Add loading animation
    await new Promise(resolve => setTimeout(resolve, 1000));

    localStorage.setItem("userRole", role);

    // Navigate based on role
    if (role === "internal") {
      navigate("/dashboard");
    } else if (role === "healthcare") {
      navigate("/healthcare-dashboard");
    }
    setIsLoading(false);
  };

  const roles = [
    {
      id: "internal",
      title: "Gokul Internal Operations",
      description: "Access to internal operational dashboards, user management, logs, reports, and system administration.",
      icon: <Building2 className="h-12 w-12" />,
      features: [
        "Internal Dashboards",
        "User Management", 
        "System Logs",
        "Operational Reports",
        "Admin Controls"
      ],
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    {
      id: "healthcare",
      title: "Healthcare Support",
      description: "Kolhapur district healthcare dashboard with 12 talukas, 6K sansthas, and 234K animals monitoring with AI insights.",
      icon: <Heart className="h-12 w-12" />,
      features: [
        "234K Animals Monitored",
        "12 Talukas Coverage",
        "6K Sansthas Network",
        "AI Health Predictions",
        "Veterinary Support"
      ],
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-4xl">
          {/* Header Section */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white p-4 rounded-2xl shadow-lg">
                <img
                  src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png"
                  alt="Gokul Dairy Logo"
                  className="h-16 w-20 object-contain"
                />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-800 mb-4">
              गोकुळ दूध डेयरी
            </h1>
            <p className="text-xl text-gray-600 mb-6">Welcome! Please select your role to continue</p>
            <Badge className="bg-blue-100 text-blue-800 px-4 py-2">
              <Building2 className="w-4 h-4 mr-2" />
              Dairy Management System
            </Badge>
          </div>

        {/* Role Selection Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          {roles.map((role) => (
            <Card
              key={role.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg border-2 ${
                selectedRole === role.id
                  ? "border-blue-500 shadow-lg bg-blue-50"
                  : "border-gray-200 hover:border-blue-300"
              }`}
              onClick={() => setSelectedRole(role.id)}
            >
              <CardHeader className="text-center pb-4">
                <div className="mx-auto p-4 rounded-full bg-blue-100 text-blue-600 mb-4 w-fit">
                  {role.icon}
                </div>
                <CardTitle className="text-2xl text-gray-800 mb-2">
                  {role.title}
                </CardTitle>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {role.description}
                </p>
              </CardHeader>

              <CardContent>
                <div className="space-y-3 mb-6">
                  <h4 className="font-semibold text-gray-800 flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                    Key Features:
                  </h4>
                  <ul className="space-y-2">
                    {role.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <div className="h-2 w-2 bg-blue-400 rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRoleSelection(role.id);
                  }}
                  className={`w-full bg-gradient-to-r ${role.color} hover:opacity-90 text-white shadow-lg`}
                  size="lg"
                >
                  Select {role.title}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

          {/* Additional Info */}
          <div className="text-center animate-fade-in" style={{ animationDelay: '0.8s' }}>
            <div className="flex items-center justify-center space-x-8 text-sm text-white/80">
              <div className="flex items-center backdrop-blur-sm bg-white/10 px-4 py-2 rounded-full">
                <Users className="h-4 w-4 mr-2" />
                Multi-Role Access
              </div>
              <div className="flex items-center backdrop-blur-sm bg-white/10 px-4 py-2 rounded-full">
                <Activity className="h-4 w-4 mr-2" />
                Real-time Analytics
              </div>
              <div className="flex items-center backdrop-blur-sm bg-white/10 px-4 py-2 rounded-full">
                <CheckCircle className="h-4 w-4 mr-2" />
                Secure Authentication
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleSelection;

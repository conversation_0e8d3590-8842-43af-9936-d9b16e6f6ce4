import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Building2, Heart, ArrowRight, CheckCircle, Users, Activity, Sparkles, Zap } from "lucide-react";
import { useNavigate } from "react-router-dom";

const RoleSelection = () => {
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hoveredRole, setHoveredRole] = useState<string | null>(null);
  const navigate = useNavigate();

  // Add entrance animation delay
  useEffect(() => {
    const timer = setTimeout(() => {
      document.body.classList.add('loaded');
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const handleRoleSelection = async (role: string) => {
    setSelectedRole(role);
    setIsLoading(true);

    // Add loading animation
    await new Promise(resolve => setTimeout(resolve, 1500));

    localStorage.setItem("userRole", role);

    // Navigate based on role
    if (role === "internal") {
      navigate("/dashboard");
    } else if (role === "healthcare") {
      navigate("/healthcare-dashboard");
    }
    setIsLoading(false);
  };

  const roles = [
    {
      id: "internal",
      title: "Gokul Internal Operations",
      description: "Access to internal operational dashboards, user management, logs, reports, and system administration.",
      icon: <Building2 className="h-12 w-12" />,
      features: [
        "Internal Dashboards",
        "User Management", 
        "System Logs",
        "Operational Reports",
        "Admin Controls"
      ],
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    {
      id: "healthcare",
      title: "Healthcare Support",
      description: "Kolhapur district healthcare dashboard with 12 talukas, 6K sansthas, and 234K animals monitoring with AI insights.",
      icon: <Heart className="h-12 w-12" />,
      features: [
        "234K Animals Monitored",
        "12 Talukas Coverage",
        "6K Sansthas Network",
        "AI Health Predictions",
        "Veterinary Support"
      ],
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    }
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <img
          src="/login-1.jpg"
          alt="Background"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-purple-900/70 to-green-900/80"></div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${4 + Math.random() * 2}s`
            }}
          >
            <Sparkles className="h-6 w-6 text-white/20" />
          </div>
        ))}
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-6xl">
          {/* Header Section */}
          <div className="text-center mb-12 animate-fade-in">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="relative">
                <img
                  src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png"
                  alt="Gokul Dairy Logo"
                  className="h-24 w-28 object-contain animate-bounce"
                />
                <div className="absolute -top-2 -right-2">
                  <Zap className="h-8 w-8 text-yellow-300 animate-pulse" />
                </div>
              </div>
            </div>
            <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-white via-blue-200 to-green-200 bg-clip-text text-transparent">
              गोकुळ दूध डेयरी
            </h1>
            <p className="text-xl text-blue-100 mb-6">Welcome! Please select your role to continue</p>
            <Badge variant="outline" className="text-sm px-6 py-2 bg-white/10 border-white/20 text-white/90 backdrop-blur-sm">
              ISO 9001:2015 Certified • Daily Capacity: 240K+ Litres
            </Badge>
          </div>

        {/* Role Selection Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          {roles.map((role) => (
            <Card
              key={role.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 transform ${
                selectedRole === role.id 
                  ? `ring-4 ring-opacity-50 ${role.borderColor} shadow-2xl scale-105` 
                  : "hover:shadow-xl"
              } ${role.bgColor} border-2 ${role.borderColor}`}
              onClick={() => setSelectedRole(role.id)}
            >
              <CardHeader className="text-center pb-4">
                <div className={`mx-auto p-4 rounded-full bg-gradient-to-r ${role.color} text-white mb-4 w-fit`}>
                  {role.icon}
                </div>
                <CardTitle className="text-2xl text-gray-900 mb-2">
                  {role.title}
                </CardTitle>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {role.description}
                </p>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3 mb-6">
                  <h4 className="font-semibold text-gray-900 flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                    Key Features:
                  </h4>
                  <ul className="space-y-2">
                    {role.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-700">
                        <div className="h-2 w-2 bg-gray-400 rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRoleSelection(role.id);
                  }}
                  className={`w-full bg-gradient-to-r ${role.color} hover:opacity-90 text-white shadow-lg`}
                  size="lg"
                >
                  Select {role.title}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

          {/* Additional Info */}
          <div className="text-center animate-fade-in" style={{ animationDelay: '0.8s' }}>
            <div className="flex items-center justify-center space-x-8 text-sm text-white/80">
              <div className="flex items-center backdrop-blur-sm bg-white/10 px-4 py-2 rounded-full">
                <Users className="h-4 w-4 mr-2" />
                Multi-Role Access
              </div>
              <div className="flex items-center backdrop-blur-sm bg-white/10 px-4 py-2 rounded-full">
                <Activity className="h-4 w-4 mr-2" />
                Real-time Analytics
              </div>
              <div className="flex items-center backdrop-blur-sm bg-white/10 px-4 py-2 rounded-full">
                <CheckCircle className="h-4 w-4 mr-2" />
                Secure Authentication
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleSelection;

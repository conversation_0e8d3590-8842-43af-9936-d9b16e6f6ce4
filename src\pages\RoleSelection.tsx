import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Building2, Heart, ArrowRight, CheckCircle, Users, Activity } from "lucide-react";
import { useNavigate } from "react-router-dom";

const RoleSelection = () => {
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleRoleSelection = (role: string) => {
    setSelectedRole(role);
    localStorage.setItem("userRole", role);
    
    // Navigate based on role
    if (role === "internal") {
      navigate("/dashboard");
    } else if (role === "healthcare") {
      navigate("/healthcare-dashboard");
    }
  };

  const roles = [
    {
      id: "internal",
      title: "Google Internal Operations",
      description: "Access to internal operational dashboards, user management, logs, reports, and system administration.",
      icon: <Building2 className="h-12 w-12" />,
      features: [
        "Internal Dashboards",
        "User Management", 
        "System Logs",
        "Operational Reports",
        "Admin Controls"
      ],
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    {
      id: "healthcare",
      title: "Healthcare Support",
      description: "Specialized dashboard for region-wise and organization-wise animal health data with AI insights and predictions.",
      icon: <Heart className="h-12 w-12" />,
      features: [
        "Animal Health Analytics",
        "Regional Insights",
        "Veterinary Network",
        "Milk Production Tracking",
        "AI Predictions"
      ],
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-200 to-green-500 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <img
              src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png"
              alt="Gokul Dairy Logo"
              className="h-20 w-24 object-contain"
            />
          </div>
          <h1 className="text-4xl font-bold text-primary mb-2">गोकुल दूध डेयरी</h1>
          <p className="text-lg text-gray-900 mb-4">Welcome! Please select your role to continue</p>
          <Badge variant="outline" className="text-sm px-4 py-2">
            ISO 9001:2015 Certified • Daily Capacity: 17K Litres
          </Badge>
        </div>

        {/* Role Selection Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          {roles.map((role) => (
            <Card
              key={role.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 transform ${
                selectedRole === role.id 
                  ? `ring-4 ring-opacity-50 ${role.borderColor} shadow-2xl scale-105` 
                  : "hover:shadow-xl"
              } ${role.bgColor} border-2 ${role.borderColor}`}
              onClick={() => setSelectedRole(role.id)}
            >
              <CardHeader className="text-center pb-4">
                <div className={`mx-auto p-4 rounded-full bg-gradient-to-r ${role.color} text-white mb-4 w-fit`}>
                  {role.icon}
                </div>
                <CardTitle className="text-2xl text-gray-900 mb-2">
                  {role.title}
                </CardTitle>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {role.description}
                </p>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3 mb-6">
                  <h4 className="font-semibold text-gray-900 flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                    Key Features:
                  </h4>
                  <ul className="space-y-2">
                    {role.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-700">
                        <div className="h-2 w-2 bg-gray-400 rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRoleSelection(role.id);
                  }}
                  className={`w-full bg-gradient-to-r ${role.color} hover:opacity-90 text-white shadow-lg`}
                  size="lg"
                >
                  Select {role.title}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Info */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-8 text-sm text-gray-600">
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Multi-Role Access
            </div>
            <div className="flex items-center">
              <Activity className="h-4 w-4 mr-2" />
              Real-time Analytics
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 mr-2" />
              Secure Authentication
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleSelection;

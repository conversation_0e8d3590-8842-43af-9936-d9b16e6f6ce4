
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Users,
  Package,
  Truck,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  RefreshCw,
  Download
} from "lucide-react";

const MISDashboard = () => {
  const [lastUpdated, setLastUpdated] = useState("2 minutes ago");
  const [isRefreshing, setIsRefreshing] = useState(false);

  const kpiMetrics = [
    {
      title: "Production Efficiency",
      value: "92.4%",
      target: "95%",
      change: "+2.1%",
      trend: "up",
      status: "good",
      icon: <Package className="h-6 w-6 text-sky-600" />
    },
    {
      title: "Quality Score",
      value: "96.8%",
      target: "98%",
      change: "+0.5%",
      trend: "up", 
      status: "good",
      icon: <CheckCircle className="h-6 w-6 text-green-600" />
    },
    {
      title: "Dispatch Efficiency",
      value: "88.3%",
      target: "90%",
      change: "+3.2%",
      trend: "up",
      status: "warning",
      icon: <Truck className="h-6 w-6 text-blue-600" />
    },
    {
      title: "Energy Optimization",
      value: "85.7%",
      target: "90%",
      change: "+1.8%",
      trend: "up",
      status: "warning",
      icon: <Zap className="h-6 w-6 text-yellow-600" />
    }
  ];

  const productionTrends = [
    { product: "Milk", today: 165000, yesterday: 162000, weekly: "+2.3%", monthly: "+5.1%" },
    { product: "Curd", today: 12500, yesterday: 12800, weekly: "-1.8%", monthly: "+3.2%" },
    { product: "Ghee", today: 3200, yesterday: 3100, weekly: "+1.5%", monthly: "+4.8%" },
    { product: "Paneer", today: 2800, yesterday: 2900, weekly: "-0.5%", monthly: "+2.1%" },
    { product: "Butter", today: 1450, yesterday: 1500, weekly: "-2.1%", monthly: "+1.8%" }
  ];

  const alerts = [
    {
      type: "warning",
      title: "Cold Storage B - Temperature Alert",
      description: "Temperature deviation detected in Cold Storage B",
      time: "5 minutes ago",
      severity: "medium"
    },
    {
      type: "info",
      title: "Packaging Line C - Maintenance Due",
      description: "Scheduled maintenance required for optimal performance",
      time: "15 minutes ago", 
      severity: "low"
    },
    {
      type: "success",
      title: "Quality Check Batch B004 - Passed",
      description: "All parameters within acceptable range",
      time: "20 minutes ago",
      severity: "low"
    },
    {
      type: "error",
      title: "Dispatch Route B - Delay Risk",
      description: "Traffic conditions may cause delivery delays",
      time: "25 minutes ago",
      severity: "high"
    }
  ];

  const operationalInsights = [
    {
      insight: "Milk production efficiency increased by 5% after AI quality monitoring implementation",
      impact: "High",
      recommendation: "Expand AI monitoring to all production lines",
      category: "Quality"
    },
    {
      insight: "Peak solar hours (12-3 PM) show optimal energy utilization rates",
      impact: "Medium",
      recommendation: "Schedule energy-intensive operations during solar peak",
      category: "Energy"
    },
    {
      insight: "Dispatch Route A consistently outperforms other routes by 15%",
      impact: "Medium", 
      recommendation: "Analyze Route A patterns for other route optimization",
      category: "Logistics"
    },
    {
      insight: "Packaging defect detection reduced recall incidents by 78%",
      impact: "High",
      recommendation: "Consider additional camera installations",
      category: "Quality"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'error': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      case 'success': return 'bg-green-50 border-green-200';
      case 'info': return 'bg-blue-50 border-blue-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'success': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'info': return <Clock className="h-5 w-5 text-blue-600" />;
      default: return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getTrendIcon = (change: string) => {
    return change.startsWith('+') ? 
      <TrendingUp className="h-4 w-4 text-green-600" /> : 
      <TrendingDown className="h-4 w-4 text-red-600" />;
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'High': return 'bg-red-100 text-red-800 border-red-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const refreshDashboard = () => {
    setIsRefreshing(true);
    setTimeout(() => {
      setIsRefreshing(false);
      setLastUpdated("Just now");
    }, 2000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-sky-200 bg-gradient-to-r from-sky-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-6 w-6 text-sky-600" />
              <span>AI-Driven Management Information System</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Last updated: {lastUpdated}</span>
              <Button 
                onClick={refreshDashboard}
                disabled={isRefreshing}
                size="sm"
                variant="outline"
              >
                {isRefreshing ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
              <Button size="sm" className="bg-sky-600 hover:bg-sky-700">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
            </div>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Real-time business insights and automated reporting for MD-level decision making
          </p>
        </CardHeader>
      </Card>

      {/* KPI Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {kpiMetrics.map((metric, index) => (
          <Card key={index} className="border-sky-200">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {metric.icon}
                    <h4 className="text-sm font-medium text-gray-700">{metric.title}</h4>
                  </div>
                  <Badge className={getStatusColor(metric.status)}>
                    {metric.status}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex items-end justify-between">
                    <span className="text-2xl font-bold text-sky-700">{metric.value}</span>
                    <div className="flex items-center space-x-1">
                      {getTrendIcon(metric.change)}
                      <span className="text-sm font-medium text-green-600">{metric.change}</span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">Target: {metric.target}</p>
                  <Progress value={parseFloat(metric.value)} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Production Trends */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-sky-600" />
            <span>Production Trends & Analytics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {productionTrends.map((product, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <Package className="h-5 w-5 text-sky-600" />
                  <div>
                    <h4 className="font-medium text-gray-900">{product.product}</h4>
                    <p className="text-sm text-gray-600">
                      Today: {product.today.toLocaleString()} L | Yesterday: {product.yesterday.toLocaleString()} L
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Weekly</p>
                    <div className="flex items-center space-x-1">
                      {getTrendIcon(product.weekly)}
                      <span className={`font-medium ${product.weekly.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                        {product.weekly}
                      </span>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Monthly</p>
                    <div className="flex items-center space-x-1">
                      {getTrendIcon(product.monthly)}
                      <span className={`font-medium ${product.monthly.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                        {product.monthly}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Alerts & Notifications */}
      <Card className="border-sky-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            <span>Real-time Alerts & Notifications</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {alerts.map((alert, index) => (
              <div key={index} className={`flex items-start space-x-3 p-3 rounded-lg border ${getAlertColor(alert.type)}`}>
                <div className="mt-0.5">
                  {getAlertIcon(alert.type)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">{alert.title}</h4>
                    <Badge variant="outline" className="text-xs">
                      {alert.severity}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{alert.description}</p>
                  <p className="text-xs text-gray-500 mt-2 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {alert.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Insights */}
      <Card className="border-sky-200 bg-gradient-to-r from-purple-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-purple-600" />
            <span>AI-Generated Business Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {operationalInsights.map((insight, index) => (
              <div key={index} className="p-4 bg-white rounded-lg border border-purple-200">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {insight.category}
                    </Badge>
                    <Badge className={getImpactColor(insight.impact)}>
                      {insight.impact} Impact
                    </Badge>
                  </div>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">{insight.insight}</h4>
                <p className="text-sm text-blue-700 font-medium">
                  💡 Recommendation: {insight.recommendation}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Benefits Summary */}
      <Card className="border-sky-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-6 w-6 text-green-600" />
            <span>MIS Dashboard Benefits</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">100%</div>
              <p className="text-sm text-gray-700">Real-time data visibility</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">80%</div>
              <p className="text-sm text-gray-700">Faster decision making</p>
            </div>
            <div className="text-center p-4">
              <div className="text-3xl font-bold text-green-600 mb-2">90%</div>
              <p className="text-sm text-gray-700">Improved governance</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MISDashboard;

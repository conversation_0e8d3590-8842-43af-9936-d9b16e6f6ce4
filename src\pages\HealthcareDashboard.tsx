import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Activity,
  Stethoscope,
  Milk,
  Brain,
  ChevronLeft,
  ChevronRight,
  LogOut,
  Heart,
  BarChart3,
  Users,
  TrendingUp,
  AlertTriangle,
  Building2,
  Bot,
} from "lucide-react";
import RegionSansthaOverview from "@/components/healthcare/RegionSansthaOverview";
import HealthAnalytics from "@/components/healthcare/HealthAnalytics";
import VeterinaryNetwork from "@/components/healthcare/VeterinaryNetwork";
import MilkContributionInsights from "@/components/healthcare/MilkContributionInsights";
import AIInsightsHealthcare from "@/components/healthcare/AIInsightsHealthcare";
import DoctorGPT from "@/components/healthcare/DoctorGPT";

const HealthcareDashboard = () => {
  const navigate = useNavigate();
  const [activeModule, setActiveModule] = useState("overview");
  const [sidebarOpen, setSidebarOpen] = useState(true);

  useEffect(() => {
    const isAuthenticated = localStorage.getItem("isAuthenticated");
    const userRole = localStorage.getItem("userRole");
    
    if (!isAuthenticated || userRole !== "healthcare") {
      navigate("/login");
    }
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem("isAuthenticated");
    localStorage.removeItem("userRole");
    navigate("/");
  };

  const modules = [
    {
      id: "overview",
      title: "Region & Sanstha Overview",
      icon: <MapPin className="h-5 w-5" />,
      description: "Regional animal inventory & organization management",
      stats: "12 Regions • 45 Sansthas"
    },
    {
      id: "health-analytics",
      title: "Health Analytics per Animal",
      icon: <Activity className="h-5 w-5" />,
      description: "Daily health predictions & feed requirements",
      stats: "2,847 Animals Monitored"
    },
    {
      id: "veterinary-network",
      title: "Veterinary Doctor Network",
      icon: <Stethoscope className="h-5 w-5" />,
      description: "Gokul-affiliated doctors & availability",
      stats: "23 Active Doctors"
    },
    {
      id: "milk-insights",
      title: "Gokul Milk Contribution Insights",
      icon: <Milk className="h-5 w-5" />,
      description: "Milk production tracking & analysis",
      stats: "17K Litres Daily"
    },
    {
      id: "ai-insights",
      title: "AI-Powered Insights & Recommendations",
      icon: <Brain className="h-5 w-5" />,
      description: "Predictive analytics & recommendations",
      stats: "95% Accuracy"
    },
    {
      id: "doctor-gpt",
      title: "DoctorGPT",
      icon: <Bot className="h-5 w-5" />,
      description: "AI veterinary assistant for healthcare support",
      stats: "24/7 Available"
    },
  ];

  const renderContent = () => {
    switch (activeModule) {
      case "overview":
        return <RegionSansthaOverview />;
      case "health-analytics":
        return <HealthAnalytics />;
      case "veterinary-network":
        return <VeterinaryNetwork />;
      case "milk-insights":
        return <MilkContributionInsights />;
      case "ai-insights":
        return <AIInsightsHealthcare />;
      case "doctor-gpt":
        return <DoctorGPT />;
      default:
        return <RegionSansthaOverview />;
    }
  };

  const handleModuleClick = (moduleId: string) => {
    setActiveModule(moduleId);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex font-inter">
      {/* Fixed Sidebar */}
      <div
        className={`${
          sidebarOpen ? "w-80" : "w-28"
        } bg-white shadow-xl transition-all duration-300 flex flex-col border-r border-green-100 fixed h-screen z-30`}
      >
        {/* Header with Logo */}
        <div className="p-6 border-b border-green-100">
          <div className="flex items-center justify-center relative">
            <div className={`flex items-center mr-8 ${!sidebarOpen && "justify-center"}`}>
              <div className="flex-shrink-0">
                <img
                  src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png"
                  alt="Gokul Dairy Logo"
                  className={`object-contain transition-all duration-300 ${
                    sidebarOpen ? "h-30 w-36" : "h-12 w-12"
                  }`}
                />
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="hover:bg-green-50 p-2 justify-end absolute top-4 -right-4"
            >
              {sidebarOpen ? (
                <ChevronLeft className="h-4 w-4 text-green-600" />
              ) : (
                <ChevronRight className="h-4 w-4 text-green-600" />
              )}
            </Button>
          </div>
          {sidebarOpen && (
            <div className="mt-4 text-center">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                🐄 Healthcare Support Dashboard
              </Badge>
            </div>
          )}
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 p-4 space-y-3 overflow-y-auto">
          {modules.map((module) => (
            <button
              key={module.id}
              onClick={() => handleModuleClick(module.id)}
              className={`w-full flex items-start space-x-3 px-4 py-4 rounded-xl text-left transition-all duration-200 group ${
                activeModule === module.id
                  ? "bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg transform scale-105"
                  : "text-gray-700 hover:bg-green-50 hover:text-green-700 hover:shadow-md"
              }`}
            >
              <div
                className={`${
                  activeModule === module.id ? "text-white" : "text-green-500"
                } transition-colors flex-shrink-0 mt-1`}
              >
                {module.icon}
              </div>
              {sidebarOpen && (
                <div className="flex-1 min-w-0">
                  <div className="font-semibold text-sm mb-1">{module.title}</div>
                  <div className="text-xs opacity-75 mb-2 leading-relaxed">
                    {module.description}
                  </div>
                  <div className="text-xs font-medium opacity-90">
                    {module.stats}
                  </div>
                </div>
              )}
            </button>
          ))}
        </nav>

        {/* Footer Info */}
        {sidebarOpen && (
          <div className="p-4 border-t border-green-100 bg-gradient-to-r from-green-50 to-blue-50">
            <div className="text-xs text-gray-600 space-y-1">
              <div className="font-medium text-green-700">Healthcare Support Mode</div>
              <div className="text-blue-600">Real-time Animal Health Monitoring</div>
              <div className="text-gray-500">Kolhapur, Maharashtra</div>
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div
        className={`flex-1 flex flex-col ${
          sidebarOpen ? "ml-80" : "ml-28"
        } transition-all duration-300`}
      >
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-green-100 px-6 py-4">
          <div className="flex items-center justify-between">
            {activeModule === "doctor-gpt" ? (
              <div className="flex items-center gap-4 bg-white rounded-xl p-4">
                <div className="bg-gradient-to-r from-green-600 to-green-700 p-3 rounded-full">
                  <Stethoscope className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {modules.find((module) => module.id === activeModule)?.title}
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    AI-Powered Veterinary Healthcare Assistant
                  </p>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-4">
                <div className="bg-gradient-to-r from-green-600 to-green-700 p-3 rounded-full">
                  <Heart className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="flex items-center gap-3">
                    <h1 className="text-2xl font-bold text-gray-900">
                      {modules.find((module) => module.id === activeModule)?.title}
                    </h1>
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      🐄 Healthcare Support
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {modules.find((module) => module.id === activeModule)?.description}
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-4">
              {/* Role Switch Section */}
              <div className="flex items-center space-x-3 px-3 py-1 bg-gray-50 rounded-lg border">
                <span className="text-xs text-gray-500 font-medium">Switch Role:</span>
                <Button
                  onClick={() => navigate("/dashboard")}
                  variant="outline"
                  size="sm"
                  className="border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700 shadow-sm"
                  title="Switch to Internal Operations Dashboard"
                >
                  <Building2 className="h-3 w-3 mr-1" />
                  Internal
                </Button>
              </div>

              {/* DoctorGPT Button */}
              <Button
                onClick={() => setActiveModule("doctor-gpt")}
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg"
                title="AI-powered veterinary assistant for healthcare support"
              >
                <Stethoscope className="h-4 w-4 mr-2" />
                DoctorGPT
              </Button>

              <Button
                onClick={handleLogout}
                variant="outline"
                className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 p-6 overflow-auto bg-gradient-to-br from-green-50/30 to-blue-50/30">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default HealthcareDashboard;

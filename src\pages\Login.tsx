import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Eye, EyeOff, Lock, User, Milk, Sparkles } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [credentials, setCredentials] = useState({ username: "", password: "" });
  const [isLoading, setIsLoading] = useState(false);
  const [currentImage, setCurrentImage] = useState(0);
  const navigate = useNavigate();

  const images = ["/login.jpg", "/login-1.jpg"];

  // Auto-rotate background images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImage((prev) => (prev + 1) % images.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate loading animation
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Simple authentication - replace with real auth
    if (credentials.username && credentials.password) {
      localStorage.setItem("isAuthenticated", "true");
      navigate("/role-selection");
    }
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background Images */}
      <div className="absolute inset-0">
        {images.map((image, index) => (
          <div
            key={image}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentImage ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image}
              alt={`Background ${index + 1}`}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/40"></div>
          </div>
        ))}
      </div>

     

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex">
        {/* Left Side - Welcome Section */}
        <div className="hidden  lg:flex lg:w-1/2 flex-col justify-center items-center p-12 text-white">
          <div className="max-w-md  p-6 rounded-3xl backdrop-blur-lg bg-white/10 text-center space-y-6 animate-fade-in">
            <div className="flex items-center justify-center mb-8">
              <div className="relative">
                <img
                  src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png"
                  alt="Gokul Dairy Logo"
                  className="h-24 w-28 object-contain"
                />
                <div className="absolute -top-2 -right-2">
                  <Milk className="h-8 w-8 text-blue-300 animate-pulse" />
                </div>
              </div>
            </div>

            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r leading-relaxed from-white to-blue-200 bg-clip-text text-transparent">
              गोकुळ दूध डेयरी
            </h1>
            <h2 className="text-2xl font-semibold text-blue-100">
              Gokul Dairy Management System
            </h2>
            <p className="text-lg text-gray-200 leading-relaxed">
              Empowering dairy operations with cutting-edge technology and AI-driven insights across Kolhapur district.
            </p>

            <div className="flex items-center justify-center space-x-8 mt-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-200">234K+</div>
                <div className="text-sm text-gray-300">Animals Monitored</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-200">6K+</div>
                <div className="text-sm text-gray-300">Sansthas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-200">12</div>
                <div className="text-sm text-gray-300">Talukas</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            {/* Mobile Logo */}
            <div className="lg:hidden text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <img
                  src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png"
                  alt="Gokul Dairy Logo"
                  className="h-16 w-20 object-contain animate-pulse"
                />
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">गोकुळ दूध डेयरी</h1>
              <p className="text-blue-200">Dairy Management System</p>
            </div>

            {/* Login Card with Glass Effect */}
            <Card className="backdrop-blur-lg bg-white/10 border border-white/20 shadow-2xl animate-slide-up">
              <CardHeader className="text-center pb-6">
                <CardTitle className="text-2xl text-white flex items-center justify-center gap-2">
                  <Sparkles className="h-6 w-6 text-yellow-300" />
                  Welcome Back
                  <Sparkles className="h-6 w-6 text-yellow-300" />
                </CardTitle>
                <p className="text-blue-100 mt-2">
                  Sign in to access your dashboard
                </p>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleLogin} className="space-y-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/90">
                      Username
                    </label>
                    <div className="relative group">
                      <User className="absolute left-3 top-3 h-5 w-5 text-blue-300 group-focus-within:text-blue-200 transition-colors" />
                      <Input
                        type="text"
                        placeholder="Enter your username"
                        className="pl-12 bg-white/10 border-white/20 text-white placeholder:text-white/60 focus:bg-white/20 focus:border-blue-300 transition-all duration-300"
                        value={credentials.username}
                        onChange={(e) => setCredentials({...credentials, username: e.target.value})}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/90">
                      Password
                    </label>
                    <div className="relative group">
                      <Lock className="absolute left-3 top-3 h-5 w-5 text-blue-300 group-focus-within:text-blue-200 transition-colors" />
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        className="pl-12 pr-12 bg-white/10 border-white/20 text-white placeholder:text-white/60 focus:bg-white/20 focus:border-blue-300 transition-all duration-300"
                        value={credentials.password}
                        onChange={(e) => setCredentials({...credentials, password: e.target.value})}
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-3 text-blue-300 hover:text-blue-200 transition-colors"
                      >
                        {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 rounded-lg transform transition-all duration-300 hover:scale-105 hover:shadow-lg"
                    size="lg"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <div className="flex items-center gap-2">
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        Signing In...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Sparkles className="h-5 w-5" />
                        Sign In
                      </div>
                    )}
                  </Button>
                </form>

                <div className="text-center pt-4 border-t border-white/20">
                  <Button
                    variant="ghost"
                    onClick={() => navigate("/")}
                    className="text-blue-200 hover:text-white hover:bg-white/10 transition-all duration-300"
                  >
                    ← Back to Home
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
        <Badge variant="outline" className="bg-white/10 border-white/20 text-white/80 backdrop-blur-sm">
          ISO 9001:2015 Certified • Daily Capacity: 240K+ Litres
        </Badge>
      </div>
    </div>
  );
};

export default Login;
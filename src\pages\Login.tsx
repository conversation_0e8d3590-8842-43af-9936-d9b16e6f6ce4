import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Eye, EyeOff, Lock, User, Building2, Shield, Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [credentials, setCredentials] = useState({ username: "", password: "" });
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate loading
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simple authentication
    if (credentials.username && credentials.password) {
      localStorage.setItem("isAuthenticated", "true");
      navigate("/role-selection");
    }
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="min-h-screen flex">
        {/* Left Side - Branding */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-400 to-blue-600 flex-col justify-center items-center p-12">
          <div className="max-w-md text-center space-y-8">
            <div className="flex justify-center">
              <div className="bg-white p-4 rounded-2xl shadow-lg">
                <img
                  src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png"
                  alt="Gokul Dairy Logo"
                  className="h-16 w-20 object-contain"
                />
              </div>
            </div>

            <div className="space-y-4">
              <h1 className="text-3xl font-bold text-white">
                गोकुळ दूध डेयरी
              </h1>
              <h2 className="text-xl font-semibold text-blue-100">
                Dairy Management System
              </h2>
              <p className="text-blue-100 leading-relaxed">
                Professional dairy operations management platform for modern farming solutions.
              </p>
            </div>

            <div className="grid grid-cols-3 gap-6 pt-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">234K+</div>
                <div className="text-sm text-blue-200">Animals</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">12</div>
                <div className="text-sm text-blue-200">Talukas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">6K+</div>
                <div className="text-sm text-blue-200">Sansthas</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            {/* Mobile Logo */}
            <div className="lg:hidden text-center mb-8">
              <div className="bg-white p-3 rounded-xl shadow-lg inline-block mb-4">
                <img
                  src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png"
                  alt="Gokul Dairy Logo"
                  className="h-12 w-16 object-contain"
                />
              </div>
              <h1 className="text-2xl font-bold text-gray-800 mb-2">गोकुळ दूध डेयरी</h1>
              <p className="text-gray-600">Dairy Management System</p>
            </div>

            {/* Login Card */}
            <Card className="shadow-xl border-0">
              <CardHeader className="text-center pb-6">
                <CardTitle className="text-2xl text-gray-800 flex items-center justify-center gap-2">
                  <Building2 className="h-6 w-6 text-blue-600" />
                  Welcome Back
                </CardTitle>
                <p className="text-gray-600 mt-2">
                  Sign in to access your dashboard
                </p>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleLogin} className="space-y-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Username
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                      <Input
                        type="text"
                        placeholder="Enter your username"
                        className="pl-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        value={credentials.username}
                        onChange={(e) => setCredentials({...credentials, username: e.target.value})}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        className="pl-12 pr-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        value={credentials.password}
                        onChange={(e) => setCredentials({...credentials, password: e.target.value})}
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3"
                    size="lg"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-5 w-5 animate-spin" />
                        Signing In...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Shield className="h-5 w-5" />
                        Sign In
                      </div>
                    )}
                  </Button>
                </form>

                <div className="text-center pt-4 border-t border-gray-200">
                  <Button
                    variant="ghost"
                    onClick={() => navigate("/")}
                    className="text-gray-600 hover:text-gray-800"
                  >
                    ← Back to Home
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Footer */}
            <div className="text-center mt-8">
              <Badge variant="outline" className="bg-white border-gray-200 text-gray-600">
                <Building2 className="w-4 h-4 mr-2" />
                Secure Dairy Management Platform
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
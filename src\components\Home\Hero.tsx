import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, Shield, Award, Brain, Zap, TrendingUp, BarChart3, Activity, Sparkles, Bot, Database, Cpu } from "lucide-react";
import heroImage from "/hero.jpg";
import cowsField from "/hero2.avif";
import { useEffect, useState } from "react";

const Hero = () => {
  const [currentStat, setCurrentStat] = useState(0);
  const [animatedNumbers, setAnimatedNumbers] = useState({ animals: 0, sansthas: 0, talukas: 0, milk: 0 });

  const stats = [
    { value: 234000, label: "Animals Monitored", suffix: "K+", icon: Activity },
    { value: 6000, label: "Sansthas Connected", suffix: "K+", icon: Database },
    { value: 12, label: "Talukas Covered", suffix: "", icon: BarChart3 },
    { value: 240, label: "Daily Milk (Litres)", suffix: "K+", icon: TrendingUp }
  ];

  // Animate numbers on mount
  useEffect(() => {
    const animateNumber = (target: number, key: keyof typeof animatedNumbers) => {
      let current = 0;
      const increment = target / 100;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        setAnimatedNumbers(prev => ({ ...prev, [key]: Math.floor(current) }));
      }, 20);
    };

    animateNumber(234, 'animals');
    animateNumber(6, 'sansthas');
    animateNumber(12, 'talukas');
    animateNumber(240, 'milk');
  }, []);

  // Cycle through stats
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStat((prev) => (prev + 1) % stats.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section id="home" className="relative min-h-screen flex items-center overflow-hidden">
      {/* Dynamic Background with Overlay */}
      <div className="absolute inset-0 z-0">
        <img
          src={heroImage}
          alt="Gokul Dairy Farm"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/90 via-blue-900/80 to-purple-900/90"></div>

        {/* Animated Grid Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="grid grid-cols-12 grid-rows-12 h-full w-full">
            {[...Array(144)].map((_, i) => (
              <div
                key={i}
                className="border border-white/20 animate-pulse"
                style={{ animationDelay: `${Math.random() * 3}s` }}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Floating AI Elements */}
      {/* <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(25)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${5 + Math.random() * 3}s`
            }}
          >
            <Sparkles className="h-4 w-4 text-blue-300/30" />
          </div>
        ))}
      </div> */}

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8 animate-fade-in">
            {/* AI-Powered Badges */}
            <div className="flex flex-wrap gap-3">
              <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 text-sm font-medium backdrop-blur-sm">
                <Brain className="w-4 h-4 mr-2 animate-pulse" />
                AI-Powered Analytics
              </Badge>
              <Badge className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2 text-sm font-medium backdrop-blur-sm">
                <Bot className="w-4 h-4 mr-2 animate-bounce" />
                Smart Dairy Management
              </Badge>
              <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 text-sm font-medium backdrop-blur-sm">
                <Cpu className="w-4 h-4 mr-2 animate-spin" />
                Real-time Monitoring
              </Badge>
            </div>

            {/* Main Heading with AI Focus */}
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-1 w-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full"></div>
                  <span className="text-blue-300 text-sm font-medium tracking-wider uppercase">Next-Gen Dairy Technology</span>
                </div>
                <h1 className="text-5xl lg:text-7xl font-bold leading-tight">
                  <span className="bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent">
                    AI-Driven
                  </span>
                  <br />
                  <span className="bg-gradient-to-r from-green-300 via-blue-300 to-purple-300 bg-clip-text text-transparent">
                    Dairy Excellence
                  </span>
                </h1>
              </div>

              <p className="text-xl text-blue-100 max-w-2xl leading-relaxed backdrop-blur-sm bg-white/5 p-6 rounded-2xl border border-white/10">
                Revolutionizing dairy operations in <span className="text-yellow-300 font-semibold">Kolhapur district</span> with cutting-edge AI technology.
                Monitor <span className="text-green-300 font-semibold">234K+ animals</span> across <span className="text-blue-300 font-semibold">12 talukas</span>
                with intelligent insights and predictive analytics.
              </p>
            </div>

            {/* AI-Powered Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 my-8">
              <div className="text-center backdrop-blur-sm bg-white/5 p-6 rounded-2xl border border-white/10 hover:bg-white/10 transition-all duration-300 group">
                <div className="flex items-center justify-center mb-3">
                  <Activity className="h-8 w-8 text-blue-400 group-hover:animate-pulse" />
                </div>
                <div className="text-3xl font-bold text-white">{animatedNumbers.animals}K+</div>
                <div className="text-sm text-blue-200">Animals Monitored</div>
              </div>

              <div className="text-center backdrop-blur-sm bg-white/5 p-6 rounded-2xl border border-white/10 hover:bg-white/10 transition-all duration-300 group">
                <div className="flex items-center justify-center mb-3">
                  <Database className="h-8 w-8 text-green-400 group-hover:animate-bounce" />
                </div>
                <div className="text-3xl font-bold text-white">{animatedNumbers.sansthas}K+</div>
                <div className="text-sm text-green-200">Sansthas Connected</div>
              </div>

              <div className="text-center backdrop-blur-sm bg-white/5 p-6 rounded-2xl border border-white/10 hover:bg-white/10 transition-all duration-300 group">
                <div className="flex items-center justify-center mb-3">
                  <BarChart3 className="h-8 w-8 text-purple-400 group-hover:animate-spin" />
                </div>
                <div className="text-3xl font-bold text-white">{animatedNumbers.talukas}</div>
                <div className="text-sm text-purple-200">Talukas Covered</div>
              </div>

              <div className="text-center backdrop-blur-sm bg-white/5 p-6 rounded-2xl border border-white/10 hover:bg-white/10 transition-all duration-300 group">
                <div className="flex items-center justify-center mb-3">
                  <TrendingUp className="h-8 w-8 text-yellow-400 group-hover:animate-pulse" />
                </div>
                <div className="text-3xl font-bold text-white">{animatedNumbers.milk}K+</div>
                <div className="text-sm text-yellow-200">Litres Daily</div>
              </div>
            </div>

            {/* AI-Powered CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                className="text-lg px-8 py-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold transform transition-all duration-300 hover:scale-105 hover:shadow-xl group"
              >
                <Brain className="w-5 h-5 mr-2 group-hover:animate-pulse" />
                Explore AI Dashboard
                <Zap className="w-5 h-5 ml-2 group-hover:animate-bounce" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm font-semibold transform transition-all duration-300 hover:scale-105 group"
              >
                <Activity className="w-5 h-5 mr-2 group-hover:animate-spin" />
                View Live Analytics
              </Button>
            </div>

            {/* AI Trust Indicators */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-8 pt-6">
              <div className="flex items-center space-x-2 backdrop-blur-sm bg-white/5 px-4 py-2 rounded-full border border-white/10">
                <div className="flex items-center space-x-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <span className="text-sm text-white font-medium">AI Accuracy: 97.8%</span>
              </div>
              <div className="flex items-center space-x-2 backdrop-blur-sm bg-white/5 px-4 py-2 rounded-full border border-white/10">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-white font-medium">Real-time Monitoring Active</span>
              </div>
            </div>
          </div>

          {/* Right Content - AI Technology Showcase */}
          <div className="lg:text-right space-y-6 relative animate-slide-up">
            {/* Floating AI Dashboard Preview */}
            <div className="absolute -top-12 -right-8 w-40 h-32 rounded-2xl overflow-hidden shadow-2xl border-2 border-blue-400/50 rotate-12 hover:rotate-0 transition-transform duration-500 backdrop-blur-sm bg-white/10">
              <div className="p-4 h-full flex flex-col justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-white font-medium">Live AI</span>
                </div>
                <div className="space-y-1">
                  <div className="h-1 bg-blue-400 rounded-full w-full"></div>
                  <div className="h-1 bg-green-400 rounded-full w-3/4"></div>
                  <div className="h-1 bg-purple-400 rounded-full w-1/2"></div>
                </div>
              </div>
            </div>

            <div className="backdrop-blur-lg bg-white/10 rounded-2xl p-8 shadow-2xl border border-white/20 relative overflow-hidden">
              {/* Animated Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="grid grid-cols-8 grid-rows-8 h-full w-full">
                  {[...Array(64)].map((_, i) => (
                    <div
                      key={i}
                      className="border border-white/20 animate-pulse"
                      style={{ animationDelay: `${Math.random() * 2}s` }}
                    />
                  ))}
                </div>
              </div>

              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
                    <Brain className="h-6 w-6 text-white animate-pulse" />
                  </div>
                  <h3 className="text-2xl font-bold text-white">गोकुळ AI Platform</h3>
                </div>

                <div className="space-y-4 text-blue-100">
                  <div className="flex items-center gap-3">
                    <Database className="h-5 w-5 text-green-400" />
                    <span><strong>Coverage:</strong> Kolhapur District (12 Talukas)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Activity className="h-5 w-5 text-blue-400" />
                    <span><strong>AI Monitoring:</strong> 234K+ Animals</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <TrendingUp className="h-5 w-5 text-purple-400" />
                    <span><strong>Daily Production:</strong> 240K+ Litres</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Bot className="h-5 w-5 text-yellow-400" />
                    <span><strong>AI Accuracy:</strong> 97.8% Prediction Rate</span>
                  </div>
                </div>

                <div className="mt-6 pt-4 border-t border-white/20">
                  <p className="text-sm text-center text-blue-200 font-medium italic">
                    "Pioneering the future of dairy farming with intelligent technology and sustainable practices"
                  </p>
                </div>

                {/* Live Status Indicators */}
                <div className="mt-6 grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-white/5 rounded-lg border border-white/10">
                    <div className="flex items-center justify-center gap-2 mb-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-xs text-green-300 font-medium">ONLINE</span>
                    </div>
                    <div className="text-sm text-white">AI Systems</div>
                  </div>
                  <div className="text-center p-3 bg-white/5 rounded-lg border border-white/10">
                    <div className="flex items-center justify-center gap-2 mb-1">
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                      <span className="text-xs text-blue-300 font-medium">ACTIVE</span>
                    </div>
                    <div className="text-sm text-white">Monitoring</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Floating Elements */}
      <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 left-10 w-40 h-40 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-full blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>

      {/* Animated Circuit Lines */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-20">
        <svg className="w-full h-full" viewBox="0 0 1000 1000">
          <defs>
            <linearGradient id="circuit-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#3B82F6" />
              <stop offset="50%" stopColor="#8B5CF6" />
              <stop offset="100%" stopColor="#06B6D4" />
            </linearGradient>
          </defs>
          <path
            d="M100,100 L200,100 L200,200 L300,200 L300,300 L400,300"
            stroke="url(#circuit-gradient)"
            strokeWidth="2"
            fill="none"
            className="animate-pulse"
          />
          <path
            d="M600,150 L700,150 L700,250 L800,250 L800,350"
            stroke="url(#circuit-gradient)"
            strokeWidth="2"
            fill="none"
            className="animate-pulse"
            style={{ animationDelay: '1s' }}
          />
          <circle cx="200" cy="200" r="4" fill="#3B82F6" className="animate-pulse" />
          <circle cx="700" cy="250" r="4" fill="#8B5CF6" className="animate-pulse" style={{ animationDelay: '0.5s' }} />
        </svg>
      </div>
    </section>
  );
};

export default Hero;
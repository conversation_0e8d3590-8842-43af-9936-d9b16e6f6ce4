import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, Shield, Award } from "lucide-react";
import heroImage from "/hero.jpg";
import cowsField from "/hero2.avif";

const Hero = () => {
  return (
    <section id="home" className="relative min-h-screen flex items-center bg-gradient-to-br from-cream to-dairy-orange-light overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <img 
          src={heroImage} 
          alt="Gokul Dairy Farm" 
          className="w-full h-full object-cover opacity-80"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-cream/90 to-dairy-orange-light/80"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            {/* Badges */}
            <div className="flex flex-wrap gap-3">
              <Badge variant="secondary" className="bg-secondary text-orange-300 px-3 py-1">
                <Shield className="w-3 h-3 mr-1" />
                ISO 9001:2015 Certified
              </Badge>
              <Badge variant="outline" className=" bg-green-300 text-green-700 px-3 py-1">
                <Award className="w-3 h-3 mr-1" />
                17K Litres Daily
              </Badge>
            </div>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-7xl font-bold text-blue-800 leading-tight">
                Pure & Fresh
                <span className="block text-green-800">Dairy Products</span>
              </h1>
              <p className="text-xl text-white bg-gray/10 backdrop-blur-md max-w-lg leading-relaxed p-2 rounded-2xl overflow-hidden">
                Experience the finest quality milk and dairy products from our state-of-the-art facility in Kolhapur, Maharashtra. Farm-fresh goodness delivered to your doorstep.
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 my-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-white">17K+</div>
                <div className="text-sm text-white">Litres Daily</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">98%</div>
                <div className="text-sm text-white">Quality Score</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">5000+</div>
                <div className="text-sm text-white">Happy Customers</div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button variant="outline" size="lg" className="text-lg px-8 py-6">
                Order Fresh Milk
              </Button>
              <Button variant="default" size="lg" className="text-lg px-8 py-6">
                View Products
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center space-x-6 pt-6">
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="w-5 h-5 fill-primary text-primary" />
                ))}
                <span className="ml-2 text-sm text-gray-900">4.9/5 Rating</span>
              </div>
              <div className="text-sm text-gray-900">
                Trusted by 5000+ families
              </div>
            </div>
          </div>

          {/* Right Content - Location Info with Floating Images */}
          <div className="lg:text-right space-y-6 relative">
            {/* Floating Cow Image */}
            <div className="absolute -top-12 -right-8 w-32 h-24 rounded-2xl overflow-hidden shadow-xl border-4 border-white/50 rotate-12 hover:rotate-0 transition-transform duration-500">
              <img 
                src={cowsField} 
                alt="Happy Cows" 
                className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
              />
            </div>
            
            <div className="bg-background/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-border relative overflow-hidden">
              {/* Decorative pattern */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full -translate-y-10 translate-x-10"></div>
              
              <h3 className="text-2xl font-bold text-primary mb-4">गोकुल दूध डेयरी</h3>
              <div className="space-y-3 text-gray-900">
                <p><strong>Location:</strong> Kolhapur, Maharashtra</p>
                <p><strong>Daily Capacity:</strong> 17,000 Litres</p>
                <p><strong>Certification:</strong> ISO 9001:2015</p>
                <p><strong>Established:</strong> Serving quality since years</p>
              </div>
              <div className="mt-6 pt-4 border-t border-border">
                <p className="text-sm text-center text-primary font-medium">
                  "Committed to delivering pure, nutritious dairy products with uncompromising quality standards"
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 right-10 w-20 h-20 bg-primary/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-20 left-10 w-32 h-32 bg-secondary/10 rounded-full blur-xl"></div>
    </section>
  );
};

export default Hero;